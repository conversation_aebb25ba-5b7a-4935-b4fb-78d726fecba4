from get_completion import get_completion_with_gemini, get_completion_with_gemini_25

def get_story_points_with_llm(issue_summary, parent_issue, backlog_subtasks, model):
    """
    Use LLM to calculate the story points of the task
    """

    backlog_story_points = parent_issue.fields.customfield_10013 if parent_issue.fields.customfield_10013 else 0
    backlog_summary= parent_issue.fields.summary;
    backlog_description = parent_issue.fields.description;

    prompt = f"""
    As an agile development expert, please evaluate the story points of the current task according to the following information:

    Summary of current task: {issue_summary}

    Backlog Summary: {backlog_summary}
    
    Backlog Description: {backlog_description}

    Backlog total story points: {backlog_story_points}

    All subtasks of Backlog: {[task['summary'] for task in backlog_subtasks]}
    
    
    Please infer the story points of the current task according to the following rules one by one:
    
    1. If the current backlog item is Ad-Hoc type, give 1 story point (even if it is a task to test or fix bugs), ignore all the following rules.
    
    2. When Backlog total story points is 0, then give this task 0 story points directly, ignore all the following rules.
       
    3. If the current task is a test task, give 1 story point; if it is a large test task, give 2 story points.

    4. If the current task summary contains "[Bug]" and it is to fix bugs generated by another sub task under the same backlog item, give 0 story points.

    5. For other tasks, please infer the proportion of their work in the whole backlog (summary/description) referencing the task summary and all other subtasks, then calculate the corresponding story point.
    Task belongs to Backlog, thus story points of current task should be less than or equal to Backlog total story points.

    Please return only one number to indicate the final story point evaluation result.
    """
    if "2.5" in model.lower():
        response = get_completion_with_gemini_25(prompt, temperature=0)
    else:
        response = get_completion_with_gemini("", prompt, temperature=0)

    try:
        # Notice: response can contain reasoning
        story_points = float(response.strip().split('\n')[0])
        return round(story_points, 2)
    except (ValueError, TypeError):
        # Use traditional method if LLM fails
        return get_story_points(issue_summary, parent_issue, backlog_subtasks)


def get_story_points(issue_summary, parent_issue, backlog_subtasks):
    backlog_story_points = parent_issue.fields.customfield_10013 if parent_issue.fields.customfield_10013 else 0
    # If backlog's story point is 0, then all tasks' story points are 0
    if backlog_story_points == 0:
        return 0

    # Bug story point is 0
    if '[bug]' in issue_summary.lower():
        return 0
    # Test story point is 1
    elif 'test' in issue_summary.lower():
        return 1
    else:
        # filter out test tasks
        backlog_test_tasks = [subtask for subtask in backlog_subtasks if 'test' in subtask['summary'].lower()]
        # calculate story points for non-test and non-bug tasks
        story_points = (backlog_story_points - len(backlog_test_tasks)) / max(1, len(backlog_subtasks) - len(backlog_test_tasks))
        story_points = round(story_points, 2)

    return story_points



