import json
import os
import streamlit as st
from jira import JIRA
from story_points_calculator import get_story_points_with_llm, get_story_points
from get_completion import get_completion_with_gemini

# JIRA server details
jira_url = "https://jira.tools.sap"
jira_username = os.getenv('JIRA_USERNAME')
jira_api_token = os.getenv('JIRA_API_TOKEN')

if not jira_username or not jira_api_token:
    st.error("JIRA_USERNAME and JIRA_API_TOKEN must be set in environment variables")
    st.stop()


def get_all_sprints(jira, board_id):
    all_sprints = []
    start_at = 0
    while True:
        sprints = jira.sprints(board_id, startAt=start_at)
        if not sprints:
            break
        all_sprints.extend(sprints)
        start_at += len(sprints)
    return all_sprints


jira = JIRA(server=jira_url, basic_auth=(jira_username, jira_api_token))
# get all boards with name containing "CALMEXT RC"
boards = jira.boards(name="CALMEXT RC")
boards.extend(jira.boards(name="CALM Build PTM"))
sprints = []
for board in boards:
    sprints.extend(get_all_sprints(jira, board.id))
sprint_names = [sprint.name for sprint in sprints if sprint.name.startswith("CALMEXT_25_RC") or sprint.name.startswith("CALM_B_25_PTM")]


def get_tasks_by_person_and_sprint(sprint_name, person_id, use_llm, model):
    # Get all issues for the given person ID and sprint
    issues = jira.search_issues(
        f'assignee = "{person_id}" AND sprint = "{sprint_name}"',
        fields=['key', 'summary', 'description', 'issuetype', 'status', 'parent']
    )

    # Group issues by backlog item (key and summary)
    backlog_items = {}
    for issue in issues:
        if issue.fields.issuetype.name != 'Task':
            continue
        backlog_key = issue.fields.parent.key if issue.fields.parent else 'No Backlog Item'
        backlog_subtasks = []
        if issue.fields.parent:
            parent_issue = jira.issue(backlog_key)
            backlog_subtasks = [dict(key=subtask.key, summary=subtask.fields.summary) for subtask in parent_issue.fields.subtasks]
            backlog_subtasks = [subtask for subtask in backlog_subtasks if not subtask['summary'].lower().startswith('[bug]')]
        backlog_summary = parent_issue.fields.summary if issue.fields.parent else 'No Backlog Item'
        backlog_description = parent_issue.fields.description if issue.fields.parent else 'No description'
        backlog_status = parent_issue.fields.status.name if issue.fields.parent else 'No status'

        # Use LLM to calculate story points
        if use_llm:
            issue_story_points = get_story_points_with_llm(issue.fields.summary, parent_issue, backlog_subtasks, model)
        else:
            issue_story_points = get_story_points(issue.fields.summary, parent_issue, backlog_subtasks)
        if backlog_key not in backlog_items:
            backlog_items[backlog_key] = dict(
                summary=backlog_summary,
                description=backlog_description,
                status=backlog_status,
                subtasks=backlog_subtasks,
                mytasks=[])
        backlog_items[backlog_key]['mytasks'].append({
            'key': issue.key,
            'summary': issue.fields.summary,
            'description': issue.fields.description,
            'type': issue.fields.issuetype.name,
            'status': issue.fields.status.name,
            'story_points': issue_story_points,
        })
    return backlog_items





def get_jira_summary(sprint_name, backlog_items):
    # Prepare the message for Gemini
    msg_obj = {
        "sprint_name": sprint_name,
        "backlog_items": backlog_items
    }
    msg = json.dumps(msg_obj, indent=4)
    msg += "\n\n"
    # Generate the summary using Gemini
    summary = get_completion_with_gemini(msg)
    return summary


# Streamlit UI
st.title("JIRA Sprint Summary Generator")

# Input form
with st.form("input_form"):
    sprint_name = st.selectbox("Sprint Name", options=sprint_names, index=0)
    person_id = st.text_input("Person ID", placeholder="Enter the person ID")
    use_llm = st.checkbox("Use AI to calculate story points", value=True, help="Enable this option to use LLM to calculate story points, otherwise use the default rules.")
    model = st.selectbox(
        "Model:",
        ("Gemini-1.5-pro", "Gemini-2.5-pro"),
    )
    submitted = st.form_submit_button("Generate Summary")


def get_task_list(sprint_name, person_id):
    # Get all issues for the given person ID and sprint
    issues = jira.search_issues(
        f'assignee = "{person_id}" AND sprint = "{sprint_name}"',
        fields=['key', 'summary', 'description', 'issuetype', 'status', 'parent']
    )

    # Filter only Task type issues
    task_issues = [issue for issue in issues if issue.fields.issuetype.name == 'Task']

    task_list = []
    for idx, issue in enumerate(task_issues, 1):
        backlog_item = ""
        user_story = ""

        # Check if parent exists
        if hasattr(issue.fields, 'parent') and issue.fields.parent:
            try:
                parent_issue = jira.issue(issue.fields.parent.key)
                parent_type = parent_issue.fields.issuetype.name

                if parent_type == 'Backlog Item':
                    # Parent is Backlog Item
                    backlog_item = f"[{parent_issue.fields.summary}]({jira_url}/browse/{parent_issue.key})"

                    # Check if Backlog Item has a parent (User Story)
                    if hasattr(parent_issue.fields, 'parent') and parent_issue.fields.parent:
                        try:
                            grandparent_issue = jira.issue(parent_issue.fields.parent.key)
                            if grandparent_issue.fields.issuetype.name == 'User Story':
                                user_story = f"[{grandparent_issue.fields.summary}]({jira_url}/browse/{grandparent_issue.key})"
                        except Exception as e:
                            print(f"Error getting grandparent for {parent_issue.key}: {e}")

                elif parent_type == 'User Story':
                    # Parent is User Story directly
                    user_story = f"[{parent_issue.fields.summary}]({jira_url}/browse/{parent_issue.key})"

            except Exception as e:
                print(f"Error getting parent for {issue.key}: {e}")

        # Determine progress tracking based on status
        status = issue.fields.status.name
        # Create task link
        task_link = f"[{issue.fields.summary}]({jira_url}/browse/{issue.key})"

        task_data = {
            'No.': idx,
            'Task': task_link,
            'Backlog Item': backlog_item,
            'User Story': user_story,
            'Start Date / Sprint': sprint_name,
            'Close Date / Sprint': sprint_name if status.lower() in ['done', 'closed', 'resolved'] else '',
            'Progress Tracking': status
        }
        task_list.append(task_data)

    # Create markdown table
    if not task_list:
        return "No tasks found for the specified criteria."

    markdown_table = "| No. | Task | Backlog Item | User Story | Start Date / Sprint | Close Date / Sprint | Progress Tracking |\n"
    markdown_table += "|---|---|---|---|---|---|---|\n"

    for task in task_list:
        markdown_table += f"| {task['No.']} | {task['Task']} | {task['Backlog Item']} | {task['User Story']} | {task['Start Date / Sprint']} | {task['Close Date / Sprint']} | {task['Progress Tracking']} |\n"

    return markdown_table


if submitted:
    if not sprint_name or not person_id:
        st.error("Please provide both Sprint Name and Person ID.")
    else:
        with st.spinner("Fetching tasks and generating summary..."):
            backlog_items = get_tasks_by_person_and_sprint(sprint_name, person_id, use_llm=use_llm, model=model)
            task_list = get_task_list(sprint_name, person_id)
            summary = get_jira_summary(sprint_name, backlog_items)

        st.subheader("Summary")
        st.markdown(summary)

        st.subheader("Task Detail")
        st.markdown(task_list)

        st.subheader("Backlog Items")
        st.json(backlog_items)
