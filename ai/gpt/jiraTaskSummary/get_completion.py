import logging
import requests

# Prompt message for Gemini
prompt_msg= """
You are a helpful assistant. You will be provided with a list of jira backlog item, tasks and their details. Your job is to summarize the tasks in a concise manner, focusing on the key points and important information.

Summarize the following sprint tasks information into a table with the columns "Category", "Topic", "Impact", "Start Date / Sprint", "Close Date / Sprint", and "Progress Tracking".


**Example Input:**
{Sprint Name: Sprint 42,
Backlog Items: {json format}}


**Table Format:**

| No. | Category | Topic | Impact | Start Date / Sprint | Close Date / Sprint | Progress Tracking | story points |
|---|---|---|---|---|---|---|---|
| ... | ... | ... | ... | ... | ... | ... | ... |


**Instructions:**

* **Category:** Always use "Project".
* **Impact:** Leave this column empty.
* **Topic:** {summanry of backlog item} - {task details base on backlog item and my tasks}. Topic should concise and clear. Less than 150 characters. 
* **Progress Tracking:** Track the status of incomplete tasks, including a percentage of completion if available. Could also note any highlights or blockers from the tasks, such as specific accomplishments or challenges encountered. 
* **Start Date / Sprint:** Use the provided `sprint name`.
* **Close Date / Sprint:** If the topic is completed/done, use the same value as "Start Date / Sprint". Otherwise, leave this column empty.
* **Story Points:** Sum up my tasks' story points.
* **Output:** Provide the table in markdown format, ensuring proper formatting and alignment. Do not include any additional text or explanations outside of the table. the last row should be the total story points.

* **Important Notes:**
- One backlog item should have one Topic. Concisely and clearly summarize each topic based on each backlog item and my related tasks. 
- If the backlog item has multiple tasks, summarize only my tasks in the Topic column. Be mindful that backlog item may have multiple tasks completed by different individuals, full list can be found in the Backlog Items subtasks, mytasks contains only my tasks. Only summarize my tasks. 
- Do not take any info from backlog item description which is not related to my tasks. 
- If some of my tasks are not completed, note down which task is still in progress in Progress Tracking column, including a percentage of completion if available.
- If task is not completed, do not consider the story points of the task.
- The table should be concise and focused on the key points, avoiding unnecessary details or lengthy explanations.
**Example Output:**
| No. | Category | Topic | Impact | Start Date / Sprint | Close Date / Sprint | Progress Tracking | story points |
|---|---|---|---|---|---|---|---|
| 1 | Project | Test Gen Code |  | Sprint 42 | Sprint 42 | found 8 issues  fix 1 issue | 1 |
| 2 | Project | Implement user authentication |  | Sprint 42 |  | 50% - The login page is done, but the backend API is still in progress. | 2.5 |
|---|---|---|---|---|---|Count|3.5|
"""

def get_API_token() -> str:
    client_id = "sb-a9868d84-4cbb-4e26-8be6-1442d51051b3!b313091|aisvc-662318f9-ies-aicore-service!b540"
    client_secret = "894922a4-4635-4766-b00b-a63c963ae7bc$EsobP8Zk1BTwCBCivtnOopYfkZlTRyyaR7OgGqe4oOQ="

    params = {"grant_type": "client_credentials"}
    resp = requests.post(f"https://sapit-core-playground-vole.authentication.eu10.hana.ondemand.com/oauth/token",
                         auth=(client_id, client_secret),
                         params=params)
    token = resp.json()["access_token"]
    return token


def get_completion_with_gemini(msg, system_msg=prompt_msg, temperature=0):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/db1ee5ec51f3f35c/models/gemini-1.5-pro-002:generateContent"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "contents": [
            {"role": "model", "parts": {"text": system_msg}},
            {"role": "user", "parts": {"text": msg}}
        ],
        "generationConfig": {
            "temperature": temperature,
        }
    }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        summary = response.json()["candidates"][0]["content"]["parts"][0]["text"]
    except:
        logging.error(f"Error in completion: {response.content}")
        logging.error("Failed to generate the summary.")
        summary = ''
    return summary


def get_completion_with_gemini_25(msg, temperature=0):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/dbb95fd34d0032ea/models/gemini-2.5-pro:generateContent"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "contents": [
            {"role": "model", "parts": {"text": msg}},
        ],
        "generationConfig": {
            "temperature": temperature,
        }
    }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        summary = response.json()["candidates"][0]["content"]["parts"][1]["text"]
    except:
        logging.error(f"Error in completion: {response.content}")
        logging.error("Failed to generate the summary.")
        summary = ''
    return summary
