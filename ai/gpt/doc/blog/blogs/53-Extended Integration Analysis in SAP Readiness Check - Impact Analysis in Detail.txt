<!DOCTYPE html>
<html class="no-js" dir="ltr" lang="en" prefix="og: http://ogp.me/ns#">
 <head>
  <title>
   Extended Integration Analysis in SAP Readiness Che... - SAP Community
  </title>
  <script type="text/javascript">
   function DataContainer(){
        var data = {
            "loginStatus": "no",
            "pageName": "Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail",
            "pageTemplate": "BlogArticlePage",
            "country": "glo",
            "productIDs": "73554900100800000266,1647bf27-5e10-4efd-89e1-a59efaf4e250",
            "nodeId": "750",
            "messageId": "13503346",
            "messageTitle": "Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail",
            "messageType": "blog",
            "messageCreateDate": "2021-09-27 08:35:49",
            "user_tags": [ "integration",   "New Features in SAP Readiness Check 2.0",   "SAP Readiness Check",   "sapreadinesscheck_news" ],
            "labels": [ "Product Updates" ],
            "pageId": "1/317/747/750",
        }
        this.getProp = function(name){ return data[name]; }
    }
  </script>
  <script type="text/javascript">
   window.adobeDataLayer=window.adobeDataLayer||[];function CommunityEDDLCore(){this.sendEvent=function(event){window.adobeDataLayer.push(event)}}function CommunityEDDLLoginState(pageData){var hasUserLoggedIn=LITHIUM.CommunityJsonObject.Activity.Results.filter(item=>item.name==='UserSignedOn').length>0;var currentState=pageData.getProp("loginStatus");var prevState=localStorage.getItem("userLoggedIn")||currentState;localStorage.setItem("userLoggedIn",currentState);this.isLogin=function(){return hasUserLoggedIn&&currentState==="yes"};this.isLogout=function(){return prevState!=currentState&&currentState==="no"};this.isAuthenticationEvent=function(){return this.isLogin()||this.isLogout()}}function CommunityEDDLEvents(){var eddl=new CommunityEDDLCore();var pageData=new DataContainer();this.global=function(){eddl.sendEvent({'event':'globalDL','site':{'name':'scn-groups','country':pageData.getProp('country')},'user':{'loginStatus':pageData.getProp('loginStatus')}})};this.pageView=function(){eddl.sendEvent({'event':'pageView','page':{'pageId':pageData.getProp('pageId'),'name':location.pathname.slice(1)||'/','title':pageData.getProp('pageName'),'template':pageData.getProp('pageTemplate'),'section':location.pathname.split('/')[2]||'/','language':'en','country':pageData.getProp('country'),'url':window.location.href,'referrer':document.referrer,'semaphoreID':pageData.getProp('productIDs')},'user':{'loginStatus':pageData.getProp('loginStatus')}})};this.siteContent=function(){if(!pageData.getProp('messageId')){return}eddl.sendEvent({'event':'siteContentView','siteContent':{'contentid':pageData.getProp('messageId'),'type':pageData.getProp('messageType'),'title':pageData.getProp('messageTitle'),'tag':pageData.getProp('user_tags'),'language':'en','createDate':pageData.getProp('messageCreateDate'),'nodeID':pageData.getProp('nodeId'),'label':pageData.getProp('labels')},'user':{'loginStatus':pageData.getProp('loginStatus')}})};this.login=function(){var loginState=new CommunityEDDLLoginState(pageData);if(!loginState.isAuthenticationEvent()){return}eddl.sendEvent({'event':loginState.isLogin()?'userLogin':'userLogout','user':{'loginStatus':pageData.getProp('loginStatus')}})};this.beacon=function(){eddl.sendEvent({'event':'stBeaconReady'})}}function CommunityScenarios(){var events=new CommunityEDDLEvents();this.pageView=function(){events.global();events.login();events.pageView();events.siteContent();events.beacon()};this.kudo=function(){};this.subscription=function(){}}window.addEventListener('load',()=>{if(LITHIUM?.CommunityJsonObject?.Activity?.Results==undefined){console.error("SAP EDDL: Tracking is disabled due to LITHIUM.CommunityJsonObject.Activity.Results is undefined");return}var eddlScenarios=new CommunityScenarios();eddlScenarios.pageView()});
  </script>
  <meta content="Dear All, This is the third blog in the series. Links to the other blogs in the series are provided at the end. In this blog we will describe how the Integration check" name="description"/>
  <meta content="width=device-width, initial-scale=1.0, user-scalable=yes" name="viewport"/>
  <meta content="2023-11-09T07:58:31+01:00" itemprop="dateModified"/>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
  <link href="https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/extended-integration-analysis-in-sap-readiness-check-impact-analysis-in/ba-p/13503346" rel="canonical"/>
  <meta content="https://groups.community.sap.com/html/assets/SAP_R_grad_200x200.png" property="og:image"/>
  <meta content="https://community.sap.com/t5/user/viewprofilepage/user-id/37278" property="article:author"/>
  <meta content="SAP Community" property="og:site_name"/>
  <meta content="article" property="og:type"/>
  <meta content="https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/extended-integration-analysis-in-sap-readiness-check-impact-analysis-in/ba-p/13503346" property="og:url"/>
  <meta content="SAP Readiness Check" property="article:tag"/>
  <meta content="Product Updates" property="article:tag"/>
  <meta content="integration" property="article:tag"/>
  <meta content="New Features in SAP Readiness Check 2.0" property="article:tag"/>
  <meta content="sapreadinesscheck_news" property="article:tag"/>
  <meta content="Enterprise Resource Planning Blogs by SAP" property="article:section"/>
  <meta content="Dear All,  This is the third blog in the series. Links to the other blogs in the series are provided at the end.  In this blog we will describe how the Integration check functions, which data sources are considered, how impact types are defined, and what they mean from a practical perspective.  As w..." property="og:description"/>
  <meta content="2021-09-27T06:35:49.000Z" property="article:published_time"/>
  <meta content="2023-11-09T07:58:31+01:00" property="article:modified_time"/>
  <meta content="Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail" property="og:title"/>
  <link class="lia-link-navigation hidden live-links" href="/khhcw49343/rss/message?board.id=erp-blog-sap&amp;message.id=42647" id="link" rel="alternate" title="article Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail in Enterprise Resource Planning Blogs by SAP" type="application/rss+xml"/>
  <link href="/skins/1907178/30d6a525796f5d5fef22afc1a0238606/sap2023.css" rel="stylesheet" type="text/css"/>
  <link href="https://community.sap.com/html/@28E64AF715869863C9097AAA9C123156/assets/favicon.ico" rel="shortcut icon"/>
  <script src="https://assets.adobedtm.com/ccc66c06b30b/2a75032df81e/launch-dfaf5c383cc4.min.js">
  </script>
  <link href="https://community.sap.com/html/@0695D6660F84CABE78A4151F0A127FA9/assets/prism-kh.css" rel="stylesheet" type="text/css"/>
  <script src="https://community.sap.com/html/@2778250842E3D1984686289CD77E55B9/assets/prism-kh.js" type="text/javascript">
  </script>
  <link href="https://contextualnavigation.api.community.sap.com/static/1.31.0/cxs-designsystem/cxs-designsystem.css" rel="stylesheet"/>
  <script src="https://contextualnavigation.api.community.sap.com/static/1.31.0/cxs-designsystem/cxs-designsystem.esm.js" type="module">
  </script>
  <script language="javascript" type="text/javascript">
   <!--
if("undefined"==typeof LITHIUM)var LITHIUM={};LITHIUM.Loader=function(){var d=[],b=[],a=!1,c=!1;return{onLoad:function(b){"function"===typeof b&&(!0===a?b():d.push(b))},onJsAttached:function(a){"function"===typeof a&&(!0===c?a():b.push(a))},runJsAttached:function(){c=!0;for(var a=0;a<b.length;a++)b[a]()},getOnLoadFunctions:function(){return d},setLoaded:function(){a=!0},isLoaded:function(){return a},isJsAttached:function(){return c}}}();"undefined"===typeof LITHIUM.Components&&(LITHIUM.Components={});LITHIUM.Components.render=function(d,b,a){LITHIUM.Loader.onLoad(function(){var c=LITHIUM.Components.renderUrl(d),h={type:"GET",dataType:"json"};LITHIUM.jQuery.extend(h,a||{});h.hasOwnProperty("url")||LITHIUM.jQuery.extend(h,{url:c});h.data=b;if("object"!==typeof h.data||null===h.data)h.data={};h.data.originalPageName=LITHIUM.Components.ORIGINAL_PAGE_NAME;h.data.originalPageContext=LITHIUM.Components.ORIGINAL_PAGE_CONTEXT;LITHIUM.jQuery.ajax(h)}.bind(this))};
LITHIUM.Components.renderUrl=function(d,b){var a=LITHIUM.Components.RENDER_URL;LITHIUM.jQuery.each({"component-id":d},function(b,d){a=a.replace(new RegExp("#{"+b+"}","g"),d)});"undefined"!==typeof b&&(a+="?"+LITHIUM.jQuery.param(b));return a};
LITHIUM.Components.renderInPlace=function(d,b,a,c){function h(a){var b=document.createElement("div"),d=(new Date).getTime()+Math.floor(1E7*Math.random()+1);b.setAttribute("id",d);a.parentNode.insertBefore(b,a);return d}if(c)var q=c;else!1===LITHIUM.Loader.isLoaded()&&(document.currentScript?q=h(document.currentScript):(c=document.querySelectorAll("script"),1<c.length&&(q=h(c[c.length-1]))));LITHIUM.Loader.onLoad(function(){var c=LITHIUM.jQuery,h=b||{},m=a||{},k=c("#"+q);c.extend(h,{renderedScripts:LITHIUM.RenderedScripts.toString(),
"component-id":d});c.extend(m,{success:function(a){var b=a.content;LITHIUM.AngularSupport.isAngularEnabled()&&(b=LITHIUM.AngularSupport.compile(b));k.replaceWith(b);LITHIUM.AjaxSupport.ScriptsProcessor.handleScriptEvaluation(a);(a=LITHIUM.jQuery(b).attr("id"))&&LITHIUM.jQuery("#"+a).trigger("LITHIUM:ajaxSuccess:renderInPlace",{componentId:d})},error:function(b,c,d){0===b.readyState||0===b.status?k.html(""):k.html('\x3cspan class\x3d"lia-ajax-error-text"\x3e'+a.errorMessage+"\x3c/span\x3e");k.removeClass(LITHIUM.Css.BASE_LAZY_LOAD).removeClass("lia-fa-spin")}});
k&&LITHIUM.Components.render(d,h,m)}.bind(this))};/*
 modernizr v3.3.1
 Build https://modernizr.com/download?-exiforientation-filereader-flash-setclasses-dontmin

 Copyright (c)
  Faruk Ates
  Paul Irish
  Alex Sexton
  Ryan Seddon
  Patrick Kettner
  Stu Cox
  Richard Herrera

 MIT License
 {
      "name": "EXIF Orientation",
      "property": "exiforientation",
      "tags": ["image"],
      "builderAliases": ["exif_orientation"],
      "async": true,
      "authors": ["Paul Sayre"],
      "notes": [{
        "name": "Article by Dave Perrett",
        "href": "http://recursive-design.com/blog/2012/07/28/exif-orientation-handling-is-a-ghetto/"
      },{
        "name": "Article by Calvin Hass",
        "href": "http://www.impulseadventure.com/photo/exif-orientation.html"
      }]
    }
    ! {
      "name": "Flash",
      "property": "flash",
      "tags": ["flash"],
      "polyfills": ["shumway"]
      }
      ! {
      "name": "File API",
      "property": "filereader",
      "caniuse": "fileapi",
      "notes": [{
        "name": "W3C Working Draft",
        "href": "https://www.w3.org/TR/FileAPI/"
      }],
      "tags": ["file"],
      "builderAliases": ["file_api"],
      "knownBugs": ["Will fail in Safari 5 due to its lack of support for the standards defined FileReader object"]
    }
    !*/
LITHIUM.LiModernizr=function(){(function(d,b,a){function c(g){var a=n.className,b=e._config.classPrefix||"";r&&(a=a.baseVal);e._config.enableJSClass&&(a=a.replace(new RegExp("(^|\\s)"+b+"no-js(\\s|$)"),"$1"+b+"js$2"));e._config.enableClasses&&(a+=" "+b+g.join(" "+b),r?n.className.baseVal=a:n.className=a)}function h(){return"function"!==typeof b.createElement?b.createElement(arguments[0]):r?b.createElementNS.call(b,"http://www.w3.org/2000/svg",arguments[0]):b.createElement.apply(b,arguments)}function q(){var a=
b.body;a||(a=h(r?"svg":"body"),a.fake=!0);return a}function p(a,b){if("object"==typeof a)for(var g in a)u(a,g)&&p(g,a[g]);else{a=a.toLowerCase();g=a.split(".");var l=e[g[0]];2==g.length&&(l=l[g[1]]);if("undefined"!=typeof l)return e;b="function"==typeof b?b():b;1==g.length?e[g[0]]=b:(!e[g[0]]||e[g[0]]instanceof Boolean||(e[g[0]]=new Boolean(e[g[0]])),e[g[0]][g[1]]=b);c([(b&&0!=b?"":"no-")+g.join("-")]);e._trigger(a,b)}return e}var t=[],m=[],k={_version:"3.3.1",_config:{classPrefix:"",enableClasses:!0,
enableJSClass:!0,usePrefixes:!0},_q:[],on:function(a,b){var g=this;setTimeout(function(){b(g[a])},0)},addTest:function(a,b,c){m.push({name:a,fn:b,options:c})},addAsyncTest:function(a){m.push({name:null,fn:a})}},e=function(){};e.prototype=k;e=new e;var n=b.documentElement,r="svg"===n.nodeName.toLowerCase(),u;(function(){var a={}.hasOwnProperty;u="undefined"!==typeof a&&"undefined"!==typeof a.call?function(b,g){return a.call(b,g)}:function(a,b){return b in a&&"undefined"===typeof a.constructor.prototype[b]}})();
k._l={};k.on=function(a,b){this._l[a]||(this._l[a]=[]);this._l[a].push(b);e.hasOwnProperty(a)&&setTimeout(function(){e._trigger(a,e[a])},0)};k._trigger=function(a,b){if(this._l[a]){var g=this._l[a];setTimeout(function(){var a;for(a=0;a<g.length;a++){var c=g[a];c(b)}},0);delete this._l[a]}};e._q.push(function(){k.addTest=p});e.addAsyncTest(function(){LITHIUM.Loader.onLoad(function(){var a=b.createElement("img");a.onerror=function(){p("exiforientation",!1,{aliases:["exif-orientation"]});b.body.removeChild(a)};
a.onload=function(){p("exiforientation",2!==a.width,{aliases:["exif-orientation"]});b.body.removeChild(a)};a.src="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/4QAiRXhpZgAASUkqAAgAAAABABIBAwABAAAABgASAAAAAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAIDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD+/iiiigD/2Q\x3d\x3d";
b.body&&(a.setAttribute("style","position: absolute; left: -9999;"),b.body.appendChild(a))})});e.addAsyncTest(function(){var a=function(a){n.contains(a)||n.appendChild(a)},c=function(a,b){var c=!!a;c&&(c=new Boolean(c),c.blocked="blocked"===a);p("flash",function(){return c});if(b&&f.contains(b)){for(;b.parentNode!==f;)b=b.parentNode;f.removeChild(b)}};try{var e="ActiveXObject"in d&&"Pan"in new d.ActiveXObject("ShockwaveFlash.ShockwaveFlash")}catch(v){}if(!("plugins"in navigator&&"Shockwave Flash"in
navigator.plugins||e)||r)c(!1);else{var l=h("embed"),f=q(),k;l.type="application/x-shockwave-flash";f.appendChild(l);if("Pan"in l||e){var m=function(){a(f);if(!n.contains(f))return f=b.body||f,l=h("embed"),l.type="application/x-shockwave-flash",f.appendChild(l),setTimeout(m,1E3);n.contains(l)?(k=l.style.cssText,""!==k?c("blocked",l):c(!0,l)):c("blocked");f.fake&&f.parentNode&&f.parentNode.removeChild(f)};setTimeout(m,10)}else a(f),c("blocked",l),f.fake&&f.parentNode&&f.parentNode.removeChild(f)}});
e.addTest("filereader",!!(d.File&&d.FileList&&d.FileReader));(function(){var a,b;for(b in m)if(m.hasOwnProperty(b)){var c=[];var d=m[b];if(d.name&&(c.push(d.name.toLowerCase()),d.options&&d.options.aliases&&d.options.aliases.length))for(a=0;a<d.options.aliases.length;a++)c.push(d.options.aliases[a].toLowerCase());d="function"===typeof d.fn?d.fn():d.fn;for(a=0;a<c.length;a++){var f=c[a];f=f.split(".");1===f.length?e[f[0]]=d:(!e[f[0]]||e[f[0]]instanceof Boolean||(e[f[0]]=new Boolean(e[f[0]])),e[f[0]][f[1]]=
d);t.push((d?"":"no-")+f.join("-"))}}})();c(t);delete k.addTest;delete k.addAsyncTest;for(a=0;a<e._q.length;a++)e._q[a]();LITHIUM.Modernizr=e})(window,document)}();(function(){LITHIUM.Globals=function(){var d={};return{preventGlobals:function(b){for(var a=0;a<b.length;a++){var c=b[a];c in window&&void 0!==window[c]&&(d[c]=window[c],window[c]=void 0)}},restoreGlobals:function(b){for(var a=0;a<b.length;a++){var c=b[a];d.hasOwnProperty(c)&&(window[c]=d[c])}}}}()})();(function(){LITHIUM.EarlyEventCapture=function(d,b,a){if(void 0===LITHIUM.jQuery||!0!==LITHIUM.jQuery.isReady||!LITHIUM.Loader.isJsAttached()){var c=d.getAttribute("data-lia-early-event-captured");if(!0===a&&!0!==c||!0===a)d.setAttribute("data-lia-early-event-captured",!0),LITHIUM.Loader.onJsAttached(function(){var a=LITHIUM.jQuery;a(function(){a(d).trigger(b)})});return!1}return!0}})();(function(d){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);Element.prototype.closest||(Element.prototype.closest=function(b){var a=this;do{if(a.matches(b))return a;a=a.parentElement||a.parentNode}while(null!==a&&1===a.nodeType);return null})})(LITHIUM.jQuery);window.FileAPI = { jsPath: '/html/assets/js/vendor/ng-file-upload-shim/' };
LITHIUM.PrefetchData = {"Components":{},"commonResults":{}};
LITHIUM.DEBUG = false;
LITHIUM.CommunityJsonObject = {
  "Validation" : {
    "image.description" : {
      "min" : 0,
      "max" : 1000,
      "isoneof" : [ ],
      "type" : "string"
    },
    "tkb.toc_maximum_heading_level" : {
      "min" : 1,
      "max" : 6,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "tkb.toc_heading_list_style" : {
      "min" : 0,
      "max" : 50,
      "isoneof" : [
        "disc",
        "circle",
        "square",
        "none"
      ],
      "type" : "string"
    },
    "blog.toc_maximum_heading_level" : {
      "min" : 1,
      "max" : 6,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "tkb.toc_heading_indent" : {
      "min" : 5,
      "max" : 50,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "blog.toc_heading_indent" : {
      "min" : 5,
      "max" : 50,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "blog.toc_heading_list_style" : {
      "min" : 0,
      "max" : 50,
      "isoneof" : [
        "disc",
        "circle",
        "square",
        "none"
      ],
      "type" : "string"
    }
  },
  "User" : {
    "settings" : {
      "imageupload.legal_file_extensions" : "*.jpg;*.JPG;*.jpeg;*.JPEG;*.gif;*.GIF;*.png;*.PNG;*.webm;*.svg;*.SVG",
      "config.enable_avatar" : true,
      "integratedprofile.show_klout_score" : true,
      "layout.sort_view_by_last_post_date" : true,
      "layout.friendly_dates_enabled" : true,
      "profileplus.allow.anonymous.scorebox" : false,
      "tkb.message_sort_default" : "topicPublishDate",
      "layout.format_pattern_date" : "MM-dd-yyyy",
      "config.require_search_before_post" : "off",
      "isUserLinked" : false,
      "integratedprofile.cta_add_topics_dismissal_timestamp" : -1,
      "layout.message_body_image_max_size" : 1000,
      "profileplus.everyone" : false,
      "integratedprofile.cta_connect_wide_dismissal_timestamp" : -1,
      "blog.toc_maximum_heading_level" : "",
      "integratedprofile.hide_social_networks" : false,
      "blog.toc_heading_indent" : "",
      "contest.entries_per_page_num" : 20,
      "layout.messages_per_page_linear" : 15,
      "integratedprofile.cta_manage_topics_dismissal_timestamp" : -1,
      "profile.shared_profile_test_group" : false,
      "integratedprofile.cta_personalized_feed_dismissal_timestamp" : -1,
      "integratedprofile.curated_feed_size" : 10,
      "contest.one_kudo_per_contest" : false,
      "integratedprofile.enable_social_networks" : false,
      "integratedprofile.my_interests_dismissal_timestamp" : -1,
      "profile.language" : "en",
      "layout.friendly_dates_max_age_days" : 31,
      "layout.threading_order" : "thread_ascending",
      "blog.toc_heading_list_style" : "disc",
      "useRecService" : false,
      "layout.module_welcome" : "<h2>Welcome to SAP Community<\/h2>\r\n<p>Connect and engage with our community to get answers, discuss best practices, \r\nand continually learn more about SAP solutions.<\/p>",
      "imageupload.max_uploaded_images_per_upload" : 100,
      "imageupload.max_uploaded_images_per_user" : 1000,
      "integratedprofile.connect_mode" : "",
      "tkb.toc_maximum_heading_level" : "",
      "tkb.toc_heading_list_style" : "disc",
      "sharedprofile.show_hovercard_score" : true,
      "config.search_before_post_scope" : "community",
      "tkb.toc_heading_indent" : "",
      "p13n.cta.recommendations_feed_dismissal_timestamp" : -1,
      "imageupload.max_file_size" : 9216,
      "layout.show_batch_checkboxes" : false,
      "integratedprofile.cta_connect_slim_dismissal_timestamp" : -1
    },
    "isAnonymous" : true,
    "policies" : {
      "image-upload.process-and-remove-exif-metadata" : false
    },
    "registered" : false,
    "emailRef" : "",
    "id" : -1,
    "login" : "Former Member"
  },
  "Server" : {
    "communityPrefix" : "/khhcw49343",
    "nodeChangeTimeStamp" : 1707286177369,
    "tapestryPrefix" : "/t5",
    "deviceMode" : "DESKTOP",
    "responsiveDeviceMode" : "DESKTOP",
    "membershipChangeTimeStamp" : "0",
    "version" : "23.12",
    "branch" : "23.12-release",
    "showTextKeys" : false
  },
  "Config" : {
    "phase" : "prod",
    "integratedprofile.cta.reprompt.delay" : 30,
    "profileplus.tracking" : {
      "profileplus.tracking.enable" : false,
      "profileplus.tracking.click.enable" : false,
      "profileplus.tracking.impression.enable" : false
    },
    "app.revision" : "2402020916-s44cf0fb5af-b79",
    "navigation.manager.community.structure.limit" : "1000"
  },
  "Activity" : {
    "Results" : [ ]
  },
  "NodeContainer" : {
    "viewHref" : "https://community.sap.com/t5/enterprise-resource-planning/ct-p/erp",
    "description" : "Get support, share your expertise, and engage with the community about SAP S/4HANA, SAP S/4HANA Cloud, and other enterprise resource planning (ERP) software.",
    "id" : "erp",
    "shortTitle" : "Enterprise Resource Planning",
    "title" : "Enterprise Resource Planning",
    "nodeType" : "category"
  },
  "Page" : {
    "skins" : [
      "sap2023",
      "theme_hermes",
      "responsive_peak"
    ],
    "authUrls" : {
      "loginUrl" : "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fextended-integration-analysis-in-sap-readiness-check-impact-analysis-in%2Fba-p%2F13503346",
      "loginUrlNotRegistered" : "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?redirectreason=notregistered&referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fextended-integration-analysis-in-sap-readiness-check-impact-analysis-in%2Fba-p%2F13503346",
      "loginUrlNotRegisteredDestTpl" : "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?redirectreason=notregistered&referer=%7B%7BdestUrl%7D%7D"
    },
    "name" : "BlogArticlePage",
    "rtl" : false,
    "object" : {
      "viewHref" : "/t5/enterprise-resource-planning-blogs-by-sap/extended-integration-analysis-in-sap-readiness-check-impact-analysis-in/ba-p/13503346",
      "subject" : "Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail",
      "id" : 13503346,
      "page" : "BlogArticlePage",
      "type" : "Thread"
    }
  },
  "WebTracking" : {
    "Activities" : { },
    "path" : "Community:SAP Community/Category:Products and Technology/Category:Enterprise Resource Planning/Blog:ERP Blogs by SAP/Article:Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail"
  },
  "Feedback" : {
    "targeted" : { }
  },
  "Seo" : {
    "markerEscaping" : {
      "pathElement" : {
        "prefix" : "@",
        "match" : "^[0-9][0-9]$"
      },
      "enabled" : false
    }
  },
  "TopLevelNode" : {
    "viewHref" : "https://community.sap.com/",
    "description" : "",
    "id" : "khhcw49343",
    "shortTitle" : "SAP Community",
    "title" : "SAP Community",
    "nodeType" : "Community"
  },
  "Community" : {
    "viewHref" : "https://community.sap.com/",
    "integratedprofile.lang_code" : "en",
    "integratedprofile.country_code" : "US",
    "id" : "khhcw49343",
    "shortTitle" : "SAP Community",
    "title" : "SAP Community"
  },
  "CoreNode" : {
    "conversationStyle" : "blog",
    "viewHref" : "https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap",
    "settings" : { },
    "description" : "Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.",
    "id" : "erp-blog-sap",
    "shortTitle" : "ERP Blogs by SAP",
    "title" : "Enterprise Resource Planning Blogs by SAP",
    "nodeType" : "Board",
    "ancestors" : [
      {
        "viewHref" : "https://community.sap.com/t5/enterprise-resource-planning/ct-p/erp",
        "description" : "Get support, share your expertise, and engage with the community about SAP S/4HANA, SAP S/4HANA Cloud, and other enterprise resource planning (ERP) software.",
        "id" : "erp",
        "shortTitle" : "Enterprise Resource Planning",
        "title" : "Enterprise Resource Planning",
        "nodeType" : "category"
      },
      {
        "viewHref" : "https://community.sap.com/t5/products-and-technology/ct-p/products",
        "description" : "",
        "id" : "products",
        "shortTitle" : "Products and Technology",
        "title" : "Products and Technology",
        "nodeType" : "category"
      },
      {
        "viewHref" : "https://community.sap.com/",
        "description" : "",
        "id" : "khhcw49343",
        "shortTitle" : "SAP Community",
        "title" : "SAP Community",
        "nodeType" : "Community"
      }
    ]
  }
};
LITHIUM.Components.RENDER_URL = "/t5/util/componentrenderpage/component-id/#{component-id}?render_behavior=raw";
LITHIUM.Components.ORIGINAL_PAGE_NAME = 'blogs/v2/BlogArticlePage';
LITHIUM.Components.ORIGINAL_PAGE_ID = 'BlogArticlePage';
LITHIUM.Components.ORIGINAL_PAGE_CONTEXT = 'WTBuvFoSJPpYyWG6QFEFf-lpyYXeBJqhXoxDfgo7YoclbHOGiOutLOnAXJqhFTrAdsJZ351aoQQLSfjzNqfOww-EePT3l9j_id0OLj8TZrydGEeaEfKe00U3WEEXzP0zvd7AKhEor86daSE4VP7c32D8OxQqd1r-Tu9ef1HMd-8WNOFzUvdNnM7WdyBYSOSvKayrbP6dHhpEOmd6gd8myroiM8c03vleWPAJLTmpZm-uBQo2MxxsUnEK7vCn7dXvX5sxpnaj_hzbiut9WdDL-aFafI58Wx85mUP2yW2BDbup6z4GDXnllAEycqJhHmFSL9_c7sI0fEm7OpPxTsEsYlWV6feSXDMcgQNC_JoPlts.';
LITHIUM.Css = {
  "BASE_DEFERRED_IMAGE" : "lia-deferred-image",
  "BASE_BUTTON" : "lia-button",
  "BASE_SPOILER_CONTAINER" : "lia-spoiler-container",
  "BASE_TABS_INACTIVE" : "lia-tabs-inactive",
  "BASE_TABS_ACTIVE" : "lia-tabs-active",
  "BASE_AJAX_REMOVE_HIGHLIGHT" : "lia-ajax-remove-highlight",
  "BASE_FEEDBACK_SCROLL_TO" : "lia-feedback-scroll-to",
  "BASE_FORM_FIELD_VALIDATING" : "lia-form-field-validating",
  "BASE_FORM_ERROR_TEXT" : "lia-form-error-text",
  "BASE_FEEDBACK_INLINE_ALERT" : "lia-panel-feedback-inline-alert",
  "BASE_BUTTON_OVERLAY" : "lia-button-overlay",
  "BASE_TABS_STANDARD" : "lia-tabs-standard",
  "BASE_AJAX_INDETERMINATE_LOADER_BAR" : "lia-ajax-indeterminate-loader-bar",
  "BASE_AJAX_SUCCESS_HIGHLIGHT" : "lia-ajax-success-highlight",
  "BASE_CONTENT" : "lia-content",
  "BASE_JS_HIDDEN" : "lia-js-hidden",
  "BASE_AJAX_LOADER_CONTENT_OVERLAY" : "lia-ajax-loader-content-overlay",
  "BASE_FORM_FIELD_SUCCESS" : "lia-form-field-success",
  "BASE_FORM_WARNING_TEXT" : "lia-form-warning-text",
  "BASE_FORM_FIELDSET_CONTENT_WRAPPER" : "lia-form-fieldset-content-wrapper",
  "BASE_AJAX_LOADER_OVERLAY_TYPE" : "lia-ajax-overlay-loader",
  "BASE_FORM_FIELD_ERROR" : "lia-form-field-error",
  "BASE_SPOILER_CONTENT" : "lia-spoiler-content",
  "BASE_FORM_SUBMITTING" : "lia-form-submitting",
  "BASE_EFFECT_HIGHLIGHT_START" : "lia-effect-highlight-start",
  "BASE_FORM_FIELD_ERROR_NO_FOCUS" : "lia-form-field-error-no-focus",
  "BASE_EFFECT_HIGHLIGHT_END" : "lia-effect-highlight-end",
  "BASE_SPOILER_LINK" : "lia-spoiler-link",
  "BASE_DISABLED" : "lia-link-disabled",
  "FACEBOOK_LOGOUT" : "lia-component-users-action-logout",
  "FACEBOOK_SWITCH_USER" : "lia-component-admin-action-switch-user",
  "BASE_FORM_FIELD_WARNING" : "lia-form-field-warning",
  "BASE_AJAX_LOADER_FEEDBACK" : "lia-ajax-loader-feedback",
  "BASE_AJAX_LOADER_OVERLAY" : "lia-ajax-loader-overlay",
  "BASE_LAZY_LOAD" : "lia-lazy-load"
};
LITHIUM.noConflict = true;
LITHIUM.useCheckOnline = false;
LITHIUM.RenderedScripts = [
  "jquery.ajax-cache-response-1.0.js",
  "jquery.ui.resizable.js",
  "Lithium.js",
  "jquery.viewport-1.0.js",
  "LiModernizr.js",
  "EarlyEventCapture.js",
  "jquery.json-2.6.0.js",
  "ElementMethods.js",
  "jquery.tools.tooltip-1.2.6.js",
  "DeferredImages.js",
  "Auth.js",
  "Link.js",
  "jquery.effects.slide.js",
  "Loader.js",
  "jquery.js",
  "jquery.fileupload.js",
  "NoConflict.js",
  "Cache.js",
  "SpoilerToggle.js",
  "Sandbox.js",
  "jquery.position-toggle-1.0.js",
  "ResizeSensor.js",
  "AutoComplete.js",
  "jquery.ui.mouse.js",
  "DataHandler.js",
  "jquery.iframe-shim-1.0.js",
  "jquery.iframe-transport.js",
  "Events.js",
  "Placeholder.js",
  "jquery.effects.core.js",
  "jquery.placeholder-2.0.7.js",
  "jquery.ui.dialog.js",
  "jquery.blockui.js",
  "jquery.ui.draggable.js",
  "AjaxSupport.js",
  "jquery.function-utils-1.0.js",
  "jquery.ui.position.js",
  "Throttle.js",
  "ActiveCast3.js",
  "jquery.ui.core.js",
  "PolyfillsAll.js",
  "Components.js",
  "Forms.js",
  "UserListActual.js",
  "MessageBodyDisplay.js",
  "AjaxFeedback.js",
  "HelpIcon.js",
  "DropDownMenu.js",
  "jquery.ui.widget.js",
  "prism.js",
  "SearchForm.js",
  "jquery.tmpl-1.1.1.js",
  "DropDownMenuVisibilityHandler.js",
  "ForceLithiumJQuery.js",
  "jquery.clone-position-1.0.js",
  "jquery.appear-1.1.1.js",
  "CustomEvent.js",
  "PartialRenderProxy.js",
  "Globals.js",
  "jquery.autocomplete.js",
  "jquery.scrollTo.js",
  "jquery.lithium-selector-extensions.js",
  "jquery.css-data-1.0.js",
  "Namespace.js",
  "Video.js",
  "jquery.delayToggle-1.0.js",
  "Text.js",
  "InformationBox.js",
  "Tooltip.js",
  "json2.js",
  "ElementQueries.js",
  "SearchAutoCompleteToggle.js",
  "jquery.hoverIntent-r6.js"
];(function(){LITHIUM.AngularSupport=function(){function g(a,c){a=a||{};for(var b in c)"[object object]"===Object.prototype.toString.call(c[b])?a[b]=g(a[b],c[b]):a[b]=c[b];return a}var d,f,b={coreModule:"li.community",coreModuleDeps:[],noConflict:!0,bootstrapElementSelector:".lia-page .min-width .lia-content",bootstrapApp:!0,debugEnabled:!1,useCsp:!0,useNg2:!1},k=function(){var a;return function(b){a||(a=document.createElement("a"));a.href=b;return a.href}}();LITHIUM.Angular={};return{preventGlobals:LITHIUM.Globals.preventGlobals,
restoreGlobals:LITHIUM.Globals.restoreGlobals,init:function(){var a=[],c=document.querySelector(b.bootstrapElementSelector);a.push(b.coreModule);b.customerModules&&0<b.customerModules.length&&a.concat(b.customerModules);b.useCsp&&(c.setAttribute("ng-csp","no-unsafe-eval"),c.setAttribute("li-common-non-bindable",""));d=LITHIUM.angular.module(b.coreModule,b.coreModuleDeps);d.config(["$locationProvider","$provide","$injector","$logProvider","$compileProvider","$qProvider","$anchorScrollProvider",function(a,
c,e,d,f,g,h){h.disableAutoScrolling();h=document.createElement("base");h.setAttribute("href",k(location));document.getElementsByTagName("head")[0].appendChild(h);window.history&&window.history.pushState&&a.html5Mode({enabled:!0,requireBase:!0,rewriteLinks:!1}).hashPrefix("!");d.debugEnabled(b.debugEnabled);f.debugInfoEnabled(b.debugEnabled);e.has("$uibModal")&&c.decorator("$uibModal",["$delegate",function(a){var b=a.open;a.open=function(a){a.backdropClass=(a.backdropClass?a.backdropClass+" ":"")+
"lia-modal-backdrop";a.windowClass=(a.windowClass?a.windowClass+" ":"")+"lia-modal-window";return b(a)};return a}]);e.has("uibDropdownConfig")&&(e.get("uibDropdownConfig").openClass="lia-dropdown-open");e.has("uibButtonConfig")&&(e.get("uibButtonConfig").activeClass="lia-link-active");g.errorOnUnhandledRejections(!1)}]);if(b.bootstrapApp)f=b.useNg2?LITHIUM.Angular.upgradeAdapter.bootstrap(c,a):LITHIUM.angular.bootstrap(c,a);else LITHIUM.Loader.onLoad(function(){f=LITHIUM.angular.element(c).injector()});
LITHIUM.Angular.app=d},compile:function(a){void 0===a&&(a=document.querySelector(b.bootstrapElementSelector));var c;if(void 0===a||""===a)return a;f.invoke(["$rootScope","$compile",function(b,d){try{var e=LITHIUM.angular.element(a)}catch(l){e=LITHIUM.angular.element("\x3cli:safe-wrapper\x3e"+a+"\x3c/li:safe-wrapper\x3e")}e.attr("li-common-non-bindable","");c=d(e)(b);b.$digest()}]);return c},isAngularEnabled:function(){return void 0!==d},updateLocationUrl:function(a,b){f.invoke(["$location","$rootScope",
"$browser",function(c,d,e){a=""===a?"?":a;c.url(a,b);d.$apply()}])},setOptions:function(a){return g(b,a)},getOptions:function(){return b},initGlobal:function(a){LITHIUM.angular=a;b.useNg2&&(LITHIUM.Angular.upgradeAdapter=new ng.upgrade.UpgradeAdapter)}}}()})();(function(){LITHIUM.ScriptLoader=function(){function d(a){a in e||(e[a]={loaded:!1});return e[a]}function f(){g.filter(function(a){return!a.loaded}).forEach(function(a){var b=!0;a.labels.forEach(function(a){!1===d(a).loaded&&(b=!1)});b&&(a.loaded=!0,a.callback())})}var e={},g=[];return{load:function(a){a.forEach(function(a){var b=document.getElementsByTagName("head")[0]||document.documentElement,c=document.createElement("script");c.src=a.url;c.async=!1;a.crossorigin&&a.integrity&&(c.setAttribute("crossorigin",
a.crossorigin),c.setAttribute("integrity",a.integrity));b.insertBefore(c,b.firstChild);d(a.label)})},setLoaded:function(a){d(a).loaded=!0;f()},ready:function(a,b){g.push({labels:a,callback:b,loaded:!1});f()}}}()})();LITHIUM.ScriptLoader.load([{"name":"lia-scripts-common-min.js","label":"common","url":"/t5/scripts/AEDAE865B0CFD50743A695EDB8D1125C/lia-scripts-common-min.js"},{"name":"lia-scripts-body-min.js","label":"body","url":"/t5/scripts/795AC2DDAACCCFDDF501127C8CBD2004/lia-scripts-body-min.js"},{"name":"lia-scripts-angularjs-min.js","label":"angularjs","url":"/t5/scripts/B2E81B1ECCE0975FEC768641B9390130/lia-scripts-angularjs-min.js"},{"name":"lia-scripts-angularjsModules-min.js","label":"angularjsModules","url":"/t5/scripts/951858BAD326D0669C2BAF489130C8F0/lia-scripts-angularjsModules-min.js"}]);
// -->
  </script>
 </head>
 <body class="lia-blog lia-user-status-anonymous BlogArticlePage lia-body lia-a11y" id="lia-body">
  <div class="ServiceNodeInfoHeader" id="2AD-118-1">
  </div>
  <div class="lia-page">
   <center>
    <div style="height: 64px; background: black">
     <ds-header identifier="community" locale="en-us" s3-bucket="https://contextualnavigation.api.community.sap.com">
     </ds-header>
    </div>
    <script type="module">
     // Listen to header custom events
    document.addEventListener('login', () => {
        window.location.href = "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fextended-integration-analysis-in-sap-readiness-check-impact-analysis-in%2Fba-p%2F13503346";
    });
    document.addEventListener('logout', () => {
        window.location.href = "https://community.sap.com/t5/community/page.logoutpage?t:cp=authentication/contributions/unticketedauthenticationactions&dest_url=https%3A%2F%2Fcommunity.sap.com%2F&lia-action-token=PQXpXucwr4JgFXzyUisYD-11cY_MWE8IbArMfxxWRrI.&lia-action-token-id=logoff";
    });
    </script>
    <div class="MinimumWidthContainer">
     <div class="min-width-wrapper">
      <div class="min-width">
       <div class="lia-content">
        <div class="lia-quilt lia-quilt-blog-article-page lia-quilt-layout-two-column-main-side lia-top-quilt">
         <div class="lia-quilt-row lia-quilt-row-header">
          <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-common-header">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
            <div class="lia-quilt lia-quilt-header lia-quilt-layout-custom-community-header lia-component-quilt-header">
             <div class="lia-quilt-row lia-quilt-row-header-top">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-top-content lia-mark-empty">
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-navigation">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-navigation-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                <div class="custom-community-header-left">
                 <div aria-label="breadcrumbs" class="BreadCrumb crumb-line lia-breadcrumb lia-component-common-widget-breadcrumb" role="navigation">
                  <ul class="lia-list-standard-inline" id="list" role="list">
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-community lia-breadcrumb-community lia-breadcrumb-forum" href="/" id="link_0">
                     SAP Community
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-community lia-breadcrumb-community lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-category lia-breadcrumb-category lia-breadcrumb-forum" href="/t5/products-and-technology/ct-p/products" id="link_1">
                     Products and Technology
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-category lia-breadcrumb-category lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display_0" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-category lia-breadcrumb-category lia-breadcrumb-forum" href="/t5/enterprise-resource-planning/ct-p/erp" id="link_2">
                     Enterprise Resource Planning
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-category lia-breadcrumb-category lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display_1" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-board lia-breadcrumb-board lia-breadcrumb-forum" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap" id="link_3">
                     ERP Blogs by SAP
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-board lia-breadcrumb-board lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display_2" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb final-crumb">
                    <span aria-disabled="true" aria-label="Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail" class="lia-link-navigation child-thread lia-link-disabled" disabled="true" id="link_4" role="link">
                     Extended Integration Analysis in SAP Readiness Che...
                    </span>
                   </li>
                  </ul>
                 </div>
                </div>
                <div class="custom-community-header-right lia-mark-empty">
                </div>
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-hero">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-hero-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                <div class="header-hero-wrapper">
                 <div class="lia-node-header-info lia-component-common-widget-node-information" id="nodeInformation">
                  <div class="lia-node-header-title">
                   Enterprise Resource Planning Blogs by SAP
                  </div>
                  <div class="lia-node-header-description">
                   Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.
                  </div>
                 </div>
                 <div class="SearchForm lia-search-form-wrapper lia-mode-default lia-component-common-widget-search-form" id="lia-searchformV32">
                  <div class="lia-inline-ajax-feedback">
                   <div class="AjaxFeedback" id="ajaxfeedback">
                   </div>
                  </div>
                  <div id="searchautocompletetoggle">
                   <div class="lia-inline-ajax-feedback">
                    <div class="AjaxFeedback" id="ajaxfeedback_0">
                    </div>
                   </div>
                   <form action="https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.form.form" class="lia-form lia-form-inline SearchForm" enctype="multipart/form-data" id="form" method="post" name="form">
                    <div class="t-invisible">
                     <input name="t:ac" type="hidden" value="blog-id/erp-blog-sap/article-id/42647"/>
                     <input name="t:cp" type="hidden" value="search/contributions/page"/>
                     <input name="lia-form-context" type="hidden" value="x3yJF4o6w2L1oRnojhvvVl8KiYfEICHnOtCXMNQ2GpStZFjBcevKuSCBFKczlZo8bFioFWGugIAaZzQnP3Nw_JGW2TqonwylTrO65Y8vWiycd5ug4Pz8_pyNFEY1DNI_960LN-9_7nW4iV1SHS22bY5xtWF5N_SiNrwz5zuCjzd1EjJGUVdiAGJhbITaEjgSolN8zovACaqnCkcAFIzrKTcw9F_eES3DKyaD3fhXxmdyD1EjSHsBcc1q8Bzfjo-9VTL-SFIXqQZx4S7fgEHZV3W21zbTj5eKrI3JHYrhzwUNywuZmpMuw-PP4wJNzVj_YIAjL8JCrwmc8wEaugl-dR-U5AlNGbc5zo9eWJgW6FhnhH8B3IuS1DjH6NIofeQN10wV3EDio4XDRHOEfuBGfdYnnZU1x-OQLSfWP4efxgJ89cKANqQi0rtP9YizyC5fWRVpXy8xcDJBqLogI5mZYhQMLo8RApNfEORs9EOAkcWiOYfqcxf06YxN7rzXQk8nlmsVlZ-koZGcObVte0mWj9WrH-gKW9bCiq-rNJLiJUkgIL3LgiVNZtNSObVs5kWsqkcnXeJ0RdADXCemqenww-xI__07wXpbYgnn6MZLPnXi2sYsxFNzjM8yLLXlwSw-rbPuPPjYFHxQY3CCwPWsA_NA8DOdmEFa49gR4FRK2ZXpYHFrRz5BpT4WMx3uVA1xpL4Q3fuEhVTvXFiceCHbYN4JLrSUZgT67BRQkkiljt2R2PVe9h7dAdw0b-vmyPUlMHWBxDv6Wd-Op7AvOIudm8ENM97DTPT1TzeSQdxUYKWQpNwPIwj7x8VJ4Kood1tus7vFJbTKeWI-rXs8uKV0HHDjai_waKnscYhuUfyOfNaVZDj9rEnVPFvYyHqvPwUsctLVRRE55ffwx-cla9Uddg-DJuo4d8M41mKo7KGVFArBqNjl46SPxv76G-OcMLzrQAbrtpwYqXB5f4IQo_Qy3A_JtShvO3MM1-q8P_mzYYK8BytuAAbvOwVM_9BYFqFj_7hm7TmTgguiscRitJ0kQU_m9mBfQYK8KmVhr8pFF1Qse3zE5CSEXVJsxY4cjpSAtcOqOkFH9-2CP1H1VBsiDVtDCmModyfMgjs88uH5W2gw5flc9KWx9YDeJUGiRZW2s3id_5Vr2i9xg0H2t39Lq3Hf1KdadiNfCRKOmEBMYk1kfiITS3e3d45dUqHAFarEBgZkffxzBXgu46Wkf6GFCiqLFFexZ_EsXnTYAj4Ksjo."/>
                     <input name="liaFormContentKey" type="hidden" value="BlogArticlePage:blog-id/erp-blog-sap/article-id/42647:searchformv32.form:"/>
                     <input name="t:formdata" type="hidden" value="5DI9GWMef1Esyz275vuiiOExwpQ=:H4sIAAAAAAAAALVSTU7CQBR+krAixkj0BrptjcpCMSbERGKCSmxcm+kwlGrbqTOvFDYexRMYL8HCnXfwAG5dubDtFKxgYgu4mrzvm3w/M+/pHcphHQ4kI4L2dMo9FLYZoM09qbeJxQ4V0+XC7e/tamqyBPEChwgbh1JAjQtLIz6hPaYh8ZlEMaxplAvm2KZmEsm0hhmBhOKpzZzOlsEw8LevR5W3zZfPEqy0oJIYc+eCuAyh2rolfaI7xLN0I8rjWfWBj7CuzJvf5osmbxRN3hacMimNwHRtKSOr0XNnv/vx+FoCGPjhMRzljhNLYHrEt9kA5T08ACCsKvREoYuqxqLl8BLO84q4UcMITcG49y/QOGs1pYyESl5p6V6qwRW086rinVmoxMZsiZud/zBUTc6gmVc4kExkJafmcYG1GM9+wfIsCkf2OP54hal5EjnG54z8h0XhjfcF7wQUs5Kz0GTjU2rOjc/llTT4Au07pDOcBQAA"/>
                    </div>
                    <div class="lia-inline-ajax-feedback">
                     <div class="AjaxFeedback" id="feedback">
                     </div>
                    </div>
                    <input name="lia-action-token" type="hidden" value="bUBpI_K2wj62qFLKgkaUx5HKmZwm1_6TrMXdJvdClPs."/>
                    <input id="form_UIDform" name="form_UID" type="hidden" value="form"/>
                    <input id="form_instance_keyform" name="form_instance_key" type="hidden" value=""/>
                    <span class="lia-search-granularity-wrapper">
                     <select aria-label="Search Granularity" class="lia-search-form-granularity search-granularity" id="searchGranularity" name="searchGranularity" title="Search Granularity">
                      <option selected="selected" title="All community" value="khhcw49343|community">
                       All community
                      </option>
                      <option title="This category" value="erp|category">
                       This category
                      </option>
                      <option title="Blog" value="erp-blog-sap|blog-board">
                       Blog
                      </option>
                      <option title="Knowledge base" value="tkb|tkb">
                       Knowledge base
                      </option>
                      <option title="Users" value="user|user">
                       Users
                      </option>
                      <option title="Managed tags" value="product|product">
                       Managed tags
                      </option>
                     </select>
                    </span>
                    <span class="lia-search-input-wrapper">
                     <span class="lia-search-input-field">
                      <span class="lia-button-wrapper lia-button-wrapper-secondary lia-button-wrapper-searchForm-action">
                       <input name="submitContextX" type="hidden" value="searchForm"/>
                       <input class="lia-button lia-button-secondary lia-button-searchForm-action" id="submitContext" name="submitContext" type="submit" value="Search"/>
                      </span>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText">
                      </span>
                      <input aria-label="Search" class="lia-form-type-text lia-autocomplete-input search-input lia-search-input-message" id="messageSearchField_0" name="messageSearchField" placeholder="What are you looking for today?" title="Search" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_0">
                      </span>
                      <input aria-label="Search" class="lia-form-type-text lia-autocomplete-input search-input lia-search-input-tkb-article lia-js-hidden" id="messageSearchField_1" name="messageSearchField_0" placeholder="What are you looking for today?" title="Search" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_1">
                      </span>
                      <input aria-label="Enter a user name or rank" class="lia-form-type-text UserSearchField lia-search-input-user search-input lia-js-hidden lia-autocomplete-input" id="userSearchField" name="userSearchField" ng-non-bindable="" placeholder="Enter a keyword to search within the users" title="Enter a user name or rank" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_2">
                      </span>
                      <input aria-label="Enter a search word" class="lia-form-type-text NoteSearchField lia-search-input-note search-input lia-js-hidden lia-autocomplete-input" id="noteSearchField_0" name="noteSearchField" placeholder="Enter a keyword to search within the private messages" title="Enter a search word" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_3">
                      </span>
                      <input aria-label="Enter a search word" class="lia-form-type-text ProductSearchField lia-search-input-product search-input lia-js-hidden lia-autocomplete-input" id="productSearchField" name="productSearchField" title="Enter a search word" type="text" value=""/>
                      <input class="lia-as-search-action-id" name="as-search-action-id" type="hidden"/>
                     </span>
                    </span>
                    <span class="lia-cancel-search">
                     cancel
                    </span>
                   </form>
                   <div class="search-autocomplete-toggle-link lia-js-hidden">
                    <span>
                     <a class="lia-link-navigation auto-complete-toggle-on lia-link-ticket-post-action lia-component-search-action-enable-auto-complete" data-lia-action-token="1f5PwKa83OvgYIrefSDmtHUnmYp-fQodP6KgiZUh0gQ." href="https://community.sap.com/t5/blogs/v2/blogarticlepage.enableautocomplete:enableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=action/contributions/searchactions" id="enableAutoComplete" rel="nofollow">
                      Turn on suggestions
                     </a>
                     <span class="HelpIcon">
                      <a aria-label="Help Icon" class="lia-link-navigation help-icon lia-tooltip-trigger" href="#" id="link_5" role="button">
                       <span alt="Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type." aria-label="Help Icon" class="lia-img-icon-help lia-fa-icon lia-fa-help lia-fa" id="display_3" role="img">
                       </span>
                      </a>
                      <div class="lia-content lia-tooltip-pos-bottom-left lia-panel-tooltip-wrapper" id="link_6-tooltip-element" role="alertdialog">
                       <div class="lia-tooltip-arrow">
                       </div>
                       <div class="lia-panel-tooltip">
                        <div class="content">
                         Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type.
                        </div>
                       </div>
                      </div>
                     </span>
                    </span>
                   </div>
                  </div>
                  <div class="spell-check-showing-result">
                   Showing results for
                   <span aria-disabled="true" class="lia-link-navigation show-results-for-link lia-link-disabled" id="showingResult">
                   </span>
                  </div>
                  <div>
                   <span class="spell-check-search-instead">
                    Search instead for
                    <a class="lia-link-navigation search-instead-for-link" href="#" id="searchInstead" rel="nofollow">
                    </a>
                   </span>
                  </div>
                  <div class="spell-check-do-you-mean lia-component-search-widget-spellcheck">
                   Did you mean:
                   <a class="lia-link-navigation do-you-mean-link" href="#" id="doYouMean" rel="nofollow">
                   </a>
                  </div>
                 </div>
                </div>
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-bottom">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-bottom-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single lia-mark-empty">
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-feedback">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-feedback">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
         <div class="lia-quilt-row lia-quilt-row-main">
          <div class="lia-quilt-column lia-quilt-column-16 lia-quilt-column-left lia-quilt-column-main-content">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-left">
            <div class="lia-panel-message message-uid-13503346 lia-component-article" data-lia-message-uid="13503346" id="messageview">
             <div class="lia-message-view-wrapper lia-js-data-messageUid-13503346 lia-component-forums-widget-message-view-two" data-lia-message-uid="13503346" id="messageView2_1">
              <span id="U13503346">
              </span>
              <span id="M42647">
              </span>
              <div class="lia-inline-ajax-feedback">
               <div class="AjaxFeedback" id="ajaxfeedback_1">
               </div>
              </div>
              <div class="MessageView lia-message-view-blog-topic-message lia-message-view-display lia-row-standard-unread lia-thread-topic">
               <span class="lia-message-state-indicator">
               </span>
               <div class="lia-quilt lia-quilt-blog-topic-message lia-quilt-layout-custom-message">
                <div class="lia-quilt-row lia-quilt-row-message-header-top">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-header-top-content">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                   <div class="lia-message-subject lia-component-message-view-widget-subject">
                    <div class="MessageSubject">
                     <div class="MessageSubjectIcons">
                      <h2 class="message-subject" itemprop="name">
                       <span class="lia-message-unread">
                        <a class="page-link lia-link-navigation lia-custom-event" href="/t5/enterprise-resource-planning-blogs-by-sap/extended-integration-analysis-in-sap-readiness-check-impact-analysis-in/ba-p/13503346" id="link_7">
                         Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail
                        </a>
                       </span>
                      </h2>
                      <span alt="Message contains a hyperlink" aria-label="Contains a hyperlink" class="lia-img-message-has-url lia-fa-message lia-fa-has lia-fa-url lia-fa" id="display_4" role="img" title="Contains a hyperlink">
                      </span>
                      <span alt="Message contains an image" aria-label="Contains an image" class="lia-img-message-has-image lia-fa-message lia-fa-has lia-fa-image lia-fa" id="display_5" role="img" title="Contains an image">
                      </span>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-header-main">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-header-main-content lia-mark-empty">
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-header-bottom">
                 <div class="lia-quilt-column lia-quilt-column-16 lia-quilt-column-left lia-quilt-column-message-header-bottom-left">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-left">
                   <div class="lia-message-author-avatar">
                    <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar lia-component-message-view-widget-author-avatar">
                     <img alt="astridtschense" class="lia-user-avatar-profile" id="imagedisplay" src="https://avatars.profile.sap.com/9/0/id9022f574fc023cb28a855cdfdf6f7d1952683662878e8530276b40b3cbf776ab_small.jpeg" title="astridtschense"/>
                    </div>
                   </div>
                   <div class="lia-message-author-with-avatar">
                    <span class="UserName lia-user-name lia-user-rank-Advisor lia-component-message-view-widget-author-username">
                     <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_6" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                     <a aria-label="View Profile of astridtschense" class="lia-link-navigation lia-page-link lia-user-name-link" href="https://community.sap.com/t5/user/viewprofilepage/user-id/37278" id="link_8" itemprop="url" style="" target="_self">
                      <span class="">
                       astridtschense
                      </span>
                     </a>
                    </span>
                    <div class="lia-message-author-rank lia-component-author-rank lia-component-message-view-widget-author-rank">
                     Advisor
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="lia-quilt-column lia-quilt-column-08 lia-quilt-column-right lia-quilt-column-message-header-bottom-right">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-right">
                   <div class="lia-menu-navigation-wrapper lia-js-hidden lia-menu-action lia-component-message-view-widget-action-menu" id="actionMenuDropDown">
                    <div class="lia-menu-navigation">
                     <div class="dropdown-default-item">
                      <a aria-expanded="false" aria-label="Show Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail post option menu" class="lia-js-menu-opener default-menu-option lia-js-mouseover-menu lia-link-navigation" href="#" id="dropDownLink" role="button" title="Show option menu">
                       Options
                      </a>
                      <div class="dropdown-positioning">
                       <div class="dropdown-positioning-static">
                        <ul aria-label="Dropdown menu items" class="lia-menu-dropdown-items" id="dropdownmenuitems" role="list">
                         <li role="listitem">
                          <a class="lia-link-navigation rss-thread-link lia-component-rss-action-thread" href="/khhcw49343/rss/message?board.id=erp-blog-sap&amp;message.id=42647" id="rssThread" rel="nofollow noopener noreferrer">
                           Subscribe to RSS Feed
                          </a>
                         </li>
                         <li aria-hidden="true">
                          <span class="lia-separator lia-component-common-widget-link-separator">
                           <span class="lia-separator-post">
                           </span>
                           <span class="lia-separator-pre">
                           </span>
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation mark-thread-unread lia-link-disabled lia-component-forums-action-mark-thread-unread" id="markThreadUnread">
                           Mark as New
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation mark-thread-read lia-link-disabled lia-component-forums-action-mark-thread-read" id="markThreadRead">
                           Mark as Read
                          </span>
                         </li>
                         <li aria-hidden="true">
                          <span class="lia-separator lia-component-common-widget-link-separator">
                           <span class="lia-separator-post">
                           </span>
                           <span class="lia-separator-pre">
                           </span>
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation addThreadUserBookmark lia-link-disabled lia-component-subscriptions-action-add-thread-user-bookmark" id="addThreadUserBookmark">
                           Bookmark
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation addThreadUserEmailSubscription lia-link-disabled lia-component-subscriptions-action-add-thread-user-email" id="addThreadUserEmailSubscription">
                           Subscribe
                          </span>
                         </li>
                         <li aria-hidden="true">
                          <span class="lia-separator lia-component-common-widget-link-separator">
                           <span class="lia-separator-post">
                           </span>
                           <span class="lia-separator-pre">
                           </span>
                          </span>
                         </li>
                         <li role="listitem">
                          <a class="lia-link-navigation print-article lia-component-forums-action-print-thread" href="/t5/blogs/blogarticleprintpage/blog-id/erp-blog-sap/article-id/42647" id="printThread" rel="nofollow">
                           Printer Friendly Page
                          </a>
                         </li>
                         <li role="listitem">
                          <a class="lia-link-navigation report-abuse-link lia-component-forums-action-report-abuse" href="/t5/notifications/notifymoderatorpage/message-uid/13503346" id="reportAbuse" rel="nofollow">
                           Report Inappropriate Content
                          </a>
                         </li>
                        </ul>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="post-info">
                    <div class="lia-message-post-date lia-component-post-date lia-component-message-view-widget-post-date" title="Posted on">
                     <span class="DateTime">
                      <span class="local-date">
                       ‎09-27-2021
                      </span>
                      <span class="local-time">
                       7:35 AM
                      </span>
                     </span>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-main">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-main-content">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                   <div class="KudosButton lia-button-image-kudos-wrapper lia-component-kudos-widget-button-version-3 lia-component-kudos-widget-button-horizontal lia-component-kudos-widget-button lia-component-kudos-action lia-component-message-view-widget-kudos-action" data-lia-kudos-id="13503346" id="kudosButtonV2">
                    <div class="lia-button-image-kudos lia-button-image-kudos-horizontal lia-button-image-kudos-enabled lia-button-image-kudos-not-kudoed lia-button">
                     <div class="lia-button-image-kudos-count">
                      <span aria-disabled="true" class="lia-link-navigation kudos-count-link lia-link-disabled" id="link_9" title="The total number of kudos this post has received.">
                       <span class="MessageKudosCount lia-component-kudos-widget-message-kudos-count" id="messageKudosCount_3e1fe15cccc68" itemprop="upvoteCount">
                        0
                       </span>
                       <span class="lia-button-image-kudos-label lia-component-kudos-widget-kudos-count-label">
                        Kudos
                       </span>
                      </span>
                     </div>
                     <div class="lia-button-image-kudos-give">
                      <a aria-label="Click here to give kudos to this post." class="lia-link-navigation kudos-link lia-link-ticket-post-action" data-lia-action-token="MHgnst4_7KV6hnRcSGNXt79ztrOpzcWXBrzKI6gTckQ." data-lia-kudos-entity-uid="13503346" href="https://community.sap.com/t5/blogs/v2/blogarticlepage.kudosbuttonv2.kudoentity:kudoentity/kudosable-gid/13503346?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=kudos/contributions/tapletcontributionspage" id="kudoEntity" onclick="return LITHIUM.EarlyEventCapture(this, 'click', true)" rel="nofollow" role="button" title="Click here to give kudos to this post.">
                      </a>
                     </div>
                    </div>
                   </div>
                   <div class="lia-message-body-wrapper lia-component-message-view-widget-body">
                    <div class="lia-message-body" id="bodyDisplay" itemprop="text">
                     <div class="lia-message-body-content">
                      Dear All,
                      <br/>
                      <br/>
                      This is the third blog in the series. Links to the other blogs in the series are provided at the end.
                      <br/>
                      <br/>
                      In this blog we will describe how the
                      <em>
                       Integration
                      </em>
                      check functions, which data sources are considered, how impact types are defined, and what they mean from a practical perspective.
                      <br/>
                      <br/>
                      As we pointed out in the first blog of the series, the new
                      <em>
                       Integration
                      </em>
                      check now performs the following:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        Provides a list of discovered interfaces
                       </li>
                       <br/>
                       <li>
                        Automates otherwise tedious analysis and cross-checks with different data sources
                       </li>
                       <br/>
                       <li>
                        Identifies interfaces that may no longer work (or function as expected) following the conversion to SAP S/4HANA or upgrade between SAP S/4HANA product versions
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      Let’s explore each of these capabilities in detail.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <strong>
                       List of Discovered Interfaces
                      </strong>
                      <br/>
                      <br/>
                      One of the surprises of the previous implementation of interface analysis within SAP Readiness Check was an unexpectedly high number of IDoc interfaces, reaching one million and beyond. This was the case because every IDoc-partner combination was counted separately.
                      <br/>
                      <br/>
                      Project experience showed that, in many cases, an interface adaptation activity is not partner specific. That led us to the idea of a technical interface definition that we now use in SAP Readiness Check to count interfaces. We define a technical interface by its key characteristics relevant for the impact analysis and adaptation activities. The number of partners is calculated to help with an effort estimation in case certain adaptation activities have to be repeated for every partner.
                      <br/>
                      <br/>
                      For example, an inbound RFC call to a custom function module is defined by the function module name. If this function module became binary incompatible upon system conversion or upgrade, the function module signature needs be adjusted on the receiver side, in SAP S/4HANA, or on the caller side. The impact type and the remediation activity depend on the function module only. However, the number of calling partners might help you choose the systems where you make the correction. The list of calling systems is provided as additional information.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      Let’s investigate what defines a technical interface.
                      <br/>
                      <br/>
                      <em>
                       IDocs
                      </em>
                      <br/>
                      <br/>
                      For the impact analysis of IDoc interfaces, it is important to distinguish their direction (inbound/outbound), message type, IDoc type, extension type, and function module. The number of IDoc interfaces is calculated as the number of unique combinations of these fields.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <em>
                       Web Services
                      </em>
                      <br/>
                      <br/>
                      Web services are primarily defined by the corresponding ABAP proxy, service definition, and direction. The combination of these three fields defines the number of Web service interfaces.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <em>
                       RFCs/BAPIs
                      </em>
                      <br/>
                      <br/>
                      RFC/BAPI calls are defined by direction, function module, and calling program. Function modules are analyzed for inbound calls. For outbound calls, since function modules belong to remote systems, only calling programs are analyzed.
                      <br/>
                      <br/>
                      The number of RFC interfaces is calculated as the number of combinations of these three fields.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <em>
                       BW Extractors
                      </em>
                      <br/>
                      <br/>
                      The SAP BW extractor name (OLTPSOURCE) is the key for counting the number of BW extractors configured in a system.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      You will find the list of technical interfaces on the detailed page, under a tab that corresponds to an interface type, by choosing the
                      <em>
                       Interfaces
                      </em>
                      view above the table.
                      <br/>
                      <p style="overflow: hidden;margin-bottom: 0px">
                       <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2021/09/interfaces-1.png"/>
                      </p>
                      <br/>
                      The
                      <em>
                       All Details
                      </em>
                      view provides you with access to all collected data and details at partner level.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <strong>
                       Automated Analysis and Cross-Checks with Different Data Sources
                      </strong>
                      <br/>
                      <br/>
                      While the simplification item catalog focuses on functional changes, the simplification database and ABAP test cockpit focus on custom code changes. There is, however, no single data source for interface-related changes. This makes interface analysis complex and highly dependent on expertise.
                      <br/>
                      <br/>
                      SAP Readiness Check now automates this and covers the following steps:
                      <br/>
                      <br/>
                      First, it generates a list of ABAP and DDIC objects associated with every “technical” interface. This includes BW extractors, IDoc types, tables, function modules, function groups, packages, and application components. It does not, however, consider extensions, such as user exits or extension points, invoked during interface processing.
                      <br/>
                      <br/>
                      Then, it cross-checks these objects with the following data sources:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        Content of SAP Note
                        <a href="https://launchpad.support.sap.com/#/notes/2500202" rel="noopener noreferrer">
                         2500202
                        </a>
                        – S4TWL – BW Extractors in SAP S/4HANA.
                       </li>
                       <br/>
                       <li>
                        Piece lists of simplification items with categories .
                       </li>
                       <br/>
                       <li>
                        List of ABAP and DDIC objects missing in the target SAP S/4HANA release. This list was produced by comparing combined TADIR entries of all ERP x releases and several latest releases of SAP S/4HANA.
                       </li>
                       <br/>
                       <li>
                        Internal blocklist (SAP Note
                        <a href="https://launchpad.support.sap.com/#/notes/2249880" rel="noopener noreferrer">
                         2249880
                        </a>
                        ) with forbidden transactions, programs, and function modules.
                       </li>
                       <br/>
                       <li>
                        External blocklist (SAP Note
                        <a href="https://launchpad.support.sap.com/#/notes/2408693" rel="noopener noreferrer">
                         2408693
                        </a>
                        ) with RFC-enabled function modules forbidden for external inbound calls.
                       </li>
                       <br/>
                       <li>
                        ABAP test cockpit findings for binary incompatible changes in custom function modules and custom IDoc extensions.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      The result of these cross-checks leads to different findings grouped into several impact types.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <strong>
                       Impact Types
                      </strong>
                      <br/>
                      <br/>
                      The interface analysis presents three impact types:
                      <br/>
                      <br/>
                      <em>
                       Functionality Unavailable
                      </em>
                      <br/>
                      <br/>
                      One of the standard ABAP or DDIC objects that are directly associated with an interface or referenced by an associated custom ABAP object is on the simplification list. The identified objects can be categorized as follows:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        Standard objects are checked against the simplification item catalog and the data dictionary of the target SAP S/4HANA release. This includes the cases when an object is missing in the target release, exists but is neither used nor supported, or when a table is partially used in SAP S/4HANA in comparison to SAP ERP.
                       </li>
                       <br/>
                       <li>
                        Custom objects are checked against the ABAP test cockpit scan results. For the integration analysis, only functional ABAP test cockpit findings are considered.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      The latter becomes available once you upload the ABAP test cockpit results to your SAP Readiness Check analysis.
                      <br/>
                      <br/>
                      Interfaces with this impact category will either dump with a syntax error due to an access request to a missing object or become instable as the referenced standard object is neither supported nor maintained in SAP S/4HANA.
                      <br/>
                      <br/>
                      Furthermore, impacted BW extractors are assigned to one of the following subcategories:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        <em>
                         Working
                        </em>
                        : The data source is available with SAP S/4HANA. There are no restrictions.
                       </li>
                       <br/>
                       <li>
                        <em>
                         Working with Restrictions
                        </em>
                        : The data source is working in SAP S/4HANA, but with noteworthy restrictions. For example, not all fields are available.
                       </li>
                       <br/>
                       <li>
                        <em>
                         Obsolete
                        </em>
                        : The data source is no longer relevant after system conversion. BW extractors in this category are legacy extractors.
                       </li>
                       <br/>
                       <li>
                        <em>
                         Not Working
                        </em>
                        : All data sources in this category are not working in SAP S/4HANA. The following subcategories exist:
                        <br/>
                        <ul>
                         <br/>
                         <li>
                          <em>
                           Alternative Exists
                          </em>
                          : The data source is not available with SAP S/4HANA, but an alternative exists. For example, by means of a new extractor or a CDS view.
                         </li>
                         <br/>
                         <li>
                          <em>
                           Alternative Planned
                          </em>
                          : The data source is not available with SAP S/4HANA yet, but an alternative is planned in the roadmap for future release. For most of the BW extractors assigned to this subcategory, the alternative will be an extractor based on a CDS view. To find out about the status, please check SAP Note
                          <a href="https://launchpad.support.sap.com/#/notes/2500202" rel="noopener noreferrer">
                           2500202
                          </a>
                          or create a support incident under the corresponding component.
                         </li>
                         <br/>
                         <li>
                          <em>
                           No Alternative Exists
                          </em>
                          : The data source is not working in SAP S/4HANA, and there is no alternative planned.
                         </li>
                         <br/>
                        </ul>
                        <br/>
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <br/>
                      <em>
                       Blocked
                      </em>
                      <br/>
                      <br/>
                      One of the standard programs or function modules associated with an interface is on the internal blocklist and can’t be used with SAP S/4HANA. Interfaces with this impact category will dump on first use due to an access to a blocked object.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      <em>
                       Serialization Issue
                      </em>
                      <br/>
                      <br/>
                      One of the objects associated with an interface becomes binary incompatible upon system conversion or upgrade due to a field length extension. These interfaces should not be used without adjustments on the caller or receiver side to avoid possible data inconsistencies.
                      <br/>
                      <br/>
                      There are three types of cases:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        An SAP S/4HANA version of an RFC-enabled standard function module and its SAP ERP version are binary incompatible. These function modules are listed in the external blocklist.
                       </li>
                       <br/>
                       <li>
                        An RFC-enabled custom function module becomes binary incompatible upon system conversion or upgrade. Such custom function modules are identified by the ABAP test cockpit.
                       </li>
                       <br/>
                       <li>
                        A custom or extension IDoc segment becomes binary incompatible upon system conversion or upgrade. Such IDoc segments are identified by the ABAP test cockpit.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      The last two become available once you upload the ABAP test cockpit results to your SAP Readiness Check analysis.
                      <br/>
                      <br/>
                      <br/>
                      <br/>
                      Previous blogs in this blog series:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        <a href="https://blogs.sap.com/2021/09/27/extended-integration-analysis-in-sap-readiness-check-is-now-live/" rel="noopener noreferrer">
                         <u>
                          Extended Integration Analysis in SAP Readiness Check Is Now Live
                         </u>
                        </a>
                        – Provides a short summary of the extended functionality; areas of improvement, supported interfaces, and an overview of the results.
                       </li>
                       <br/>
                       <li>
                        <a href="https://blogs.sap.com/2021/09/27/extended-integration-analysis-in-sap-readiness-check-prerequisites-and-data-collection/" rel="noopener noreferrer">
                         <u>
                          Extended Integration Analysis in SAP Readiness Check – Prerequisites and Data Collection
                         </u>
                        </a>
                        – What you need to install and why; what data is collected and transmitted to perform the analysis; and what option you have if you prefer to limit the data submitted for analysis.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      Next blogs in this blog series:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        <a href="https://blogs.sap.com/2021/09/27/extended-integration-analysis-in-sap-readiness-check-findings-next-steps-and-examples/" rel="noopener noreferrer">
                         <u>
                          Extended Integration Analysis in SAP Readiness Check – Findings, Next Steps, and Examples
                         </u>
                        </a>
                        – How to interpret and address the findings; we will walk through the next steps proposed by SAP Readiness Check using real examples.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                     </div>
                    </div>
                   </div>
                   <div class="custom-view-associated-products">
                    <ul class="lia-list-standard-inline" id="list_0" role="list">
                     <li aria-level="5" class="custom-labels-title" role="heading">
                      SAP Managed Tags:
                     </li>
                     <li class="lia-link-navigation lia-custom-event">
                      <a href="https://community.sap.com/t5/c-khhcw49343/SAP%2520S%252F4HANA/pd-p/73554900100800000266">
                       SAP S/4HANA
                      </a>
                      ,
                     </li>
                     <li class="lia-link-navigation lia-custom-event">
                      <a href="https://community.sap.com/t5/c-khhcw49343/SAP%2520Readiness%2520Check/pd-p/1647bf27-5e10-4efd-89e1-a59efaf4e250">
                       SAP Readiness Check
                      </a>
                     </li>
                    </ul>
                   </div>
                   <div class="LabelsForArticle lia-component-labels lia-component-message-view-widget-labels-with-event" id="labelsWithEvent">
                    <span aria-level="5" class="article-labels-title" role="heading">
                     Labels:
                    </span>
                    <div class="LabelsList">
                     <ul class="lia-list-standard-inline" id="list_0" role="list">
                      <li class="label">
                       <a class="label-link lia-link-navigation lia-custom-event" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/product%20updates" id="link_10" rel="tag">
                        Product Updates
                        <wbr/>
                       </a>
                      </li>
                     </ul>
                    </div>
                   </div>
                   <div class="TagList lia-message-tags lia-component-message-view-widget-tags" id="taglist">
                    <ul aria-label="User Tags" class="lia-list-standard-inline" id="list_1" role="list">
                     <li class="tag-263 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-263 lia-js-data-tagUid-263" href="/t5/tag/integration/tg-p/board-id/erp-blog-sap" id="link_11" rel="tag">
                       integration
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                     <li class="tag-132935 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-132935 lia-js-data-tagUid-132935" href="/t5/tag/New%20Features%20in%20SAP%20Readiness%20Check%202.0/tg-p/board-id/erp-blog-sap" id="link_12" rel="tag">
                       New Features in SAP Readiness Check 2.0
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                     <li class="tag-783 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-783 lia-js-data-tagUid-783" href="/t5/tag/SAP%20Readiness%20Check/tg-p/board-id/erp-blog-sap" id="link_13" rel="tag">
                       SAP Readiness Check
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                     <li class="tag-139837 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-139837 lia-js-data-tagUid-139837" href="/t5/tag/sapreadinesscheck_news/tg-p/board-id/erp-blog-sap" id="link_14" rel="tag">
                       sapreadinesscheck_news
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                    </ul>
                    <div class="lia-inline-ajax-feedback">
                     <div class="AjaxFeedback" id="ajaxFeedback">
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-footer">
                 <div class="lia-quilt-column lia-quilt-column-08 lia-quilt-column-left lia-quilt-column-message-footer-left lia-mark-empty">
                 </div>
                 <div class="lia-quilt-column lia-quilt-column-16 lia-quilt-column-right lia-quilt-column-message-footer-right">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-right">
                   <div class="footer-top">
                    <div class="lia-button-group lia-component-comment-button lia-component-message-view-widget-comment-button">
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-moderation">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-moderation-content">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                   <div class="footer-bottom lia-mark-empty">
                   </div>
                  </div>
                 </div>
                </div>
               </div>
              </div>
             </div>
             <div class="lia-progress lia-js-hidden" id="progressBar">
              <div class="lia-progress-indeterminate">
              </div>
             </div>
            </div>
            <div class="lia-text lia-blog-article-page-comment-count lia-discussion-page-sub-section-header lia-component-comment-count-conditional">
             2 Comments
            </div>
            <a name="comments">
            </a>
            <div class="lia-component-blogs-page-comment-list-loader lia-component-lazy-loader lia-lazy-load lia-component-comment-list" id="lazyload">
            </div>
            <a name="comment-on-this">
            </a>
            <span id="feedback-successinformationbox_7">
            </span>
            <div class="InfoMessage lia-panel-feedback-banner-note lia-component-comment-editor" id="informationbox_7">
             <div class="lia-text" role="alert">
              <p ng-non-bindable="" tabindex="0">
               You must be a registered user to add a comment. If you've already registered, sign in. Otherwise, register and sign in.
              </p>
              <ul class="lia-list-standard" id="list_2" role="list">
               <li>
                <a class="lia-link-navigation blog-link lia-message-comment-post" href="/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?redirectreason=permissiondenied&amp;referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fextended-integration-analysis-in-sap-readiness-check-impact-analysis-in%2Fba-p%2F13503346%23comment-on-this" id="link_15" rel="nofollow">
                 Comment
                </a>
               </li>
              </ul>
             </div>
            </div>
           </div>
          </div>
          <div class="lia-quilt-column lia-quilt-column-08 lia-quilt-column-right lia-quilt-column-side-content">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-right">
            <div class="lia-panel lia-panel-standard LabelsTaplet Chrome lia-component-labels-widget-labels-list">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span aria-level="3" class="lia-panel-heading-bar-title" role="heading">
                   Labels in this area
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <div id="labelsTaplet">
                   <div class="BlogLabelsTaplet">
                    <div class="LabelsList" id="list_3">
                     <ul class="lia-list-standard" id="list_4" role="list">
                      <li class="label even-row">
                       <a aria-label="Business Trends" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/business%20trends" id="link_16">
                        Business Trends
                        <wbr/>
                       </a>
                       <span class="label-count">
                        363
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Business Trends​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/business%20trends%E2%80%8B" id="link_17">
                        Business Trends​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        1
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Event Information" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/event%20information" id="link_18">
                        Event Information
                        <wbr/>
                       </a>
                       <span class="label-count">
                        462
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Event Information​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/event%20information%E2%80%8B" id="link_19">
                        Event Information​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        9
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Expert Insights" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/expert%20insights" id="link_20">
                        Expert Insights
                        <wbr/>
                       </a>
                       <span class="label-count">
                        114
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Expert Insights​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/expert%20insights%E2%80%8B" id="link_21">
                        Expert Insights​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        38
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Life at SAP" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/life%20at%20sap" id="link_22">
                        Life at SAP
                        <wbr/>
                       </a>
                       <span class="label-count">
                        420
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Life at SAP​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/life%20at%20sap%E2%80%8B" id="link_23">
                        Life at SAP​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        1
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Product Updates" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/product%20updates" id="link_24">
                        Product Updates
                        <wbr/>
                       </a>
                       <span class="label-count">
                        4,691
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Product Updates​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/product%20updates%E2%80%8B" id="link_25">
                        Product Updates​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        74
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Technology Updates" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/technology%20updates" id="link_26">
                        Technology Updates
                        <wbr/>
                       </a>
                       <span class="label-count">
                        1,505
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Technology Updates​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/technology%20updates%E2%80%8B" id="link_27">
                        Technology Updates​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        31
                       </span>
                      </li>
                     </ul>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
            <div class="lia-panel lia-panel-standard custom-related-content">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span class="lia-panel-heading-bar-title">
                   Related Content
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <ul class="lia-list-standard">
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/governance-risk-and-compliance-grc-with-sap-s-4hana-cloud-public-edition/ba-p/13586848">
                     Governance, Risk, and Compliance (GRC) with SAP S/4HANA Cloud, Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      Monday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/finance-in-sap-s-4hana-cloud-public-edition-2402/ba-p/13590761">
                     Finance in SAP S/4HANA Cloud Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      Monday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/public-sector-management-in-sap-s-4hana-cloud-public-edition-2402/ba-p/13591283">
                     Public Sector Management in SAP S/4HANA Cloud Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      Friday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/sourcing-amp-procurement-in-sap-s-4hana-cloud-public-edition-2402/ba-p/13590144">
                     Sourcing &amp; Procurement in SAP S/4HANA Cloud Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      Thursday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/news-from-sap-activate-sap-s-4hana-cloud-public-edition-2402/ba-p/13589835">
                     News from SAP Activate SAP S/4HANA Cloud Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      Wednesday
                     </time>
                    </small>
                   </li>
                  </ul>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
            <div class="lia-panel lia-panel-standard custom-popular-blog-articles">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span class="lia-panel-heading-bar-title">
                   Popular Blog Posts
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <section>
                   <article class="custom-popular-article-tile">
                    <a href="/t5/enterprise-resource-planning-blogs-by-sap/useful-documents-on-scn/ba-p/13101633" title="View article">
                     <img alt="" src="https://community.sap.com/html/assets/img_tile-default.png"/>
                    </a>
                    <h3>
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/useful-documents-on-scn/ba-p/13101633">
                      Useful documents on SCN
                     </a>
                    </h3>
                    <aside>
                     <a class="UserAvatar lia-link-navigation" href="/t5/user/viewprofilepage/user-id/10611" title="View profile">
                      <img alt="theme-lib.general.user-avatar" class="lia-user-avatar-message" src="https://avatars.profile.sap.com/b/6/idb649706629af2724d98fb26f5d1428fb257cf2d216340afe52cd31da26978bc0_small.jpeg"/>
                     </a>
                     <strong>
                      <span>
                       by
                      </span>
                      <a href="/t5/user/viewprofilepage/user-id/10611" rel="author" title="View profile">
                       <span class="">
                        Nancy
                       </span>
                      </a>
                     </strong>
                     <small>
                      •
                     </small>
                     <em>
                      Product and Topic Expert
                     </em>
                    </aside>
                    <footer>
                     <ul class="custom-tile-statistics">
                      <li class="custom-tile-views">
                       <b>
                        132504
                       </b>
                       Views
                      </li>
                      <li class="custom-tile-replies">
                       <b>
                        123
                       </b>
                       comments
                      </li>
                      <li class="custom-tile-kudos">
                       <b>
                        219
                       </b>
                       kudos
                      </li>
                     </ul>
                     <div class="post-time">
                      <time>
                       01-06-2015
                      </time>
                     </div>
                    </footer>
                   </article>
                   <article class="custom-popular-article-tile">
                    <a href="/t5/enterprise-resource-planning-blogs-by-sap/evolution-of-abap/ba-p/13522761" title="View article">
                     <img alt="" src="https://community.sap.com/html/assets/img_tile-default.png"/>
                    </a>
                    <h3>
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/evolution-of-abap/ba-p/13522761">
                      Evolution of ABAP
                     </a>
                    </h3>
                    <aside>
                     <a class="UserAvatar lia-link-navigation" href="/t5/user/viewprofilepage/user-id/533" title="View profile">
                      <img alt="theme-lib.general.user-avatar" class="lia-user-avatar-message" src="https://avatars.profile.sap.com/e/0/ide03ed0fa93f30abfeac07c402c516ddc1e247fb3fb2774a62c9624e0a47fc785_small.jpeg"/>
                     </a>
                     <strong>
                      <span>
                       by
                      </span>
                      <a href="/t5/user/viewprofilepage/user-id/533" rel="author" title="View profile">
                       <span class="">
                        KARLKESSLER
                       </span>
                      </a>
                     </strong>
                     <small>
                      •
                     </small>
                     <em>
                      Advisor
                     </em>
                    </aside>
                    <footer>
                     <ul class="custom-tile-statistics">
                      <li class="custom-tile-views">
                       <b>
                        24452
                       </b>
                       Views
                      </li>
                      <li class="custom-tile-replies">
                       <b>
                        42
                       </b>
                       comments
                      </li>
                      <li class="custom-tile-kudos">
                       <b>
                        192
                       </b>
                       kudos
                      </li>
                     </ul>
                     <div class="post-time">
                      <time>
                       09-01-2022
                      </time>
                     </div>
                    </footer>
                   </article>
                   <article class="custom-popular-article-tile">
                    <a href="/t5/enterprise-resource-planning-blogs-by-sap/analytics-in-s-4hana-real-shape-of-embedded-analytics-and-beyond-embedded/ba-p/13403536" title="View article">
                     <img alt="" src="https://community.sap.com/html/assets/img_tile-default.png"/>
                    </a>
                    <h3>
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/analytics-in-s-4hana-real-shape-of-embedded-analytics-and-beyond-embedded/ba-p/13403536">
                      Analytics in S/4HANA - real shape of embedded analytics and beyond embedded analytics
                     </a>
                    </h3>
                    <aside>
                     <a class="UserAvatar lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131707" title="View profile">
                      <img alt="theme-lib.general.user-avatar" class="lia-user-avatar-message" src="https://avatars.profile.sap.com/d/c/iddc47e4382f12bb063df8959eaa11212843bb1f4922da76e911867d0a173536f6_small.jpeg"/>
                     </a>
                     <strong>
                      <span>
                       by
                      </span>
                      <a href="/t5/user/viewprofilepage/user-id/131707" rel="author" title="View profile">
                       <span class="">
                        Masaaki
                       </span>
                      </a>
                     </strong>
                     <small>
                      •
                     </small>
                     <em>
                      Advisor
                     </em>
                    </aside>
                    <footer>
                     <ul class="custom-tile-statistics">
                      <li class="custom-tile-views">
                       <b>
                        96748
                       </b>
                       Views
                      </li>
                      <li class="custom-tile-replies">
                       <b>
                        32
                       </b>
                       comments
                      </li>
                      <li class="custom-tile-kudos">
                       <b>
                        182
                       </b>
                       kudos
                      </li>
                     </ul>
                     <div class="post-time">
                      <time>
                       06-08-2019
                      </time>
                     </div>
                    </footer>
                   </article>
                  </section>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
            <div class="lia-panel lia-panel-standard KudoedAuthorsLeaderboardTaplet Chrome lia-component-kudos-widget-authors-leaderboard">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span aria-level="3" class="lia-panel-heading-bar-title" role="heading">
                   Top kudoed authors
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <div class="UserList lia-component-users-widget-user-list">
                   <span id="user-listuserList">
                   </span>
                   <div class="t-data-grid" id="grid">
                    <table class="lia-list-slim" role="presentation">
                     <thead class="lia-table-head" id="columns">
                      <tr>
                       <th class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text t-first" scope="col">
                        <span aria-disabled="true" class="lia-view-filter lia-link-disabled" id="link_28">
                         User
                        </span>
                       </th>
                       <th class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer t-last" scope="col">
                        Count
                       </th>
                      </tr>
                     </thead>
                     <tbody>
                      <tr class="lia-list-row lia-row-odd t-first">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-131558 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131558" id="link_29">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="SDenecken" class="lia-user-avatar-message" id="imagedisplay_0" src="https://avatars.profile.sap.com/0/b/id0bee1c5eb86ded2e6b290e7e400cafce88fd51258967fecdea226f48f1affbf1_small.jpeg" title="SDenecken"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Employee">
                              <img alt="Employee" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_7" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Employee"/>
                              <span class="">
                               SDenecken
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 2,029" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        2029
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-121014 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/121014" id="link_30">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="former_member121014" class="lia-user-avatar-message" id="imagedisplay_1" src="https://avatars.profile.sap.com/former_member_small.jpeg" title="former_member121014"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_8" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               former_member12
                               <wbr/>
                               1014
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 1,203" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        1203
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-131461 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131461" id="link_31">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Gerhard_Welker" class="lia-user-avatar-message" id="imagedisplay_2" src="https://avatars.profile.sap.com/3/a/id3ad0f2da55e04fe9ba2e7851cf31b9406e23ca17cf5f0b21225b4be1f67254af_small.jpeg" title="Gerhard_Welker"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_9" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               Gerhard_Welker
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 1,072" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        1072
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-755 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/755" id="link_32">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="mahesh_sardesai" class="lia-user-avatar-message" id="imagedisplay_3" src="https://avatars.profile.sap.com/8/a/id8add3238dfb86754123fab2374437646e50fe79a842990baf674e3f6f49e0e6e_small.jpeg" title="mahesh_sardesai"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_10" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               mahesh_sardesai
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 1,055" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        1055
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-131707 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131707" id="link_33">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Masaaki" class="lia-user-avatar-message" id="imagedisplay_4" src="https://avatars.profile.sap.com/d/c/iddc47e4382f12bb063df8959eaa11212843bb1f4922da76e911867d0a173536f6_small.jpeg" title="Masaaki"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_11" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               Masaaki
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 899" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        899
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-17414 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/17414" id="link_34">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="janmusil" class="lia-user-avatar-message" id="imagedisplay_5" src="https://avatars.profile.sap.com/7/5/id75bacc120bd928ba0e87c1c763270c5b287b548075ed54265fa072ac81d68074_small.jpeg" title="janmusil"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_12" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               janmusil
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 888" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        888
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-6638 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/6638" id="link_35">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="OlgaDolinskaja" class="lia-user-avatar-message" id="imagedisplay_6" src="https://avatars.profile.sap.com/3/d/id3de2dde04a1aa64b641424db3bbbc1eb95d2a09d6ce8813af73d131db316e370_small.jpeg" title="OlgaDolinskaja"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_13" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               OlgaDolinskaja
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 755" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        755
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-40376 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/40376" id="link_36">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="christian_v2" class="lia-user-avatar-message" id="imagedisplay_7" src="https://avatars.profile.sap.com/c/f/idcf5d0531ff6e21c8818384a144f9b5c1859d67ba97b8b86286ee779db9418445_small.jpeg" title="christian_v2"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_14" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               christian_v2
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 680" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        680
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-17006 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/17006" id="link_37">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Amin-Hoque" class="lia-user-avatar-message" id="imagedisplay_8" src="https://avatars.profile.sap.com/a/a/idaa332129eb19aa8ce045f53b67fec68127885409a9d7524aaa2498c2fefaebab_small.jpeg" title="Amin-Hoque"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_15" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               Amin-Hoque
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 647" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        647
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even t-last">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-10611 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/10611" id="link_38">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Nancy" class="lia-user-avatar-message" id="imagedisplay_9" src="https://avatars.profile.sap.com/b/6/idb649706629af2724d98fb26f5d1428fb257cf2d216340afe52cd31da26978bc0_small.jpeg" title="Nancy"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_16" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               Nancy
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 515" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        515
                       </td>
                      </tr>
                     </tbody>
                    </table>
                   </div>
                  </div>
                  <div class="lia-view-all">
                   <a class="lia-link-navigation view-all-link" href="/t5/forums/kudosleaderboardpage/board-id/erp-blog-sap/timerange/one_month/page/1/tab/authors" id="link_39">
                    View all
                   </a>
                  </div>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
         <div class="lia-quilt-row lia-quilt-row-footer">
          <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-common-footer">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
            <div class="lia-quilt lia-quilt-footer lia-quilt-layout-custom-community-footer lia-component-quilt-footer">
             <div class="lia-quilt-row lia-quilt-row-footer-top">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-top-content lia-mark-empty">
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-footer-main">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-main-content lia-mark-empty">
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-footer-bottom">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-bottom-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single lia-mark-empty">
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-footer-external">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-external-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                <ds-footer is-light-theme="false" is-show-cookie-preferences="true" navigation-items='[
{"url": "https://pages.community.sap.com/resources/sap-community-privacy-statement", "title": "Privacy Statement", "target": "_blank", "label": "Privacy"},
{"url": "https://www.sap.com/corporate/en/legal/terms-of-use.html", "title": "Terms of Use", "target": "_blank", "label": "Terms of Use"},
{"url": "https://www.sap.com/about/legal/copyright.html", "title": "View the Copyright Information", "target": "_blank", "label": "Copyright"},
{"url": "https://www.sap.com/about/legal/impressum.html", "target": "_blank", "label": "Legal Disclosure"},
{"url": "https://www.sap.com/about/legal/trademark.html", "title": "View the Trademark Information", "target": "_blank", "label": "Trademark"},
{"url": "https://www.sap.com/cmp/nl/sap-community-voice/index.html", "title": "View the Community newsletter information", "target": "_blank", "label": "Newsletter"},
{"url": "mailto:<EMAIL>", "title": "Get Community support", "target": "_blank", "label": "Support"}
]' social-links='[{"tooltip":"Visit the Services and Support from SAP Facebook page","target":"_blank","rel":"noopener noreferrer","name":"facebook","url":"https://www.facebook.com/SAPDigitalBusinessServices/","iconName":"Facebook"},{"tooltip":"Follow the SAP Support Twitter page","target":"_blank","rel":"noopener noreferrer","name":"twitter","url":"https://twitter.com/SAPSupportHelp","iconName":"Twitter"},{"tooltip":"Subscribe to Services and Support from SAP","target":"_blank","rel":"noopener noreferrer","name":"youtube","url":"https://www.youtube.com/user/SAPSupportInfo","iconName":"Youtube"},{"tooltip":"Follow SAP Support Help","target":"_blank","rel":"noopener noreferrer","name":"linkedin","url":"https://www.linkedin.com/groups/138840","iconName":"LinkedIn"}]' social-links-title='"Follow"'>
                </ds-footer>
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </center>
  </div>
  <script type="text/javascript">
   _satellite.pageBottom();
  </script>
  <!-- skin Page Hitbox Content START -->
  <script type="text/javascript">
   new Image().src = ["/","b","e","a","c","o","n","/","2","3","9","0","3","5","3","3","1","9","5","_","1","7","0","7","2","8","6","2","5","1","5","8","5",".","g","i","f"].join("");
  </script>
  <script language="javascript" type="text/javascript">
   <!--
LITHIUM.ScriptLoader.ready(['common', 'body', 'angularjs', 'angularjsModules'], function () {
LITHIUM.AngularSupport.setOptions({
  "useCsp" : true,
  "useNg2" : false,
  "coreModuleDeps" : [
    "li.directives.messages.message-image",
    "li.directives.common.non-bindable"
  ],
  "debugEnabled" : false
});
LITHIUM.AngularSupport.initGlobal(angular);LITHIUM.AngularSupport.init();LITHIUM.Globals.restoreGlobals(['define', '$', 'jQuery', 'angular']);LITHIUM.Sandbox.restore();
LITHIUM.jQuery.fn.cssData.defaults = {"dataPrefix":"lia-js-data","pairDelimeter":"-","prefixDelimeter":"-"};
(($) => {
$(document).ready(() => {
    var Prism = window.PrsmK || window.Prism;
    var classupdates = [
            {'old': 'language-visual', 'new': 'language-visual-basic'},
            {'old': '-basic', 'new': ''},
            {'old': 'language-excel', 'new': 'language-excel-formula'},
            {'old': '-formula', 'new': ''}
        ];
    const fixer = (el5) => {
        $.each(classupdates, (i5, v5) => {
            if ((0 < (v5.old || '').length) && $(el5).hasClass(v5.old)) {
                if (0 < (v5.new || '').length) $(el5).addClass(v5.new);
                $(el5).removeClass(v5.old);
            }
        });
    };
    const worker = (el) => {
        fixer(el);
        //add classes for answers and blogs posts
        if(($(el)[0].className.length > 0) && $(el)[0].className.match(/language-(\w+)/) && !$(el).hasClass('lia-code-sample')) $(el).addClass('lia-code-sample');

        if($(el)[0].className.length === 0) $(el).addClass('lia-code-sample language-abap');
        //end add classes for answers and blogs

        if (!$(el).hasClass('line-numbers')) $(el).addClass('line-numbers');
        if ($('> code', el).length < 1){
            $(el).wrapInner('<code></code>');
            if($(el).hasClass("language-abap")){
                $(el).find('> code').addClass('language-abap');
            }
        }
        $('> code', el).each((i, v) => {
            fixer(v);
            if ($('.line-numbers-rows', v).length < 1) Prism.highlightElement(v);
        });
    };
    const styleNewSamples = () => {
        $('body pre.lia-code-sample').each((i2, v2) => {
            worker(v2);
        });
        $('.mce-edit-area iframe').each((i3, v3) => {
            $(v3).contents().find('body pre.lia-code-sample').each((i4, v4) => {
                worker(v4);
            });
        });
        //add classes for answers and blogs posts
        $('body pre').each((i5, v5) => {
            worker(v5);
        });
    };
    styleNewSamples();
    setInterval(() => {
        styleNewSamples();
    }, 500);
});
})(LITHIUM.jQuery);

LITHIUM.CommunityJsonObject.User.policies['forums.action.message-view.batch-messages.allow'] = false;
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_0","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_1","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_2","feedbackSelector":".InfoMessage"});
LITHIUM.AjaxFeedback(".lia-inline-ajax-feedback", "LITHIUM:hideAjaxFeedback", ".lia-inline-ajax-feedback-persist");
LITHIUM.Placeholder();
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.defaultAjaxFeedbackHtml = \"<div class=\\\"lia-inline-ajax-feedback lia-component-common-widget-ajax-feedback\\\">\\n\\t\\t\\t<div class=\\\"AjaxFeedback\\\" id=\\\"ajaxFeedback_3e1fe1358877d\\\"><\\/div>\\n\\t\\t\\t\\n\\t\\n\\n\\t\\n\\n\\t\\t<\\/div>\";LITHIUM.AjaxSupport.defaultAjaxErrorHtml = \"<span id=\\\"feedback-errorfeedback_3e1fe1371a7ca\\\"> <\\/span>\\n\\n\\t\\n\\t\\t<div class=\\\"InfoMessage lia-panel-feedback-inline-alert lia-component-common-widget-feedback\\\" id=\\\"feedback_3e1fe1371a7ca\\\">\\n\\t\\t\\t<div role=\\\"alert\\\" class=\\\"lia-text\\\">\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t<p ng-non-bindable=\\\"\\\" tabindex=\\\"0\\\">\\n\\t\\t\\t\\t\\t\\tSorry, unable to complete the action you requested.\\n\\t\\t\\t\\t\\t<\\/p>\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\t\\t\\t<\\/div>\\n\\n\\t\\t\\t\\n\\t\\t<\\/div>\";LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3e1fe134dcef6', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'r9KWvQ_FCMAzKBEnNteqrABwndHx7A8phnCVR9DwrGE.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"B8HN5pG9fH9GkqvGAo5nWbrAaGCBrT_50vTtJ-eqtKI.\" rel=\"nofollow\" id=\"disableAutoComplete_3e1fe134dcef6\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":3},"inputSelector":"#messageSearchField_0","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.messagesearchfield.messagesearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_0","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3e1fe13b8a862', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'B2pqm1R9Qi1zqvwOy9jkLinc4Txe-GrLGEFRPZtPHpI.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"UGbPJgybqbnqMWR_uUEVYpPEYAbghPypsZyOmCbe8DU.\" rel=\"nofollow\" id=\"disableAutoComplete_3e1fe13b8a862\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":3},"inputSelector":"#messageSearchField_1","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.tkbmessagesearchfield.messagesearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_1","updateInputOnSelect":true,"loadingText":"Searching for users...","emptyText":"No Matches","successText":"Users found:","defaultText":"Enter a user name or rank","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3e1fe13ec13a1', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, '_hO3FbR0OoHKjusd1SW3f1JC5WGCvkBf0Xl12vNT_pU.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"Y7aAwvX_ngzjEepcVla-ksvqjpckowrbWjnC1HCinXI.\" rel=\"nofollow\" id=\"disableAutoComplete_3e1fe13ec13a1\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":0},"inputSelector":"#userSearchField","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.usersearchfield.usersearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AjaxSupport({"ajaxOptionsParam":{"event":"LITHIUM:userExistsQuery","parameters":{"javascript.ignore_combine_and_minify":"true"}},"tokenId":"ajax","elementSelector":"#userSearchField","action":"userExistsQuery","feedbackSelector":"#ajaxfeedback_0","url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.usersearchfield:userexistsquery?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=search/contributions/page","ajaxErrorEventName":"LITHIUM:ajaxError","token":"oVkucOzXfBwY38eCDV3UnibeY0G8VvGVqR8w05_t3q8."});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_2","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3e1fe141d6930', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'bAOmGj7ruJf4faZSi2GsVHDWpFQwJPZjMcRWf1xjuP4.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"58n1ObsQ9ZERf0nJvZprR8tTSf-xBkXBiN5-7Z7V7_w.\" rel=\"nofollow\" id=\"disableAutoComplete_3e1fe141d6930\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":0},"inputSelector":"#noteSearchField_0","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.notesearchfield.notesearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_3","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3e1fe1444a9d3', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'MHQJ7V4ny2RUE6PtM3YV-l9tPVceP4K_GWiTqv0Qyo4.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"H76s6NwNqb-qC71VIMPfnEA1LbAh4wu4kkWFENeHPv8.\" rel=\"nofollow\" id=\"disableAutoComplete_3e1fe1444a9d3\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":0},"inputSelector":"#productSearchField","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.productsearchfield.productsearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.Link({"linkSelector":"a.lia-link-ticket-post-action"});
LITHIUM.AjaxSupport.fromLink('#enableAutoComplete', 'enableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, '3o57CY1oo2FgPi22tcNKyskU4_6LeNxpaUgMX4zHn9E.', 'ajax');
LITHIUM.Tooltip({"bodySelector":"body#lia-body","delay":30,"enableOnClickForTrigger":false,"predelay":10,"triggerSelector":"#link_5","tooltipContentSelector":"#link_6-tooltip-element .content","position":["bottom","left"],"tooltipElementSelector":"#link_6-tooltip-element","events":{"def":"focus mouseover keydown,blur mouseout keydown"},"hideOnLeave":true});
LITHIUM.HelpIcon({"selectors":{"helpIconSelector":".help-icon .lia-img-icon-help"}});
LITHIUM.SearchAutoCompleteToggle({"containerSelector":"#searchautocompletetoggle","enableAutoCompleteSelector":".search-autocomplete-toggle-link","enableAutocompleteSuccessEvent":"LITHIUM:ajaxSuccess:enableAutoComplete","disableAutoCompleteSelector":".lia-autocomplete-toggle-off","disableAutocompleteSuccessEvent":"LITHIUM:ajaxSuccess:disableAutoComplete","autoCompleteSelector":".lia-autocomplete-input"});
LITHIUM.SearchForm({"asSearchActionIdSelector":".lia-as-search-action-id","useAutoComplete":true,"selectSelector":".lia-search-form-granularity","useClearSearchButton":false,"buttonSelector":".lia-button-searchForm-action","asSearchActionIdParamName":"as-search-action-id","formSelector":"#lia-searchformV32","nodesModel":{"erp|category":{"title":"Search Category: Enterprise Resource Planning Blogs by SAP","inputSelector":".lia-search-input-message"},"khhcw49343|community":{"title":"Search Community: Enterprise Resource Planning Blogs by SAP","inputSelector":".lia-search-input-message"},"erp-blog-sap|blog-board":{"title":"Search Blog: Enterprise Resource Planning Blogs by SAP","inputSelector":".lia-search-input-message"},"tkb|tkb":{"title":"Knowledge base","inputSelector":".lia-search-input-tkb-article"},"product|product":{"title":"Managed tags","inputSelector":".lia-search-input-product"},"user|user":{"title":"Users","inputSelector":".lia-search-input-user"}},"asSearchActionIdHeaderKey":"X-LI-AS-Search-Action-Id","inputSelector":"#messageSearchField_0:not(.lia-js-hidden)","clearSearchButtonSelector":null});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#pageInformation","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_3","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_4","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_5","feedbackSelector":".InfoMessage"});
LITHIUM.Form.resetFieldForFocusFound();
LITHIUM.CustomEvent('.lia-custom-event', 'click');
LITHIUM.DropDownMenu({"userMessagesFeedOptionsClass":"div.user-messages-feed-options-menu a.lia-js-menu-opener","menuOffsetContainer":".lia-menu-offset-container","hoverLeaveEvent":"LITHIUM:hoverLeave","mouseoverElementSelector":".lia-js-mouseover-menu","userMessagesFeedOptionsAriaLabel":"Show contributions of the user, selected option is Show Extended Integration Analysis in SAP Readiness Check - Impact Analysis in Detail post option menu. You may choose another option from the dropdown menu.","disabledLink":"lia-link-disabled","menuOpenCssClass":"dropdownHover","menuElementSelector":".lia-menu-navigation-wrapper","dialogSelector":".lia-panel-dialog-trigger","messageOptions":"lia-component-message-view-widget-action-menu","menuBarComponent":"lia-component-menu-bar","closeMenuEvent":"LITHIUM:closeMenu","menuOpenedEvent":"LITHIUM:menuOpened","pageOptions":"lia-component-community-widget-page-options","clickElementSelector":".lia-js-click-menu","menuItemsSelector":".lia-menu-dropdown-items","menuClosedEvent":"LITHIUM:menuClosed"});
LITHIUM.DropDownMenuVisibilityHandler({"selectors":{"menuSelector":"#actionMenuDropDown","menuItemsSelector":".lia-menu-dropdown-items"}});
LITHIUM.AjaxSupport.fromLink('#kudoEntity', 'kudoEntity', '#ajaxfeedback_1', 'LITHIUM:ajaxError', {}, 'F8H16M1q92w56VBekbWrHLrOodQCMVhVFCtFcjaOQ2s.', 'ajax');
LITHIUM.AjaxSupport.ComponentEvents.set({
  "eventActions" : [
    {
      "event" : "kudoEntity",
      "actions" : [
        {
          "context" : "envParam:entity",
          "action" : "rerender"
        }
      ]
    }
  ],
  "componentId" : "kudos.widget.button",
  "initiatorBinding" : true,
  "selector" : "#kudosButtonV2",
  "parameters" : {
    "displayStyle" : "horizontal",
    "disallowZeroCount" : "false",
    "revokeMode" : "true",
    "kudosable" : "true",
    "showCountOnly" : "false",
    "disableKudosForAnonUser" : "false",
    "useCountToKudo" : "false",
    "entity" : "13503346",
    "linkDisabled" : "false"
  },
  "initiatorDataMatcher" : "data-lia-kudos-id"
});
LITHIUM.MessageBodyDisplay('#bodyDisplay', '.lia-truncated-body-container', '#viewMoreLink', '.lia-full-body-container' );
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_6","feedbackSelector":".InfoMessage"});
LITHIUM.AjaxSupport.ComponentEvents.set({
  "eventActions" : [
    {
      "event" : "approveMessage",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        },
        {
          "context" : "",
          "action" : "pulsate"
        }
      ]
    },
    {
      "event" : "unapproveMessage",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        },
        {
          "context" : "",
          "action" : "pulsate"
        }
      ]
    },
    {
      "event" : "deleteMessage",
      "actions" : [
        {
          "context" : "lia-deleted-state",
          "action" : "addClassName"
        },
        {
          "context" : "",
          "action" : "pulsate"
        }
      ]
    },
    {
      "event" : "QuickReply",
      "actions" : [
        {
          "context" : "envParam:feedbackData",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "expandMessage",
      "actions" : [
        {
          "context" : "envParam:quiltName,expandedQuiltName",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "ProductAnswer",
      "actions" : [
        {
          "context" : "envParam:quiltName",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "ProductAnswerComment",
      "actions" : [
        {
          "context" : "envParam:selectedMessage",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "editProductMessage",
      "actions" : [
        {
          "context" : "envParam:quiltName,message",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetEditAction",
      "actions" : [
        {
          "context" : "envParam:quiltName,message,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "ProductMessageEdit",
      "actions" : [
        {
          "context" : "envParam:quiltName",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetMessageEdit",
      "actions" : [
        {
          "context" : "envParam:quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "AcceptSolutionAction",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "RevokeSolutionAction",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "addThreadUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "removeThreadUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "addMessageUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "removeMessageUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "markAsSpamWithoutRedirect",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetAnswerForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,page,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetEditAnswerForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetCommentForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetEditCommentForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    }
  ],
  "componentId" : "forums.widget.message-view",
  "initiatorBinding" : true,
  "selector" : "#messageview",
  "parameters" : {
    "disableLabelLinks" : "false",
    "truncateBodyRetainsHtml" : "false",
    "forceSearchRequestParameterForBlurbBuilder" : "false",
    "kudosLinksDisabled" : "false",
    "useSubjectIcons" : "true",
    "quiltName" : "BlogTopicMessage",
    "truncateBody" : "true",
    "message" : "13503346",
    "includeRepliesModerationState" : "false",
    "useSimpleView" : "false",
    "useTruncatedSubject" : "true",
    "disableLinks" : "false",
    "messageViewOptions" : "1111110111111100111111101110100101111101",
    "displaySubject" : "true"
  },
  "initiatorDataMatcher" : "data-lia-message-uid"
});
LITHIUM.Components.renderInPlace('blogsPageCommentList', {"componentParams":"{\n  \"articleRef\" : \"42647\",\n  \"blogRef\" : \"V-LZJ_OJGfXpgE5esPt-GJtAvJOw8_mwz-e6SAwESsjPLtm4SHmyDObkdjekg7q8WvNhP07glkduFiKL4SGzsT8Rf_-jXDUZ-JJgulqYTxqaR5ttC0vMgw0PfsuCVu9BeUAd6jEEaAjalSHF6zBsO0oTnqF10F9VpMWFcsKinYSSRod6vxUSbTbwzx9wjbTxSRLwZQMyAm8WFbNgZ9s8q92rjk9dfmjn1TC0NiH8MkS291pdXxcCjpdJaK1TtWX7d_6hvnbZiO8YQWMpnBM-8RYreRGa0_S-G14oBbnQNTTz8dyJBVoZ9fDsOlN7-DUtJfaWV4-3GMgwxUC9GiU-aNBoPSeNdFo-Km_UlpXiVlyRiyFok5pfu29wOJ4O_Qv2jGuSAwWD4IuzwHmqFL4yJj6x4nOWGYVJUbD0EA1Vw4HSPqaar9KymiQHsQlen8X5_c78i7trhHxl0-x779n1HXXY0X7mWU6qftgct_oYO9S2ZO7IizuVAQkfrSCBEw5oDixAar5GApsB4EhPugqt2Wq03mrGCk3PXiM5R-N4IwwbL3kt6TkewZr20BLpQ6XkqgBwi-cHmTxuQlO2rYfP1PyW9ASAYkof3MdGrz-ZqO0LlRg-lFImPIucJ8g-qKHET_-pmCFMlXzeqSGfB6tJ5RP92G8pl8P4BKEZuqfuK1oG3hD12NmK3wDuvrh-aD5EDbYHo52VT1Bo-3fn7-VdDR7Xik4INfmKmjACbTe125Q_ObCkKHoRYGXju9MUBiRZI_HBrLS9Y8OzbmBx3V6M-7UdtinGNvKoFtbiUREIRVZB_kPKQuD0bGNUAimro8gTUbAfoOgG1NJf2Z-RXKOOW80TfCDVxnlt7MKGYVW0eRmc95S72lEKd42gq6SvxIiFqJvZG8G_DbkTQ8lx2fVfmADgju7oK8S3TH8anhfXfq55N0jOEyzF07-vwyJpRSGt9f3d4yD3CjscqWRbd-Q6OxOVFxHnRiFxZjG2N-WE7cHHZBgiTZk0YCYRqW46UuqsWV6RvC6_cyGGVPErBhScONSaU0Eh_JwbNEq81CNLrGpKgIPWeHxrNWjP0O8iLTwILpRiQWgp9TR6gOrnCJ47fYNvgY8TG57CSp4Hgrw-coJ-ttNYEXv2tALyR9-FO392dQhVRtl_KNvpOGyGuJ1QdCbIA1ZxsSoJJ4GOZeMTOraVWl7C84a-SVaDzRXLBMwfdrd0SqhO42TKJfa5CbFhGdNrGlMw65MmVVvmWXY1DVCuEaDfxMTSw_DKZhZ7tEPWHLCIUfAK0xN1v9_9sKG3zmGnrkmlqKkMTh96D959pggQ8Sy1RnwSr6tG_JtUD-J-RsvYs2AtMi1zRiBX5oXDSwxhsVOcC93aXVbXgf8Box7IexvW-shfdfRLuTovTHBp-z5dkVZ86phWjL7DVCtynotH-G6JOrtdfujG_rIFkwd3VbwvzGHWdEGvEosRtTC54Fzf-Ubj_wRka6OFEWGozFfymnyhl0OOcuPWIRgwsWylhMOM2L3DPPsh1jiGXULk56iMTZYQwam5lKMg8ukLNd7M5QkAfroBj47urMTOa81m6LTqRTbcgWF0-SR8nfl8EyPILsB2Aa_dC0xwGBXnNOEw3_Xy8Ps22ltUY8fNBA8PrjUZEHGPd9QNBXkBc5t8xMpO7xLiLhkBklKbK_J0MakL-vQ6QKGEpPhnQ2XZhFBj-vyiUzgWo--UaB_GiKvsm7KPnNF9GUjNnQCU2GburSUr_ZU8aC3uDzBJt9ufW7jDINCTC8_5GYAT9_x7gkV6W_bUIJBrTWLVVz4GDwxe2GBdJI2bvaDSBbMiqyUbhE_6o8XTAprefMWmxNrYSwuf\",\n  \"messageViewOptions\" : \"1111110111111100111111101110100101111101\",\n  \"viewAllComments\" : \"false\",\n  \"commentPageId\" : \"1\"\n}","componentId":"blogsPageCommentList"}, {"errorMessage":"An Unexpected Error has occurred.","type":"POST","url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.componentdisplay:lazyrender?t:ac=blog-id/erp-blog-sap/article-id/42647"}, 'lazyload');
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_7","feedbackSelector":".InfoMessage"});
LITHIUM.AjaxSupport.ComponentEvents.set({
  "eventActions" : [
    {
      "event" : "sortLabelsWidget",
      "actions" : [
        {
          "context" : "envParam:viewOrderSpec",
          "action" : "rerender"
        }
      ]
    }
  ],
  "componentId" : "labels.widget.labels.sortable",
  "initiatorBinding" : false,
  "selector" : "#labelsTaplet",
  "parameters" : {
    "labelDisplaySize" : "100",
    "useSortHeader" : "false",
    "viewOrderSpec" : "k_McYMllMF9G7S7zGpGmooxSYIzHb6L12gkkLxPizMNLs-j6Nf_Yo6oJCyqb5SvA5t1d1qlNTLl5J0Sx3fIGBoNoL1Y43fFTld2AjaTVAXS1_dhfJQO42vbYLkFEPuTRa9xR1v6jbx8QpKjDGcNHgCvvEcdJl9niXbZZ3CBWrtbqeBcpSSQvz4UArcCoMS8uiC9R19VIh44Nd9AnV_0H2fb2NMgPyzj7e1g8Kv9j2M0EA3sPCCaUPaEPHOSqKTiTtxXHhk2CmHpO4IQK9R3Om17QUvacRXTakFIx_HbFnSQMXTsvPiPJfw7RyzaXck-l4BsXIeBhx0d8uLrHjigl2gDbtSXx9otLjLMurvniPZVqw17_m6iRbwrJc4Cy4-fNsKiXceGDsusbgIFxFZ3gK0shsXQ9TZrvGFqaZeFhKBDv-ke5OSzMEK3ni74cMI_zwjhAxHHwkrusGIwJM3Xa8OXhYc82BNvo-JG9GSc0hAGRheHaFPBlLh0hdC595yFEzHSzJNtXptodxgbCylw7J6ajP8qxnRup0kTzpBs5rYE."
  },
  "initiatorDataMatcher" : ""
});
LITHIUM.AjaxSupport({"ajaxOptionsParam":{"event":"LITHIUM:sortLabelsWidget","parameters":{"javascript.ignore_combine_and_minify":"true"}},"tokenId":"ajax","elementSelector":"#labelsTaplet","action":"sortLabelsWidget","feedbackSelector":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.labelstaplet:sortlabelswidget?t:ac=blog-id/erp-blog-sap/article-id/42647&t:cp=labels/contributions/page","ajaxErrorEventName":"LITHIUM:ajaxError","token":"DbyOdel-OhPWksEy-Kl7XxIIgysJDFe5yZTciTYEKl8."});
LITHIUM.UserListActual({"acceptedSolutionsColumnSelector":".UserList .lia-list-row .acceptedSolutionsCountColumn","kudosColumnSelector":".UserList .lia-list-row .kudosCountColumn"});
;document.createElement('ds-footer');
document.addEventListener('openCookiePreferences', (e) => {
  truste.eu.reopenBanner();

});

LITHIUM.PartialRenderProxy({"limuirsComponentRenderedEvent":"LITHIUM:limuirsComponentRendered","relayEvent":"LITHIUM:partialRenderProxyRelay","listenerEvent":"LITHIUM:partialRenderProxy"});
LITHIUM.AjaxSupport({"ajaxOptionsParam":{"event":"LITHIUM:partialRenderProxyRelay","parameters":{"javascript.ignore_combine_and_minify":"true"}},"tokenId":"ajax","elementSelector":document,"action":"partialRenderProxyRelay","feedbackSelector":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.liabase.basebody.partialrenderproxy:partialrenderproxyrelay?t:ac=blog-id/erp-blog-sap/article-id/42647","ajaxErrorEventName":"LITHIUM:ajaxError","token":"2RRwFyQBhCROGrq6oCgqWooalDGJHVJLWER-uNt-gfk."});
LITHIUM.Auth.API_URL = "/t5/util/authcheckpage";
LITHIUM.Auth.LOGIN_URL_TMPL = "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?referer=https%3A%2F%2FREPLACE_TEXT";
LITHIUM.Auth.KEEP_ALIVE_URL = "/t5/status/blankpage?keepalive";
LITHIUM.Auth.KEEP_ALIVE_TIME = 180000;
LITHIUM.Auth.CHECK_SESSION_TOKEN = 'Vfmt04oQ7yIpomO5EzGwqJDoSyRQy7odlnhR7RFRe1g.';
LITHIUM.AjaxSupport.useTickets = false;
LITHIUM.Cache.CustomEvent.set([{"elementId":"link_7","stopTriggerEvent":false,"fireEvent":"LITHIUM:selectMessage","triggerEvent":"click","eventContext":{"message":13503346}},{"elementId":"link_10","stopTriggerEvent":false,"fireEvent":"LITHIUM:labelSelected","triggerEvent":"click","eventContext":{"uid":1307,"selectedLabel":"product updates","title":"Product Updates"}}]);
LITHIUM.Loader.runJsAttached();

});
// -->
  </script>
 </body>
</html>
