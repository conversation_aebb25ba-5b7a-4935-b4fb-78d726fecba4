{"Request": {"Number": "1698281", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 396, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017521312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001698281?language=E&token=545D3BB81F957C0C212E877A14531027"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001698281", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001698281/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1698281"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.11.2021"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA Database"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1698281 - Assess the memory consumption of a SAP HANA System"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Assess the memory consumption of a SAP HANA System</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BW on HANA, sizing, memory</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>After a DB migration (from a not - HANA Database to HANA Database) you need detailed infomation about memory consumption.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The python script memorySizing.py attached to this note analyzes the HANA database after completed migration; it collects a statistics on the memory consumption of a SAP HANA system. This comprises memory usage information also on single table and attribute level.<br /><br />The information may be used for the following use cases:</p>\r\n<ul>\r\n<li>provide information for sizing a SAP HANA system,</li>\r\n</ul>\r\n<ul>\r\n<li>understand actual memory usage of a running SAP HANA database system</li>\r\n</ul>\r\n<ul>\r\n<li>collect information to be used for support requests and for debugging.</li>\r\n</ul>\r\n<p><br />The output of this script is not aimed to be directly used for licence fee or pricing considerations.<br /><br />The script helps to avoid some pitfalls when using information like<br />provided by the operating system, for example:<br /><br />1) The internal memory management of SAP HANA may<br />report allocated memory larger than actually used memory.<br /><br />2) Tables may be loaded into main memory only when they are accessed<br />for the first time. Thus, the reported memory may appear too low as<br />unloaded tables are not considered.<br /><br />In the case you miss to understand some parts of the output, you might create a message on component HAN-DB.</p>\r\n<p><strong>Installation and usage hints for the attached script</strong></p>\r\n<p><br />Log into the HANA system with the NewDB admin user (sidadm user) and install the attachment with these steps:<br />1. download the attachment to the exe/python_support directory<br />2. navigate to the exe/python_support directory<br /><br />After installing you remain logged-in as sidadm user, in the exe/python_support directory. Make sure that all environment variables like SAP_RETRIEVAL_PATH are set, e.g. execute<br />\"source /usr/sap/&lt;SID&gt;/HDB&lt;instance&gt;/HDBSettings.sh\" (for bash) or<br />\"source /usr/sap/&lt;SID&gt;/HDB&lt;instance&gt;/HDBSettings.csh\" (for csh).<br /><br />The script reads metadata of all available tables in the system.&#160; Schemas or tables that are not readable will not included in the analysis and error messages will be traced. For full information, please use a user that has read permissions for all relevant tables.</p>\r\n<p><br />Notice:<br />If you call the script on a HANA database with revision lower Rev 24,<br />on all tables to be loaded, an explicit unload will be triggered. This is done to make sure that all parts not yet loaded are loaded into memory to generate correct evaluation results of the script. Specially the explicit unload can result in very long runtimes of the script. On higher revisions, only a 'LOAD ALL' SQL statement is issued on the tables which loads the missing parts.</p>\r\n<p><strong>Usage Information:</strong></p>\r\n<p>This usage info is pasted here informally, you should check the parameters by calling \"python memorySizing.py --help\" on command line before using the script.<br /><br />Usage:<br />&#160;&#160;&#160;&#160;This script collects a number of sizing parameters from a HANA database instance.<br />&#160;&#160;&#160;&#160;The results are printed to stdout, unless you give a file name by option --file.<br />&#160;&#160;&#160;&#160;The output is formatted as CSV data with semicolon as field separator; suitable for programs like MS Excel.<br />&#160;&#160;&#160;&#160;The script tries to load all tables and to merge all existing deltas, in order to get correct results,<br />&#160;&#160;&#160;&#160;because table parts not loaded are not counted into aggregation, and delta has a different memory to data ratio.<br /><br />&#160;&#160;&#160;&#160;For HANA Revisons less than 24, the tables are explicitely unloaded before loading, assuring correctness of evaluation.<br />&#160;&#160;&#160;&#160;This may cause the script to run for several hours!<br /><br />&#160;&#160;&#160;&#160;DO NOT USE THIS SCRIPT DURING DATA LOAD PHASES:<br /><br />&#160;&#160;&#160;&#160;the load and merge loops may run some long time,<br />&#160;&#160;&#160;&#160;the merging may slow down the system performance, and the new data loaded after a merge is done will produce new deltas,<br />&#160;&#160;&#160;&#160;this may decrease correctness of the evaluation results<br /><br />&#160;&#160;&#160;&#160;Usage: memorySizing.py [options]<br /><br />Options:<br />&#160;&#160;-h, --help&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;show this help message and exit<br /><br />&#160;&#160;General Options:<br />&#160;&#160;&#160;&#160;-a ADDRESS, --address=ADDRESS<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;server address (&lt;server&gt;[:&lt;port&gt;])for DB connection<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[default: localhost]<br />&#160;&#160;&#160;&#160;-u USER, --user=USER<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;user for DB connection<br />&#160;&#160;&#160;&#160;-p PASSWORD, --password=PASSWORD<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;password for DB connection<br />&#160;&#160;&#160;&#160;-s SYSPASSWD, --syspasswd=SYSPASSWD<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;password for user SYSTEM<br />&#160;&#160;&#160;&#160;-v, --verbose&#160;&#160;&#160;&#160;&#160;&#160; if present write verbose output [default: False]<br />&#160;&#160;&#160;&#160;-f OUTFILE, --file=OUTFILE<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;the file to write to [default: memorySizing.csv]<br /><br />&#160;&#160;Measurement Options:<br />&#160;&#160;&#160;&#160;-l LOADTABLES, --loadTables=LOADTABLES<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;if yes (no), (do not) load tables before sizing<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;[default: yes]<br />&#160;&#160; -i IMMEDIATEUNLOAD, --immediateUnload=IMMEDIATEUNLOAD<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; immediately unload a table after loading (possible<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; values: no, unloaded, partially_loaded, all) - only<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; relevant together with --loadTables yes [default: no]<br />&#160;&#160;&#160;&#160;-m MERGECOMPRESS, --mergeCompress=MERGECOMPRESS<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;if yes (no), (do not) merge and compress tables before<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;sizing&#160;&#160;[default: yes]</p>\r\n<p><strong>Remarks on Usage:</strong></p>\r\n<p><br />Parameters -a, -p -u, -s are used as connection parameters. The user must have privileges to read the relevant schemas and system views, e.g. SYS.M_CS_COLUMNS.<br />Information is collected for all nodes of a distributed SAP HANA DB that is identified by the connection parameters.<br /><br />The file specified in parameter -f is overwritten if a file with the same name already exists. Output is formatted as Comma Separated Values (CSV) that can be imported into tools like MS Excel, the field delimiter is semicolon; the file may also be attached to support messages. The file consists of different blocks of measured results, each preceeded by a line with a brief description of the data and a heading line giving the meaning of the columns of the data block.<br /><br />Parameter -l makes sure that all tables are loaded into main memory for sizing purposes.&#160;&#160;Depending on the data volume this can take several hours.&#160;&#160;However, on a multi-core system with x cores, the script will spawn x/2 threads to parallelize the loading task.<br />The script can be used to assess the memory when all data is in use, e.g. for purposes of sizing. Notice, that additional main memory may be required, e.g. for merging tables. So the overall memory consumption reported cannot be used immediately for sizing.</p>\r\n<p>Parameter -i can be used to unload tables after they were loaded.&#160; After a table was loaded into main memory the main statistics on the table are available for the remaining process.&#160; To save main memory or to recover the state of loaded tables as of before the script was running one can use this option:</p>\r\n<ul>\r\n<li>\"-i no\" will not unload any tables after they were loaded.&#160; Use this for most precise results.&#160; This is the default</li>\r\n<li>\"-i unloaded\" will unload all tables that were completely unloaded before the script was started. Partially or fully loaded column tables will remain in memory as fully loaded.</li>\r\n<li>\"-i partially_loaded\" will unload all tables that were completely unloaded or partially loaded&#160;before the script was started. Fully loaded column tables will remain fully loaded.</li>\r\n<li>\"-i all\" will unload all tables unconditionally.</li>\r\n</ul>\r\n<p>Parameter -m makes sure that all table updates are merged into the main storage and compressed before applying the sizing for more robust information on memory usage.<br /><br />The overall runtime of the script on a large database may take several hours to complete. If you want to see quickly a rough overview of the memory usage, you may call the script with merging and loading switched off (-m no -l no), it needs then only a few minutes. For a correct sizing information, you call it thereafter with both options switched on.<br /><br />Besides merging and compressing of table content the system remains unchanged. Thus, the script can be applied multiple times. The result changes between different calls will be caused by user or system activity, collected system statistics data for example.</p>\r\n<p><strong>Understanding the results:</strong></p>\r\n<p>For understanding the meaning of the script output, you should read the \"HANA Administration Guide\", section \"Memory Usage in the SAP HANA Database\" to be found with this link: https://help.sap.com/viewer/product/SAP_HANA_PLATFORM.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D049164)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D068864)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001698281/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "memorySizing.py", "FileSize": "27", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000061302012&iv_version=0016&iv_guid=6CAE8B3E8D4B1ED691823686379340C6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1872170", "RefComponent": "XX-SER-SIZING", "RefTitle": "ABAP on HANA sizing report (S/4HANA, Suite on HANA...)", "RefUrl": "/notes/1872170"}, {"RefNumber": "1730999", "RefComponent": "HAN-DB", "RefTitle": "Configuration changes to SAP HANA system", "RefUrl": "/notes/1730999"}, {"RefNumber": "1730928", "RefComponent": "HAN-DB", "RefTitle": "Using external software in a HANA appliance", "RefUrl": "/notes/1730928"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2138117", "RefComponent": "HAN-APP-DWS-DDO", "RefTitle": "Export HANA system meta-data for DDO application", "RefUrl": "/notes/2138117 "}, {"RefNumber": "1872170", "RefComponent": "XX-SER-SIZING", "RefTitle": "ABAP on HANA sizing report (S/4HANA, Suite on HANA...)", "RefUrl": "/notes/1872170 "}, {"RefNumber": "1730999", "RefComponent": "HAN-DB", "RefTitle": "Configuration changes to SAP HANA system", "RefUrl": "/notes/1730999 "}, {"RefNumber": "1730928", "RefComponent": "HAN-DB", "RefTitle": "Using external software in a HANA appliance", "RefUrl": "/notes/1730928 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}