{"Request": {"Number": "966073", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 264, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016132882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000966073?language=E&token=4AFBFACFA1026DC03A0FB69770053CD9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000966073", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000966073/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "966073"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.12.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "966073 - Oracle Flash Recovery Area/Fast Recovery Area"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p></p> <b>Flash Recovery Area/Fast Recovery Area (FRA)</b><br /> <p><br />This note describes the configuration and administration of the <B>Flash Recovery Area (FRA)</B> or <B>Fast Recovery Area (FRA)</B> for the use with Flashback databases using SAP BR*Tools.<br /><br />For information about the \"Flashback Database\" feature, see Note 966117.<br /><br />This note is valid for Oracle Releases 10.2 and 11.2.<br /></p> <b>New features in Release 11.2</b><br /> <UL><LI>'Flash Recovery Area' was renamed as 'Fast Recovery Area' (FRA).</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />The starting point is a standard configured Oracle database of an SAP system without Fast Recovery Area.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Contents</b><br /> <p><br />General information<br />Prerequisites<br />SAP backup strategy with SAP BR*Tools and Fast Recovery Area<br />Configuration of Fast Recovery Area<br />Administration of Fast Recovery Area<br /></p> <b>General information</b><br /> <b></b><br /> <p>The Flash Recovery Area or Fast Recovery Area is a separate directory in a fast storage system that is created especially for backup, restore and recovery purposes.<br /><br />The main purpose of the Fast Recovery Area is to store all files required for restoring the database to be able to restore the database as fast as possible with these files if required. Depending on the configuration and the backup strategy used, you can backup the following files in the FRA:</p> <UL><LI>Current control file</LI></UL> <UL><LI>Online logs/Archived logs</LI></UL> <UL><LI>Flashback logs</LI></UL> <UL><LI>Control file autobackups/Control file copies</LI></UL> <UL><LI>Datafile copies/Backup pieces</LI></UL> <p><br />The configuration described in this note operates on the assumption that you use SAP BR*Tools (BRBACKUP, BRARCHIVE, BRRESTORE, BRRECOVER) to backup and recover Oracle, and that you use the Fast Recovery Area only for Oracle Flashback logs (see Note 966117).<br /></p> <b>Prerequisites</b><br /> <p><br />The parameters <B>LOG_ARCHIVE_DEST</B> and <B>LOG_ARCHIVE_DUPLEX_DEST</B> are incompatible with the configuration of a Flash Recovery Area. If these parameters are set, you must replace them with the parameters LOG_ARCHIVE_DEST_1 and LOG_ARCHIVE_DEST_2.<br /><br />See the parameter recommendations in SAP Notes 830576 (Release 10.2) and 1431798 (Release 11.2).<br /></p> <b>SAP backup strategy with SAP BR*Tools and Fast Recovery Area</b><br /> <p><br />During an SAP installation, the FRA is not set up by default. You should only set up an FRA if you want to use Flashback Database (also see the recommendation in Note 966117).<br /><br />The SAP BR*Tools support the Fast Recovery Area under the following prerequisites:</p> <UL><LI>You use Oracle Release 10.2 or higher.</LI></UL> <UL><LI>You use SAP BR*Tools Release 7.10 Patchlevel 6.<br />See Note 1125923 (brspace -f mfback).</LI></UL> <p><br />The configuration described here is supported by SAP-BR*Tools, which means:</p> <UL><LI>The previous backup strategy works as before.</LI></UL> <UL><LI>Oracle Archive logs are written to the same directory and are backed up as before using brarchive.</LI></UL> <UL><LI>The FRA is used only for Flashback Log files.</LI></UL> <p></p> <b>Configuration of Fast Recovery Area</b><br /> <p><br />When configuring the Flash Recovery Area, note the following:</p> <UL><LI>Flashback Logs are in the Fast Recovery Area under '&lt;db_recovery_file_dest&gt;/&lt;DB_NAME&gt;/flashback/*.flb'. The standard path for the FRA in the SAP environment is defined as follows:</LI></UL> <UL><UL><LI>On UNIX platforms: <B><B>/oracle/&lt;DBSID&gt;/oraflash</B></B></LI></UL></UL> <UL><UL><LI>On Windows platforms: <B><B>&lt;sapmnt&gt;\\oracle\\&lt;DBSID&gt;\\oraflash</B></B></LI></UL></UL> <UL><LI>You must specify a maximum size for the FRA.</LI></UL> <UL><LI>For performance reasons, the FRA should be stored in its own fast storage (no raw device).</LI></UL> <UL><LI>Each database has its own FRA.</LI></UL> <UL><LI>For all RAC systems, the FRA must be in shared storage so that it can be accessed from all RAC nodes. The parameter values for &lt;db_recovery_file_dest_size&gt; and &lt;db_recovery_file_dest&gt; are identical for all RAC instances.</LI></UL> <p><br />Procedure:<br /></p> <OL>1. Set the following parameters:</OL> <UL><UL><LI>DB_RECOVERY_FILE_DEST_SIZE<br /><NOBR>SQL&gt; alter system set db_recovery_file_dest_size =</NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;db_recovery_file_dest_size&gt;;</NOBR><br /><br /><B>Recommendation about the size estimate </B><br /><br /> It makes sense to retain Flashback Logs as long as archive logs. The space requirement of Flashback Logs and archive logs is approximately the same (proportionally). To determine a useful size for the Flash Recovery Area, you must, therefore, determine how many archive logs are written between two archive log backups when there is a high load on the system. In addition, to take account of peak load times that occur from time to time, you must then multiply this value by factor 2 again. The result of this is the size for the Flash Recovery Area.<br /><br />In the SAP environment, a size of 30G is generally a good start value to begin with. Depending on the size of the database, the change load, and the required retention period for flashback data, more or less space may have to be reserved.</LI></UL></UL> <UL><UL><LI>DB_RECOVERY_FILE_DEST<br /><NOBR>SQL&gt; alter system set db_recovery_file_dest =</NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0; '&lt;db_recovery_file_dest&gt;';</NOBR></LI></UL></UL> <OL>2. If required, restart the database and check the configuration with regard to the BR*Tools backup strategy.</OL> <p></p> <b>Administration of Fast Recovery Area</b><br /> <p><br /><B><B>Space monitoring of Fast Recovery Area</B></B><br /><br /><NOBR>SQL&gt; SELECT * FROM V$RECOVERY_FILE_DEST;</NOBR><br /><NOBR>SQL&gt; SELECT * FROM V$FLASH_RECOVERY_AREA_USAGE;</NOBR><br /><br /><B><B>Deactivating the Fast Recovery Area</B></B><br /><br /><NOBR>SQL&gt; ALTER SYSTEM RESET DB_RECOVERY_FILE_DEST SCOPE=SPFILE SID='*';</NOBR><br /><br />Flashback Logging should have been deactivated beforehand (see Note 966117). Otherwise:<br /><NOBR>ORA-38775: cannot disable flash recovery area - flashback database is enabled</NOBR><br /><br />If no FRA is configured and if Flashback Logging is still active, the system reports the following error when the instance is restarted:<br /><NOBR>ORA-38760: This database instance failed to turn on flashback database</NOBR><br /><NOBR>ORA-38776: cannot begin flashback generation - flash recovery area is disabled</NOBR><br /><br /><B><B>Reactivating the Fast Recovery Area</B></B><br /><br /><NOBR>SQL&gt; ALTER SYSTEM SET DB_RECOVERY_FILE_DEST='&lt;db_recovery_file_dest&gt;';</NOBR><br /><br />You can then activate the Flashback Logging (Note 966117) again.<br /><br /><B>Archive Log status/Check parameter </B><br /><NOBR>SQL&gt; archive log list</NOBR><br /><NOBR>SQL&gt; col name&#x00A0;&#x00A0;format a30</NOBR><br /><NOBR>SQL&gt; col value format a30</NOBR><br /><NOBR>SQL&gt; set pagesize 100</NOBR><br /><NOBR>SQL&gt; select name, value, isspecified from v$spparameter</NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; where name in ('log_archive_dest', </NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'log_archive_duplex_dest', </NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'log_archive_dest_1', </NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'log_archive_dest_2', </NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'log_archive_format');</NOBR><br /><br /><B>Delete LOG_ARCHIVE_DEST parameter </B><br /><NOBR>SQL&gt; alter system reset LOG_ARCHIVE_DEST </NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0; scope = spfile sid = '*';</NOBR><br /><NOBR>SQL&gt; alter system reset LOG_ARCHIVE_DUPLEX_DEST </NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0; scope = spfile sid = '*';</NOBR><br /><br /><B>Set LOG_ARCHIVE_DEST_1</B><br /><NOBR>SQL&gt; alter system set LOG_ARCHIVE_DEST_1 =</NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0; 'LOCATION=&lt;path&gt;' scope = spfile;</NOBR><br /><br /><B>Set FRA parameter</B><br /><NOBR>SQL&gt; alter system set db_recovery_file_dest_size =</NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;db_recovery_file_dest_size&gt;;</NOBR><br /><NOBR>SQL&gt; alter system set db_recovery_file_dest =</NOBR><br /><NOBR>&#x00A0;&#x00A0;&#x00A0;&#x00A0; '&lt;db_recovery_file_dest&gt;';</NOBR><br /><NOBR>SQL&gt; shutdown immediate</NOBR><br /><NOBR>SQL&gt; startup</NOBR><br /><br /><B>Check FRA configuration</B><br />The following command shows an incorrect configuration from the viewpoint of the SAP BR*Tools backup strategy:<br /><br /><NOBR>SQL&gt; archive log list</NOBR><br /><NOBR>Database log mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Archive Mode</NOBR><br /><NOBR>Automatic archival&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Enabled</NOBR><br /><B><B><NOBR>Archive destination&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;USE_DB_RECOVERY_FILE_DEST</NOBR></B></B><br /><NOBR>Oldest online log sequence&#x00A0;&#x00A0;&#x00A0;&#x00A0; 132</NOBR><br /><NOBR>Next log sequence to archive&#x00A0;&#x00A0; 134</NOBR><br /><NOBR>Current log sequence&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 134</NOBR><br /><NOBR>SQL&gt;</NOBR><br /><br />In this configuration example, the archive destination points to USE_DB_RECOVERY_FILE_DEST. As a result, the system writes archive logs to the Fast Recovery Area.<br /><br />Reason: Parameter log_archive_dest_1 is not set.<br /><br />This is not compatible with the SAP BR*Tools backup strategy because BRARCHIVE assumes that the archive logs exist in log_archive_dest_1. After some time, an archiver stuck occurs (if the Flash Recovery Area is full) because BRARCHIVE does not back up any archive logs from the Flash Recovery Area.<br /><br />Solution: Correct the configuration accordingly.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000966073/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000966073/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "fra_setup.txt", "FileSize": "2", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000342132006&iv_version=0017&iv_guid=A194946CA956704085EF68800F05BB8C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "1125923", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle database flashback in BR*Tools", "RefUrl": "/notes/1125923"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3008457", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-38760: This database instance failed to turn on flashback database - NetWeaver", "RefUrl": "/notes/3008457 "}, {"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117 "}, {"RefNumber": "1125923", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle database flashback in BR*Tools", "RefUrl": "/notes/1125923 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}