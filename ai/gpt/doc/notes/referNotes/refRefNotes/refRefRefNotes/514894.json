{"Request": {"Number": "514894", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 424, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002455282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000514894?language=E&token=AF2F53929CAB7D30D988B8130A0C06F0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000514894", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000514894/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "514894"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MD-CON-IF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Exchange of Condition Records and Customizing Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "CRM-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Condition Technique", "value": "CRM-MD-CON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD-CON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Exchange of Condition Records and Customizing Data", "value": "CRM-MD-CON-IF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD-CON-IF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "514894 - Data type changes of MXWRT/GKWRT in table CDBC_P_CN_LIMI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use extractor object CRM_PRC_CNDLMT to extract pricing Customizing from CRM table PRCC_COND_LIMITS and transfer it to CDB table CDBC_P_CN_LIMITS.<br /><br />After the transfer, the values for attributes MXWRT and GKWRT in CDB table CDBC_P_CN_LIMITS are always initial independent of the original values in CRM table PRCC_COND_LIMITS.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>'Start initial load', 'Synchronize objects', CRM_PRC_CNDLMT,PRCC_COND_LIMITS, CDBC_P_CN_LIMITS, R3AS, R3AS4</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The data types of attributes MXWRT and GKWRT in the CDB table were designed incorrectly. The data types of the specified attributes are of type CURR with data type length 11 and 2 decimal places.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following corrections were made</p> <UL><LI>The data lengths of attributes MXWRT and GKWRT are increased from 11 to 28 in CDB table CDBC_P_CN_LIMITS. The number of decimal places is set to 9 respectively.<br /></LI></UL> <UL><LI>The data types of attributes MXWRT and GKWRT in the segment fields of the corresponding 'synchronized BDoc type' PRC_CUST_CNDLMT are adjusted to the changes for the CDB table.<br /></LI></UL> <UL><LI>For extractor object CRM_PRC_CNDLMT, the assignment of additional functions is reset from 'COMPARE NO ISO CODES' to 'COMPARE' using Transaction 'Middleware -&gt; Data exchange -&gt; Object Management -&gt; Condition objects' (R3AC5). This change occurs in the 'Object data' entry area for the 'Function' input field.<br /></LI></UL> <UL><LI>Currency amounts and percentages are stored according to the external display in the CDB and according to the internal display in the CRM Online database. The conversion occurs automatically for currency amounts, however, not for percentages. The conversion from the internal to the external display is also taken into account for percentages now.<br /></LI></UL> <p>Import the corresponding Support Package or carry out the following steps to provide the correction in advance:</p> <UL><LI>Partially, the required corrections cannot be implemented manually because, for example, there is no option to maintain SAP standard BDoc types using the 'Middleware -&gt; Development -&gt; BDoc type Modeler' (SBDM) incompatibly. Thus import the SAPSERV transport available for this note (to be found in the directory ftp://sapserv3/general/R3server/abap/note.514894/, Dateien: K071641.PIC (transport info file) R071641.PIC (R3Trans file)). In this context, consider Note 13719.<br /></LI></UL> <UL><LI>If you import the SAPSERV transport in advance, activate the change of extractor object CRM_PRC_CNDLMT using the 'Adapter Repository Wizard' Transaction (R3AWIZ).<br /></LI></UL> <UL><LI>Implement the source code corrections according to the attached correction instructions.</LI></UL> <p><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D034928)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000514894/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000514894/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "547705", "RefComponent": "CRM", "RefTitle": "SAPKU31001: Support Package 01 for CRM 3.1", "RefUrl": "/notes/547705"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "564529", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Condition customising adapter objects from CRM to CDB.", "RefUrl": "/notes/564529 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "547705", "RefComponent": "CRM", "RefTitle": "SAPKU31001: Support Package 01 for CRM 3.1", "RefUrl": "/notes/547705 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "310", "To": "310", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "350", "To": "350", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 300", "SupportPackage": "SAPKU30010", "URL": "/supportpackage/SAPKU30010"}, {"SoftwareComponentVersion": "BBPCRM 310", "SupportPackage": "SAPKU31001", "URL": "/supportpackage/SAPKU31001"}, {"SoftwareComponentVersion": "BBPCRM 350", "SupportPackage": "SAPKU35002", "URL": "/supportpackage/SAPKU35002"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BBPCRM", "NumberOfCorrin": 2, "URL": "/corrins/0000514894/63"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}