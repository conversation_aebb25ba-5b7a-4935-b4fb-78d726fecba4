{"Request": {"Number": "1174130", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 529, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016526922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001174130?language=E&token=8FCD2E1623E704E8522DF11DE5CD0BF6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001174130", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1174130"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "29.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "FS-PE"}, "SAPComponentKeyText": {"_label": "Component", "value": "Payment Engine"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payment Engine", "value": "FS-PE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-PE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1174130 - Consistency Checks"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>First set of Consistency Checks for Business Objects</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Before you can implement the attached transport you have to make sure that Note 1173544 and Note 1173837 is implemented. The order of implementing the two mentioned note is important as well. Start with 1173544.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This report is only meant to be used by SAP and it is guaranteed to work only immediately before going live of the pilot customer. SAP will keep these reports up-to-date - only if required.<br /><br />Apply the provided solution. You will find the transport which you have to implement as attachment to this note. The steps you have to perform are decribed in SAP Note 13719.<br /><br />After implementing the attached transport You have to append or modificate the structure /PE4/PE_PP_STR_PARM_TRANS.<br /><br />Therefor you have to perform the following steps:</p> <UL><LI>Go to transaction SE11</LI></UL> <UL><LI>Enter /PE4/PE_PP_STR_PARM_TRANS as structure</LI></UL> <UL><LI>Append the structure /PE1/STR_TRANS_CONSITENCY to the given one</LI></UL> <UL><LI>Save and activate</LI></UL> <p><br />You also have to do some steps in the customizing to integrate the new worker class for the paralization framework:</p> <UL><LI>Go to transaction SM30</LI></UL> <UL><LI>Enter /PE4/PE_PPA as table/view name</LI></UL> <UL><LI>Press display</LI></UL> <UL><LI>Delete this entry PEPPTEMPL HDR SINGLE_CLARA</LI></UL> <UL><LI>Save</LI></UL> <p></p> <UL><LI>Go to transaction SM30</LI></UL> <UL><LI>Enter /PE1/TS_PP_ACT as table/view name</LI></UL> <UL><LI>Press display</LI></UL> <UL><LI>Add the following entries to the table</LI></UL> <OL>1. </OL> <UL><UL><LI>Appl. Type: PEPPTEMPLE</LI></UL></UL> <UL><UL><LI>ObjectTyp: PEP2</LI></UL></UL> <UL><UL><LI>proc.: 1</LI></UL></UL> <UL><UL><LI>activity cl. order: /PE1/CL_PE_PP_PROXY_ARFC</LI></UL></UL> <UL><UL><LI>activity cl. item: /PE1/CL_PE_PP_PROXY_ARFC</LI></UL></UL> <UL><UL><LI>work class: /PE1/CL_PO_E_CONSISTENCY_PP</LI></UL></UL> <OL>2. </OL> <UL><UL><LI>Appl. Type: PEPPTEMPLE</LI></UL></UL> <UL><UL><LI>ObjectTyp: PEPP</LI></UL></UL> <UL><UL><LI>proc.: 1</LI></UL></UL> <UL><UL><LI>activity cl. order: /PE1/CL_PE_PP_PROXY_ARFC</LI></UL></UL> <UL><UL><LI>activity cl. item: /PE1/CL_PE_PP_PROXY_ARFC</LI></UL></UL> <UL><UL><LI>work class: /PE1/CL_PO_E_CONSISTENCY_PP</LI></UL></UL> <UL><UL><LI></LI></UL></UL> <UL><UL><LI></LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D050629"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D041667)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001174130/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "consistency.zip", "FileSize": "50", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000284522008&iv_version=0003&iv_guid=02A1DCFD3D9A574C8D7A69FDC5BC4E32"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1273604", "RefComponent": "FS-PE", "RefTitle": "Third set of Consistency Checks", "RefUrl": "/notes/1273604"}, {"RefNumber": "1174730", "RefComponent": "FS-PE", "RefTitle": "Second set of Consistency Checks", "RefUrl": "/notes/1174730"}, {"RefNumber": "1173837", "RefComponent": "FS-PE", "RefTitle": "PPF-PE: optimize adjust selection", "RefUrl": "/notes/1173837"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1273604", "RefComponent": "FS-PE", "RefTitle": "Third set of Consistency Checks", "RefUrl": "/notes/1273604 "}, {"RefNumber": "1174730", "RefComponent": "FS-PE", "RefTitle": "Second set of Consistency Checks", "RefUrl": "/notes/1174730 "}, {"RefNumber": "1173837", "RefComponent": "FS-PE", "RefTitle": "PPF-PE: optimize adjust selection", "RefUrl": "/notes/1173837 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PAY-ENGINE", "From": "230", "To": "230", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}