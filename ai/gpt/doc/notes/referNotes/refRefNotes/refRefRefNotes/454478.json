{"Request": {"Number": "454478", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 516, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015133272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000454478?language=E&token=D27C74395CFF2E86059BB3D99E249A30"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000454478", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000454478/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "454478"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.10.2002"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP-AP-Q1"}, "SAPComponentKeyText": {"_label": "Component", "value": "Withholding Tax (Reporting)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AP-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Withholding Tax (Reporting)", "value": "FI-AP-AP-Q1", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP-Q1*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "454478 - New 1042 record format and form for year 2002"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>New record format and form for 1042; Foreign person's U.S. source income subject to Withholding.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RFKQSU40, F_RFKQSU40_10</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legal change</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1) The modified report and SAPScript form for release 3.1I and release 4.0B and above is available on SAPSERV (see note 13719 for more information concerning SAPSERV transports)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 3.1I<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;directory:&#x00A0;&#x00A0;/general/R3server/abap/note.0492783<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport:&#x00A0;&#x00A0; U3IK900633<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 4.*<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;directory:&#x00A0;&#x00A0;/general/R3server/abap/note.0454478<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport:&#x00A0;&#x00A0; U4BK901615<br /><br />NOTE:<br />After you download the report from above SAPserv, please implement<br />note 494042.<br /><br />if you have release 45B and up, after download the report from above<br />SAPserv, please add the following code in FIWT0010.<br /><br />*&amp;---------------------------------------------------------------------*<br />*&amp;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Form&#x00A0;&#x00A0;read_t059p<br />*&amp;---------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Determines whether WT type is type with posting at invoice or<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; at payment time.<br />*----------------------------------------------------------------------*<br />form read_t059p tables i_t059p structure t059p<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;using value(i_land1) like t001-land1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;value(i_witht) like with_item-witht.<br /><br />&#x00A0;&#x00A0;clear i_t059p.<br />&#x00A0;&#x00A0;read table i_t059p with key land1 = i_land1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;witht = i_witht.<br />&#x00A0;&#x00A0;if sy-subrc &lt;&gt; 0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;select single * from t059p where land1 = i_land1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and witht = i_witht.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if sy-subrc = 0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;i_t059p = t059p.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;append i_t059p.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<br />&#x00A0;&#x00A0;endif.<br /><br />endform.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\" read_t059p<br /><br /><br />2) The modified report and SAPScript form&#x00A0;&#x00A0;will be available in next &#x00A0;&#x00A0; support package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-GL-D (Reporting/Analysis/Display)"}, {"Key": "Transaction codes", "Value": "CLEAR"}, {"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Responsible                                                                                         ", "Value": "D031478"}, {"Key": "Processor                                                                                           ", "Value": "D031478"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000454478/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "510748", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modification three for new 1042 for year 2002", "RefUrl": "/notes/510748"}, {"RefNumber": "497879", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modification two for new 1042 for year 2002", "RefUrl": "/notes/497879"}, {"RefNumber": "494042", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "rfkqsu40 database BSAK problem", "RefUrl": "/notes/494042"}, {"RefNumber": "492783", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modification one for new 1042 for year 2002", "RefUrl": "/notes/492783"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "670407", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1042S Reporting", "RefUrl": "/notes/670407 "}, {"RefNumber": "494042", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "rfkqsu40 database BSAK problem", "RefUrl": "/notes/494042 "}, {"RefNumber": "510748", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modification three for new 1042 for year 2002", "RefUrl": "/notes/510748 "}, {"RefNumber": "497879", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modification two for new 1042 for year 2002", "RefUrl": "/notes/497879 "}, {"RefNumber": "492783", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modification one for new 1042 for year 2002", "RefUrl": "/notes/492783 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I97", "URL": "/supportpackage/SAPKH31I97"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B71", "URL": "/supportpackage/SAPKH40B71"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B55", "URL": "/supportpackage/SAPKH45B55"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B43", "URL": "/supportpackage/SAPKH46B43"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C37", "URL": "/supportpackage/SAPKH46C37"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}