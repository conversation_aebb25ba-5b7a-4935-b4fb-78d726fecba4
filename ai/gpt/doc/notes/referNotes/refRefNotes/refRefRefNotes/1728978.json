{"Request": {"Number": "1728978", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 509, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017454152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001728978?language=E&token=F60DFC621D286930908FB85DBA1D0267"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001728978", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001728978/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1728978"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.10.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1728978 - Guided Self Service Transport Execution Analysis for Project"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />As of SAP Solution Manager 7.0 with support package stack 23 or SAP Solution Manager 7.1 a self service is available to analyze a set of transport requests before it is imported into a target system. It identifies wrong transport sequences and potential import errors before they occur and gives recommendations to avoid them.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Transport Execution Analysis for Projects, Guided Self Service, GSS_TEAP, GSS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br /><strong>Requirements in the managed systems (development, reference and target system):</strong></p>\r\n<ul>\r\n<li>ST-PI 2008.1 with SP6 or higher.</li>\r\n<li>In addition implement the following notes:</li>\r\n<ul>\r\n<li>For ST-PI 2008.1 SP6: Implement note 1738183</li>\r\n<li>For ST-PI 2008.1 SP7: Implement note 1781984</li>\r\n<li>For ST-PI 2008.1 SP8: Implement note 1870040</li>\r\n<li>For ST-PI 2008.1 SP9: Implement note 1978770</li>\r\n<li>For ST-PI 2008.1 SP10: Implement note 2062253</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Requirements in SAP Solution Manager:</strong></p>\r\n<ul>\r\n<li>Solution Manager 7.0 with support package stack 23 or higher or Solution Manager 7.1.</li>\r\n<li>Depending on your Solution Manager release you must implement the following notes in addition:</li>\r\n<ul>\r\n<li>Release 7.0 SP23/24: Notes 1491227, 1517658 and 1550309</li>\r\n</ul>\r\n<ul>\r\n<li>Release 7.0 SP25/26: Note 1550309</li>\r\n</ul>\r\n<ul>\r\n<li>Release 7.1 SP3/4: Note 1674913</li>\r\n</ul>\r\n<li>Solution Tool Plugins ST-A/PI 01P or higher and ST-PI 2008.1 with SP6 or higher.</li>\r\n<li>In addition implement the following notes:</li>\r\n<ul>\r\n<li>For ST-A/PI 01P or ST-A/PI 01Q without support package or with support package 1: Implement note 1721643. Execute the manual activities of note 1721643 before installation with SNOTE.</li>\r\n<li>For ST-A/PI 01Q with support package 2: Implement note 2041971</li>\r\n<li>For ST-A/PI 01R without support package: Implement note 2080429</li>\r\n<li>For ST-PI 2008.1 SP6: Implement note 1738183</li>\r\n<li>For ST-PI 2008.1 SP7: Implement note 1781984</li>\r\n<li>For ST-PI 2008.1 SP8: Implement note 1870040</li>\r\n</ul>\r\n<li>The managed systems must be available in transaction SMSY and READ RFC connections must exist from SAP Solution Manager to the managed systems (development, reference and target system).</li>\r\n<li>In the development, reference and target system, the communication user of the READ RFC&#160;connections&#160;must be updated with the latest authorization profile SAP_SOLMAN_READ (for SAP_BASIS 701 and lower) or SAP_SOLMAN_READ_702 (for SAP_BASIS 702 and higher). These profiles&#160;are in the attachment of SAP Note 1830640. Upload these profiles in transaction PFCG of the managed systems, generate them, and assign them to the user which is maintained in the READ RFC connections.</li>\r\n<li>In the Solution Manager the user which executes the self service must have sufficient authorizations to access the workcenter \"SAP Engagement and Service Delivery\". This authorization is included in the role SAP_SOLMAN_ONSITE_COMP (see note 872800). In addition this user needs the authorization to access transaction ST14 (authorization object S_TCODE) and to start a background job.</li>\r\n<li>Dynamic content update must be activated as described in note 1143775.</li>\r\n<li>Release 7.0 SP23 to 28 and release 7.1 SP1 to SP4: If the GSS_TEAP service is not available in your Solution Manager you can download it via the content update. Go to transaction AGS_UPDATE and enter +GSS_TEAP in the OK code field. After that apply the newly downloaded content update.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>If the requirements are fulfilled, you can start the service in the \"SAP Engagement and Service Delivery\" workcenter. Depending on the release of the SAP Solution Manager, the execution of the self service is slightly different.</p>\r\n<p><strong>SAP Solution Manager is on release 7.1</strong></p>\r\n<p>Using transaction \"sm_workcenter\", the work center \"SAP Engagement and Service Delivery\" can be opened. By pressing \"Services\", an overview of the existing services for the selected \"Solution\" is given. Pressing \"Create\" allows the creation of the Guided Self Service - Transport Execution Analysis for Project (GSS_TEAP). A Guided Procedure appears with the steps (1) \"Prepare\" (2) \"Analysis\" (3) \"Report\" and (4) \"Follow Up\". In the attachment of this note you can find the document GSS_TEAP_SM71SPS5.pdf. This is a detailed step by step description which guides you through the service execution.</p>\r\n<p><strong>SAP Solution Manager is on release 7.0 with support package stack 23 or higher</strong></p>\r\n<p>Like in case of a SAP Solution Manager 7.1, the service can be created in the work center \"SAP Engagement and Service Delivery\". However, after the service is created, the session will not be opened automatically. Instead, it is necessary first to click the triangle symbol in front of the service name. After this is done, a new line with the session title \"Transport Execution Analysis for Projects\" appears. This line has to be marked in order to see the \"Details of Service\". In the tab \"Sessions\" of the \"Details of Service\", the button \"Create Questionnaire\" needs to be pressed to create a second entry \" Guided Self Service: Transport Execution Analysis for Projects - Guided Preparation\". Click on the Guided Preparation to navigate into the questionnaire session. In the attachment of this note you can find the document GSS_TEAP_SM70SPS29.pdf. This is a detailed step by step description which guides you through the service execution.<br /><br />More information about Guided Self Services is available in the SAP Service Marketplace under http://service.sap.com/gss.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031384)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001728978/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001728978/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "GSS_TEAP_SM70SPS29.pdf", "FileSize": "971", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000292692012&iv_version=0009&iv_guid=05AF9EFDDB6ADF4A8BE1D7EA613D0664"}, {"FileName": "GSS_TEAP_SM71SPS5.pdf", "FileSize": "986", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000292692012&iv_version=0009&iv_guid=1C21224BF3BF18438A74121118FDF574"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1830640", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users 7.1 SP09 - 7.2 SP01 set", "RefUrl": "/notes/1830640"}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183 "}, {"RefNumber": "1143775", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "SAP Service Content Update", "RefUrl": "/notes/1143775 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-SER", "From": "701_2010_1", "To": "701_2010_1", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}