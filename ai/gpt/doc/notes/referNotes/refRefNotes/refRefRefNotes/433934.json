{"Request": {"Number": "433934", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 326, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002024142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000433934?language=E&token=82B171D0D88C4FCDA16C792C7C4AD6A9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000433934", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000433934/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "433934"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.04.2002"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MD-CON-IF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Exchange of Condition Records and Customizing Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "CRM-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Condition Technique", "value": "CRM-MD-CON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD-CON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Exchange of Condition Records and Customizing Data", "value": "CRM-MD-CON-IF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MD-CON-IF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "433934 - Transfer ddic info for condition objects to mobile client"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Texts of the data-dictionary objects like, data-element ,domain and fixed values and thier texts, not available in the IPC display panel.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SPE_DDIC_WRITE, Data-dictionary text.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>One of the causes may be that the texts are not downloaded, to the appropriate tables. The IPC access to these tables will not get proper texts to display.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p> On the mobile cleint the information related to the texts in various<br />languages of the DATA-ELEMENTs and&#x00A0;&#x00A0;DOMAINs of the FIELDs used in<br />pricing, should be downloaded in order that the pricing panel displays it on the mobile cleint. This displaying of the information is<br />done by IPC. But IPC needs information about texts, in the appropriate<br />tables. And this information has to be downloaded from the CRM online<br />database to the CDB and from CDB it should be transferred to mobile<br />client so that&#x00A0;&#x00A0;the texts are displayed in pricing panel during pricing<br />analysis on the mobile cleint.<br /><br />To download this information on to mobile cleint please<br />Implement the correction instruction attached with this note.<br /><br />To do this first create the function group CND_CUST_COND_FLOW_DB in<br />Package CND_CUST_COND_FLOW. Under this function group create the function modules and Forms as given in the correction insturction.<br />&#x00A0;&#x00A0;After creating this , add the following messages to CND_MAST.<br />800 - No entries in table /SAPCND/T681 with Gesta = 5.<br />In the long text fill in these details too.<br />Diagnosis - For the usage &amp;V1&amp; and Application &amp;V2&amp; no<br />entries exist in the table /SAPCND/T681 with Generation status GESTA = 5.<br />System response - No data for condition tables are extracted.<br />Procedure - Check the customizing of the condition tables.<br />Procedure for system administration - *<br /><br />801 - The table &amp;1 does not exists.<br /> Mark the self-explanatory check box.<br /><br />802 - The sboc &amp;1 does not exists.<br /> Mark the self-explanatory check box.<br /><br />803 - For the usage &amp;1 there is no scales.<br /> Mark the self-explanatory check box.<br /><br />804 - The determination of table names failed.<br />In the long text fill in these details too.<br />Diagnosis - The determination of the table names for usage &amp;V1&amp; ,<br />Application &amp;V2&amp;&#x00A0;&#x00A0; and Dimension &amp;V3&amp;&#x00A0;&#x00A0;failed.<br /><br />805 - The communication sturucture does not exists for the application<br />&amp;1.<br />&#x00A0;&#x00A0;Mark the self-explanatory check box.<br /><br />806 - NO entries in the internal table&#x00A0;&#x00A0;for the selection from<br />dictionary.<br />In the long text fill in these details too.<br />Diagnosis - The dictionary information is read for the tables which are in the internal table. Only for the tables in this internal table the<br />dictionary information is read. Further for these tables the SBDOC<br />should exists Othewise the dictionary information will not be determined. In this case no entries were found in the internal table.<br />System response - Abort.<br />Procedure - Check the customizing of the condition technique and<br />the generated tables. IF necessary the genearation Report must be run.<br /><br />807 - For the table &amp;1 , no entries exists in table DD03P.<br />In the long text fill in these details too.<br />Diagnosis - The function 'DDIF_TABL_GET' did not find the entires in the dictionary table DD03P for the table &amp;V1&amp;.<br />System response - The execution should be aborted for this table.<br />Procedure - Check the consistency of the dictionary for the indicated table.<br />Procedure for system administration - *.<br /><br />808 - There exists entries in the table DD03P with Tablename = Initial.<br />In the long text fill in these details too.<br />Diagnosis - While determining entries for the table &amp;V1&amp;&#x00A0;&#x00A0;in table<br />DD03P some entries with field TABNAME = initial were determined.<br />System response - These entries are deleted and system gives warning.<br />Procedure - Check the consistency of the dictionary for the table<br />&amp;V1&amp;.<br />Procedure for system administration - *.<br /><br />809 - The internal table for the determination&#x00A0;&#x00A0;of the details&#x00A0;&#x00A0;of DDIC<br />objects is empty.<br />In the long text fill in these details too.<br />Diagnosis - Based on the entries in the internal table the dictionary<br />details for the data elements and domains are determined. In this case,<br />this internal table was empty.<br />System response - The determination is aborted and process is stopped.<br />Procedure - Check the customizing for the condition tables and consistency of dictionary of the conditions table itself. Also, check<br />for the existence of SBDOC for the condition tables. These are pre<br />requisite for the determination of dictionary data.<br />Procedure for system administration - *.<br /><br />810 - Entries exists in table with rollname = Initial or domname = Initial.<br />In the long text fill in these details too.<br />Diagnosis - For the determination of the data dictionary details entries<br />of DD03L were selected.&#x00A0;&#x00A0;Based on these entires&#x00A0;&#x00A0;the apporpriate&#x00A0;&#x00A0;data<br />elements ( roll name )&#x00A0;&#x00A0;and domains are selected. In this case, entries<br />in table DD03L exists with rollname or domname as initial.<br />System response - These entries will be removed before futher<br />selection.<br />Procedure - Check the consistency of the condition table in Data dictionary.<br />Procedure for system administration - *<br /><br />811 - Trans ID of the BDOC 'CND_CUST_SPE_DDIC_WRITE' could not be determined.<br />Mark the self-explanatory checkbox.<br /><br />812 - Logical system could not be determined from the table 'SMOF_ERPSH'.<br />Mark the self-explanatory check box.<br /><br />813 - Logical system in the table SMOF_ERPSH is not unique.<br />Mark the self-explanatory check box.<br /><br />814 - No entry in SMOFDSTAT for the REF_ID &amp;1.<br />Mark the self-explanatory check box.<br /><br />815 - No entry in SMOFRSTAT for the REF_ID &amp;1.<br />Mark the self-explanatory check box.<br /><br />816 - Error in call of function SMOF0_GENERAL_DATA_HANDLER.<br />Mark the self-explanatory check box.<br /><br />2. Create the following CDB tables through transaction se11<br />&#x00A0;&#x00A0;The following&#x00A0;&#x00A0;attributes is common to all the tables .<br />Fill in the Package as CDB.<br />Delivary class&#x00A0;&#x00A0;is W<br />Data Browser/Table view Maint. Display/maintainance allowed<br /><br /> Table name - CDBC_C_DD01L<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />SFADD01L&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBT_CND_GUID<br />DOMNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMNAME<br />DATATYPE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DATATYPE_D<br />LENG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DDLENG<br />OUTPUTLEN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OUTPUTLEN<br />DECIMALS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DECIMALS<br />LOWERCASE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LOWERCASE<br />SIGNFLAG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SIGNFLAG<br />VALEXI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VALEXI<br />ENTITYTAB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENTITYTAB<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />Table Name - CDBC_C_DD01T<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />SFADD01T&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CDBT_CND_GUID<br />DOMNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMNAME<br />DDLANGUAGE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMO_LANGU<br />DDTEXT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AS4TEXT<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />Table Name - CDBC_C_DD03L<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />SFADD03L&#x00A0;&#x00A0;&#x00A0;&#x00A0; YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBT_CND_GUID<br />TABNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TABNAME<br />FIELDNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIELDNAME<br />ROLLNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ROLLNAME<br />DOMNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMNAME<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />Table Name - CDBC_C_DD04L<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0; YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; MANDT<br />SFADD04L&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CDBT_CND_GUID<br />ROLLNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ROLLNAME<br />DOMNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMNAME<br />ROUTPUTLEN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DDLENG<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />Table Name - CDBC_C_DD04T<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br /><br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />SFADD04T&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBT_CND_GUID<br />ROLLNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ROLLNAME<br />DDLANGUAGE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMO_LANGU<br />DDTEXT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AS4TEXT<br />REPTEXT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REPTEXT<br />SCRTEXT_S&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SCRTEXT_S<br />SCRTEXT_M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SCRTEXT_M<br />SCRTEXT_L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SCRTEXT_L<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />Table Name - CDBC_C_DD07L<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />SFADD07L&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBT_CND_GUID<br />DOMNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMNAME<br />VALPOS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VALPOS<br />DOMVALUE_L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMVALUE_L<br />DOMVALUE_H&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMVALUE_H<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />Table Name - CDBC_C_DD07T<br /><br />FIELD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; KEY&#x00A0;&#x00A0; Initial values&#x00A0;&#x00A0;&#x00A0;&#x00A0;data Element<br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />SFADD07T&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBT_CND_GUID<br />DOMNAME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMNAME<br />DDLANGUAGE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMO_LANGU<br />VALPOS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VALPOS<br />DDTEXT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VAL_TEXT<br />DOMVAL_LD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMVAL_LD<br />DOMVAL_HD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMVAL_HD<br />DOMVALUE_L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOMVALUE_L<br />.INCLUDE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SMOCOMMFDS<br /><br />3. With the Transaction SM30, insert an entry in table CRMSUBTAB<br />as follows.<br />Consumer&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDB<br />Obj. Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CRM_CUST_CNDDIC<br />Dwnl.typ&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;D<br />Objt Class&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CONDCUSTOMIZING<br />Function<br />ObjType<br />Function module CND_SPE_DDIC_CUST_EXTRACT<br />Don't Mark the inactiv checkbox.<br /><br />4. Now create the download object, using the transaction R3AC5, and<br /> enter the following details.<br /><br />Object Name : CRM_CUST_CNDDIC<br />Object Class: CONDCUSTOMIZING<br />Linked BDOC : CND_CUST_SPE_DDIC_WRITE<br />Block SIZE&#x00A0;&#x00A0;: 0<br />Inactive object : Dont mark this check box<br />FUNCTION : COMPARE<br /><br /> Now, In the Tab INITIAL FLOW CONTEXTS , go and select&#x00A0;&#x00A0;CRM from the<br />drop down selection available for the field SOURCE SITE TYPE.<br /><br />In the TAB TABLES/STRUCTURES&#x00A0;&#x00A0;Fill in these details<br /><br />Table(source site)&#x00A0;&#x00A0; Main table/struc&#x00A0;&#x00A0; Mapped&#x00A0;&#x00A0;structure (target site)<br />DD01L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD01L<br />DD01T&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD01T<br />DD03L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD03L<br />DD04L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD04L<br />DD04T&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD04T<br />DD07L&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD07L<br />DD07T&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CDBC_C_DD07T<br /><br />IN the tab&#x00A0;&#x00A0;Mapping Modules Fill in the following<br />Module Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;call order<br />CND_SPE_DDIC_MAP_CUSTOMIZING&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.2<br /><br />Save and activate the Download object.<br /><br />5.Import the BDOC CND_CUST_SPE_DDIC_WRITE from server SAPSERV3&#x00A0;&#x00A0;from the<br />location given in the note 485633. To import the BDOC from the server<br />please refer to note 13719.<br /><br />Additional comments:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; The data downloaded, is usually large and this might take a bit<br />time, depending on the number of condition tables, you are using.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; This object needs to be download only once, and everytime when a<br />new condition table is created and transfered on to mobile client.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "CLEAR"}, {"Key": "Transaction codes", "Value": "MOVE"}, {"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "SE11"}, {"Key": "Transaction codes", "Value": "SM30"}, {"Key": "Transaction codes", "Value": "DICTIONARY"}, {"Key": "Transaction codes", "Value": "FULL"}, {"Key": "Transaction codes", "Value": "R3AC5"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I062006)"}, {"Key": "Processor                                                                                           ", "Value": "I043163"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000433934/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000433934/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "540640", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "To download the DDIC texts description for table PRCD_COND", "RefUrl": "/notes/540640"}, {"RefNumber": "485633", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Synchronization BDoc - Data exchange( pricing & conditions)", "RefUrl": "/notes/485633"}, {"RefNumber": "452079", "RefComponent": "CRM-MSA-ADP", "RefTitle": "Customising Object SPE_DDIC_WRITE inactive in CRM 3.0 SP05", "RefUrl": "/notes/452079"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "540640", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "To download the DDIC texts description for table PRCD_COND", "RefUrl": "/notes/540640 "}, {"RefNumber": "485633", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Synchronization BDoc - Data exchange( pricing & conditions)", "RefUrl": "/notes/485633 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "452079", "RefComponent": "CRM-MSA-ADP", "RefTitle": "Customising Object SPE_DDIC_WRITE inactive in CRM 3.0 SP05", "RefUrl": "/notes/452079 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 300", "SupportPackage": "SAPKU30007", "URL": "/supportpackage/SAPKU30007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "BBPCRM", "NumberOfCorrin": 1, "URL": "/corrins/0000433934/63"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}