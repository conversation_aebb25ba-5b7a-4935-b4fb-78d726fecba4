{"Request": {"Number": "2154991", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1525, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018050532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002154991?language=E&token=7B7DFE4A0321D5C90D9B87D27A8F7F5E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002154991", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2154991"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.04.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2154991 - <PERSON>id Node Checking For Registration (VNCR)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Valid Node Checking For Registration (VNCR) is a new security feature in Oracle Net ******** and 12c which allows instance registrations to only come from known servers.</p>\r\n<p>Oracle recommends using the VNCR feature in ******** and 12c as an alternative to COST (Class Of Secure Transports)&#160;if the implementation is only to regulate database service registration requests with Listeners. If COST parameters are needed for Oracle Database ******** or 12c for other or for additional reasons, then they should be used as intended.<br />Information about the COST implementation can be found in these SAP Notes:</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/1714255\">1714255 - Restrict Instance Registration in non-RAC environments</a></li>\r\n<li><a target=\"_blank\" href=\"/notes/1714667\">1714667 - Restrict Instance Registration in RAC environments</a></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> The feature VNCR is independent of the validnode checking that clients use (TCP.VALIDNODE_CHECKING). For this feature please refer to SAP Note <a target=\"_blank\" href=\"/notes/186119\">186119 - Restricting DB access to specific hosts</a>&#160;.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Listener, lsnrctl, security, listener.ora, TNS-01182, Oracle ********, Oracle 12.1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Without proper configuration a 12c listener (by default) will not allow remote servers to register their database instances.&#160; Remote registrations will result in a TNS-01182 being logged in the listener.log file.&#160; This is normal behavior as the listener will only allow registrations from instances that are on the same machine as the listener.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To allow remote instances to register with a 12c listener, enable VNCR in the listener.ora.</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> With Oracle ********, the VALID_NODE_CHECKING_REGISTRATION_&lt;listener_name&gt; default is OFF and must be set to ON<br />in order to enable the ability of blocking remote registration attempts.</p>\r\n<p><strong>VNCR parameters</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\"><strong>Parameter</strong></span></td>\r\n<td><strong><span style=\"text-decoration: underline;\">Value</span></strong></td>\r\n<td><span style=\"text-decoration: underline;\"><strong>Comment</strong></span></td>\r\n</tr>\r\n<tr>\r\n<td>VALID_NODE_CHECKING_REGISTRATION_&lt;listener_name&gt;</td>\r\n<td>\r\n<ul>\r\n<li>OFF/0</li>\r\n<li>ON/1/LOCAL</li>\r\n<li>SUBNET/2</li>\r\n</ul>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>Disables VNCR</li>\r\n<li>The <strong>default</strong>. Enables VNCR. All local machine IP's can register.</li>\r\n<li>All machines in the subnet are allowed registration.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>REGISTRATION_INVITED_NODES_&lt;listener-name&gt;</td>\r\n<td>Values are valid IPs, valid hosts, a subnet using <a target=\"_blank\" href=\"http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">CIDR notation</a> (for ip4/6), or wildcard (*) for ipv4.</td>\r\n<td>For example: REGISTRATION_INVITED_NODES_Listener=(net-vm1, *************, 127.42.5.*)</td>\r\n</tr>\r\n<tr>\r\n<td>REGISTRATION_EXCLUDED_NODES_&lt;listener_name&gt;</td>\r\n<td>&nbsp;</td>\r\n<td>Parameter has the&#160;inverse behavior&#160;of REGISTRATION_INVITED_NODES</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> When an INVITED list is set, it will automatically include the machine's local IP in the list.&#160;There is no need to include it.</p>\r\n<p>&#160;</p>\r\n<p>For the setup on RAC systems please refer&#160;to SAP note <a target=\"_blank\" href=\"/notes/2154993\">2154993 - How to Enable VNCR on RAC Database to Register only Local Instances</a> .</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5014980)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5000979)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002154991/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002154991/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2154993", "RefComponent": "BC-DB-ORA", "RefTitle": "How to Enable VNCR on RAC Database to Register only Local Instances", "RefUrl": "/notes/2154993"}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094"}, {"RefNumber": "1714667", "RefComponent": "BC-DB-ORA", "RefTitle": "Restrict Instance Registration in RAC environments", "RefUrl": "/notes/1714667"}, {"RefNumber": "1714255", "RefComponent": "BC-DB-ORA", "RefTitle": "Restrict Instance Registration in non-RAC environments", "RefUrl": "/notes/1714255"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}, {"RefNumber": "2154993", "RefComponent": "BC-DB-ORA", "RefTitle": "How to Enable VNCR on RAC Database to Register only Local Instances", "RefUrl": "/notes/2154993 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}