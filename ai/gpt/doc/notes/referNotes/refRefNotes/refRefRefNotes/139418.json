{"Request": {"Number": "139418", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 424, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014648172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000139418?language=E&token=C8B2C80CEE73D0710D9F758847D3540F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000139418", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000139418/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "139418"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Security - Read KBA 2985997 for subcomponents"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "139418 - Logging of user actions (ABAP server)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to record the activities of users in an ABAP-based system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Logging, table changes, statistical data, syslog, security audit log, read access logging, SQL audit log</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are looking for different possibilities to log user activities in the SAP system, among other things to<br /> - understand transactions and changes<br /> - execute DP audits<br /> - analyze security incidents in the system<br /> - detect security holes<br /> - optimize the security settings</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In the standard SAP System, extensive functions exist for logging user activities and changes to the system. You must use them selectively to record the required data specifically, and at the same time to facilitate their efficient evaluation and utilization. When you log user activities you must generally note that</p>\r\n<ul>\r\n<li>Existing data protection laws are not violated (for example, German Data Protection Act). </li>\r\n<li>In certain cases, recording is permitted only when approved by the data protection officer and an employee representative and is additionally subject to the regulations of a company agreement.</li>\r\n<li>Large datasets can develop very quickly whose storage and evaluation ties up technical and organizational resources.</li>\r\n</ul>\r\n<p><br />In general, the following recordings are available in the standard SAP system for tracking user actions:</p>\r\n<ul>\r\n<li><a target=\"_self\" href=\"#SYSLOG\">System log</a></li>\r\n<li><a target=\"_self\" href=\"#SAL\">Security Audit Log</a></li>\r\n<li><a target=\"_self\" href=\"#RAL\">Read access log</a></li>\r\n<li><a target=\"_self\" href=\"#CDHDR\">General change documents</a></li>\r\n<li><a target=\"_self\" href=\"#DBTABLOG\">Generic logging of table content changes</a></li>\r\n<li><a target=\"_self\" href=\"#ST03N\">Performance statistics on user behavior</a></li>\r\n<li><a target=\"_self\" href=\"#SE80\">Version management in ABAP Workbench</a></li>\r\n<li><a target=\"_self\" href=\"#SE09\">Transport logs</a></li>\r\n<li><a target=\"_self\" href=\"#SM39\">Background processing logs</a></li>\r\n<li><a target=\"_self\" href=\"#XXXX\">Application-specific change documents</a></li>\r\n<li><a target=\"_self\" href=\"#TTRACE\">Technical traces</a></li>\r\n</ul>\r\n<p>SAP Note 2423576 provides transaction SAIS_MONI, which supports a summarized, time-stream related evaluation for selected recording types.</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>\r\n<p><strong>Short information on selected recording types</strong></p>\r\n<p><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"SYSLOG\"></a><strong>&#xFEFF;System log (BC-CCM-MON-SLG)</strong></span></p>\r\n<p><span style=\"font-size: 14px;\">The system log logs (fault) events related to the operation of the ABAP instance. The log is file-based and is written on a rolling basis. <br />The evaluation is carried out using transaction SM21, among others.<br />&#x00A0;</span></p>\r\n<p><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"SAL\"></a>&#x00A0;<strong>&#xFEFF;Security Audit Log (BC-SEC-SAL)</strong></span></p>\r\n<p><span style=\"font-size: 14px;\">According to its configuration, Security Audit Log (SAL) records potentially security-relevant events in a persistent, proprietary log format. Before SAP Basis Version 7.50 SP03, FAQ note 539404 can be used as a guide for configuration and evaluation. For newer versions, SAP Note 2191612 contains further recommendations.&#x00A0;<br /></span>As of SAP Note 2883981, transaction RSAU_READ_LOG_ADM provides a pseudonymized evaluation of the SAL to facilitate the check of the log for administration purposes.&#x00A0;<br />The SQL audit is no longer available as of SAP Basis Release 7.40; see SAP Note 2246878. Use the read access log instead.</p>\r\n<p>&#x00A0;<a target=\"_blank\" name=\"RAL\" style=\"background-color: #ffffff;\"></a><strong>&#xFEFF;Read access log (BC-SEC-RAL)</strong></p>\r\n<p>The optional Read Access Logging (RAL) is used to monitor and record read access to sensitive data. Read Access Logging is always based on a purpose that must be defined according to the requirements of an organization (such as data protection and privacy). The Read Access Logging framework can be used to meet legal or other requirements, detect fraud or data theft, or conduct audits, and is also suitable for other internal purposes.&#x00A0;See the online documentation for more information.</p>\r\n<p><strong><a target=\"_blank\" name=\"CDHDR\" style=\"background-color: #ffffff;\"></a>General change documents (BC-SRV-ASF-CHD)</strong></p>\r\n<p><span style=\"font-size: 14px;\">In accordance with the generally accepted accounting principles (documentation requirement) and other legal bases, changes to master data are recorded in standardized change documents, among other things. You use transaction SCDO to define change document objects, and transaction SCD3 to display change documents generically.</span></p>\r\n<p><a target=\"_blank\" name=\"DBTABLOG\" style=\"background-color: #ffffff;\"></a><strong>&#xFEFF;Generic logging of table content changes (BC-CUS-TOL-TME)</strong></p>\r\n<p><span style=\"font-size: 14px;\">Generic logging for configuration changes is activated using the profile parameter rec/client. You can register tables for this log function using transaction SE13 or the report RDDPRCHK. You can use transaction SCU3 or the report RFTBPROT_BCE to evaluate the change documents. Among others, SAP Note 112388 contains more detailed information.</span></p>\r\n<p><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"ST03N\"></a>&#xFEFF;<strong>Performance statistical data on user behavior (BC-CCM-MON-TUN)</strong></span></p>\r\n<p><span style=\"font-size: 14px;\">The workload monitor (transaction ST03N) is used to analyze the statistical data of the ABAP kernel. </span>Using a variety of possible analysis views, you can determine the cause of possible performance problems, but also show how users use applications. For more detailed information, see the online documentation.</p>\r\n<p>&#x00A0;<strong><a target=\"_blank\" name=\"SE80\" style=\"background-color: #ffffff;\"></a>&#xFEFF;Version management of ABAP Workbench (BC-ABA-LA)</strong></p>\r\n<p>Version management for ABAP repository objects can be used via the relevant maintenance applications and SE09. Versioning always takes place when workbench transport requests are released and when changes are checked and, if necessary, undone. Transaction SPAU supports the tracking of changes that modify the standard.</p>\r\n<p><a target=\"_blank\" name=\"SE09\"></a><strong>&#xFEFF;Transport logs (BC-CTS-ORG)</strong></p>\r\n<p>You can view the logs of the transport system in transaction STMS and in the detail view of the transport requests in SE01/SE09/SE10. You can check logs for local transport processes using transaction SCC3.</p>\r\n<p><a target=\"_blank\" name=\"SM39\" style=\"background-color: #ffffff;\"></a><strong>&#xFEFF;Background processing logs (BC-CCM-BTC)</strong></p>\r\n<p>You can check background processing logs using transaction SM37 or SM39.</p>\r\n<p>&#x00A0;<a target=\"_blank\" name=\"XXXX\" style=\"background-color: #ffffff;\"></a><strong>&#xFEFF;Application-specific change documents</strong></p>\r\n<p>For historical or technical reasons, changing accesses are updated in application-specific change documents in selected applications. You can usually view and administer such documents in the direct environment of the application in question. Examples of application-specific protocols are:</p>\r\n<ul>\r\n<li>User changes and authorization changes (transaction SUIM)</li>\r\n<li>HTTP server log, HTTP client log (transaction SMICM)</li>\r\n<li>Unified Connectivity (transaction UCONPHTL)</li>\r\n<li>RFC gateway logging (transaction SMGW)</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"TTRACE\"></a><strong>&#xFEFF;Technical traces (BC*)</strong></span></p>\r\n<p><span style=\"font-size: 14px;\">Technical traces are usually used for problem analysis and are activated only for a short period of time for targeted analyses. </span>The system trace (transaction ST01, STAUTHTRACE) and the performance trace (transaction ST05) are frequently used.<br />Other technical traces work continuously and may reflect user actions. <br />Use transactions STRFCTRACE, STUSERTRACE, STUSOBTRACE, and ST11, among others, to access this data.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D033600)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033600)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000139418/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000139418/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91980", "RefComponent": "BC-MID-RFC", "RefTitle": "Missing output of RFC short dump for logon error", "RefUrl": "/notes/91980"}, {"RefNumber": "873749", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/873749"}, {"RefNumber": "84052", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Table logging", "RefUrl": "/notes/84052"}, {"RefNumber": "6833", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Deleting statistics file, size of statistics file", "RefUrl": "/notes/6833"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "41300", "RefComponent": "BC-DB-DBI", "RefTitle": "Table DBTABPRT is very large", "RefUrl": "/notes/41300"}, {"RefNumber": "30724", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/30724"}, {"RefNumber": "2467", "RefComponent": "BC-SEC", "RefTitle": "Password rules and avoidance of incorrect logons", "RefUrl": "/notes/2467"}, {"RefNumber": "23611", "RefComponent": "BC-SEC", "RefTitle": "Collective Note: Security in SAP Products", "RefUrl": "/notes/23611"}, {"RefNumber": "22514", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Error analysis for client copy", "RefUrl": "/notes/22514"}, {"RefNumber": "1916", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Logging of table changes in R/3", "RefUrl": "/notes/1916"}, {"RefNumber": "1499357", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Enhancement of table logging", "RefUrl": "/notes/1499357"}, {"RefNumber": "1497672", "RefComponent": "BC-SEC-AIS", "RefTitle": "AIS| Optimization of the report RDDPRCHK", "RefUrl": "/notes/1497672"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "115224", "RefComponent": "BC-SEC", "RefTitle": "SQL audit", "RefUrl": "/notes/115224"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3215592", "RefComponent": "BC-CST-LL", "RefTitle": "STAUTHTRACE log persistency", "RefUrl": "/notes/3215592 "}, {"RefNumber": "2349134", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03 - Analyzing reports/transactions executed in the system", "RefUrl": "/notes/2349134 "}, {"RefNumber": "2168742", "RefComponent": "FI-GL", "RefTitle": "Change logs cannot be found in T-code OB52", "RefUrl": "/notes/2168742 "}, {"RefNumber": "419933", "RefComponent": "BC-SEC-USR-IS", "RefTitle": "FAQ: Maintenance of change documents in user administration", "RefUrl": "/notes/419933 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "22514", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Error analysis for client copy", "RefUrl": "/notes/22514 "}, {"RefNumber": "41300", "RefComponent": "BC-DB-DBI", "RefTitle": "Table DBTABPRT is very large", "RefUrl": "/notes/41300 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "1916", "RefComponent": "BC-CUS-TOL-ALO", "RefTitle": "Logging of table changes in R/3", "RefUrl": "/notes/1916 "}, {"RefNumber": "115224", "RefComponent": "BC-SEC", "RefTitle": "SQL audit", "RefUrl": "/notes/115224 "}, {"RefNumber": "1497672", "RefComponent": "BC-SEC-AIS", "RefTitle": "AIS| Optimization of the report RDDPRCHK", "RefUrl": "/notes/1497672 "}, {"RefNumber": "1499357", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Enhancement of table logging", "RefUrl": "/notes/1499357 "}, {"RefNumber": "30724", "RefComponent": "BC-SEC", "RefTitle": "Data protection and security in SAP Systems", "RefUrl": "/notes/30724 "}, {"RefNumber": "91980", "RefComponent": "BC-MID-RFC", "RefTitle": "Missing output of RFC short dump for logon error", "RefUrl": "/notes/91980 "}, {"RefNumber": "2467", "RefComponent": "BC-SEC", "RefTitle": "Password rules and avoidance of incorrect logons", "RefUrl": "/notes/2467 "}, {"RefNumber": "873749", "RefComponent": "MM-IM-ED", "RefTitle": "ED: Grundsätze zur ordnungsmäßigen Führung und Aufbewahrung", "RefUrl": "/notes/873749 "}, {"RefNumber": "23611", "RefComponent": "BC-SEC", "RefTitle": "Collective Note: Security in SAP Products", "RefUrl": "/notes/23611 "}, {"RefNumber": "84052", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Table logging", "RefUrl": "/notes/84052 "}, {"RefNumber": "145816", "RefComponent": "BC-SEC", "RefTitle": "Security audit: Corrections for 4.0B", "RefUrl": "/notes/145816 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}