{"Request": {"Number": "1428529", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 412, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016959192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001428529?language=E&token=7C32095660680EC68D0F3B24C67E2ECF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001428529", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001428529/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1428529"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 36}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.03.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1428529 - Corrections in BR*Tools Version 7.20"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note is a composite SAP Note for known problems in BR*Tools Version 7.20. The notes listed below contain detailed information about the problems that were solved.<br />You can use BR*Tools 7.20 for all SAP releases and kernel versions provided that the system is based on an Oracle 10g or 11g database. Oracle 9i is no longer supported by BR*Tools 7.20.<br /><br />The most important enhancements in BR*Tools 7.20 are as follows:<br />* Enhanced support for Oracle 11g<br />For more information about the exact extent of this support, see Note 1430669.<br /><br />Note:<br />--------<br />To be able to use BR*Tools 7.20 under Oracle 11g on Unix platforms (except for AIX), you must set up the following soft link in $ORACLE_HOME/lib:<br />&gt; cd $ORACLE_HOME/lib<br />&gt; ln -s libnnz11.so libnnz10.so<br />Note the following:<br />The soft link is no longer required for the version of BR*Tools 7.20 that is delivered with SAP Kernel 7.20_EXT. However, you can use these tools only as of certain operating system versions (for example, for IBM AIX as of Version 6.1). For more information, see SAP Note 1638356.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BRARCHIVE, BRBACKUP, BRCONNECT, BRRESTORE, BRRECOVER, BRSPACE, BRTOOLS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The following problems were solved with patches for BR*Tools 7.20:<br /><br />Patch    Date    SAP Note   Tool<br /><br />&#x00A0;&#x00A0; 1&#x00A0;&#x00A0; JAN/26/2010&#x00A0;&#x00A0;1430669&#x00A0;&#x00A0;BR*Tools<br />BR*Tools support for Oracle 11g<br />&#x00A0;&#x00A0; 2&#x00A0;&#x00A0; APR/29/2010&#x00A0;&#x00A0;1464155&#x00A0;&#x00A0;BRSPACE<br />Error in BRSPACE w/ options -f|-file and -a5|-autoextend5<br />&#x00A0;&#x00A0; 3&#x00A0;&#x00A0; APR/29/2010&#x00A0;&#x00A0;1464156&#x00A0;&#x00A0;BRSPACE<br />Support for index compression in BRSPACE<br />&#x00A0;&#x00A0; 4&#x00A0;&#x00A0; JUL/28/2010&#x00A0;&#x00A0;1493500&#x00A0;&#x00A0;BR*Tools<br />BR*Tools terminate with segmentation fault (core dump)<br />&#x00A0;&#x00A0; 5&#x00A0;&#x00A0; JUL/28/2010&#x00A0;&#x00A0;1493613&#x00A0;&#x00A0;BRRECOVER,BRSPACE<br />Rebuilding indexes after recovery fails with BR1179E<br />&#x00A0;&#x00A0; 6&#x00A0;&#x00A0; JUL/28/2010&#x00A0;&#x00A0;1493614&#x00A0;&#x00A0;BRBACKUP,BRRESTORE<br />Parallel incremental backup on disk<br />&#x00A0;&#x00A0; 7&#x00A0;&#x00A0; AUG/25/2010&#x00A0;&#x00A0;1502257&#x00A0;&#x00A0;BRBACKUP<br />No incremental backup possible with BRBACKUP 7.20 (6)<br />&#x00A0;&#x00A0; 8&#x00A0;&#x00A0; OCT/06/2010&#x00A0;&#x00A0;1515982&#x00A0;&#x00A0;BRSPACE<br />Oracle error messages for index compression using BRSPACE<br />&#x00A0;&#x00A0; 9&#x00A0;&#x00A0; OCT/27/2010&#x00A0;&#x00A0;1523205&#x00A0;&#x00A0;BR*Tools<br />BR*Tools support for eSourcing databases<br />&#x00A0;&#x00A0;10&#x00A0;&#x00A0; NOV/24/2010&#x00A0;&#x00A0;1532316&#x00A0;&#x00A0;BRCONNECT<br />MISSING_STATISTICS alert directly after update statistics<br />&#x00A0;&#x00A0;11&#x00A0;&#x00A0; JAN/27/2011&#x00A0;&#x00A0;1553244&#x00A0;&#x00A0;BRCONNECT<br />Long runtimes of BRCONNECT function &quot;check&quot;<br />&#x00A0;&#x00A0;12&#x00A0;&#x00A0; JAN/27/2011&#x00A0;&#x00A0;1553245&#x00A0;&#x00A0;BRSPACE<br />Corrections in BR*Tools regarding Oracle 11g<br />&#x00A0;&#x00A0;13&#x00A0;&#x00A0; MAR/02/2011&#x00A0;&#x00A0;1564526&#x00A0;&#x00A0;BRARCHIVE<br />Core dump in BRARCHIVE when importing Archivelog files<br />&#x00A0;&#x00A0;14&#x00A0;&#x00A0; MAY/04/2011&#x00A0;&#x00A0;1585727&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP<br />Syntax error in &quot;keep&quot; option during RMAN call<br />&#x00A0;&#x00A0;15&#x00A0;&#x00A0; JUN/30/2011&#x00A0;&#x00A0;1605578&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 Patch 15<br />&#x00A0;&#x00A0;16&#x00A0;&#x00A0; JUL/28/2011&#x00A0;&#x00A0;1615503&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 16<br />&#x00A0;&#x00A0;17&#x00A0;&#x00A0; AUG/26/2011&#x00A0;&#x00A0;1625038&#x00A0;&#x00A0;BRBACKUP<br />BRBACKUP 7.20 EXT fails on Windows with BR0274E<br />&#x00A0;&#x00A0;18&#x00A0;&#x00A0; SEP/07/2011&#x00A0;&#x00A0;1627541&#x00A0;&#x00A0;BR*Tools<br />BR*Tools support for Oracle ASM and Exadata<br />&#x00A0;&#x00A0;19&#x00A0;&#x00A0; OCT/28/2011&#x00A0;&#x00A0;1647270&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 19<br />&#x00A0;&#x00A0;20&#x00A0;&#x00A0; OCT/28/2011&#x00A0;&#x00A0;1647271&#x00A0;&#x00A0;BRSPACE<br />Support for bigfile tablespaces in BR*Tools<br />&#x00A0;&#x00A0;21&#x00A0;&#x00A0; NOV/21/2011&#x00A0;&#x00A0;1655221&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRSPACE<br />Corrections in BR*Tools 7.20 patch 21<br />&#x00A0;&#x00A0;22&#x00A0;&#x00A0; JAN/10/2012&#x00A0;&#x00A0;1671867&#x00A0;&#x00A0;BRBACKUP<br />Online consistent backup with disk copy and split mirror<br />&#x00A0;&#x00A0;23&#x00A0;&#x00A0; MAR/16/2012&#x00A0;&#x00A0;1697269&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRCONNECT<br />Corrections in BR*Tools 7.20 patch 23<br />&#x00A0;&#x00A0;24&#x00A0;&#x00A0; MAY/25/2012&#x00A0;&#x00A0;1723121&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 24<br />&#x00A0;&#x00A0;25&#x00A0;&#x00A0; JUN/28/2012&#x00A0;&#x00A0;1735811&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 Patch 25<br />&#x00A0;&#x00A0;26&#x00A0;&#x00A0; SEP/12/2012&#x00A0;&#x00A0;1763972&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 26<br />&#x00A0;&#x00A0;27&#x00A0;&#x00A0; SEP/12/2012&#x00A0;&#x00A0;1764043&#x00A0;&#x00A0;BR*Tools<br />Support for secure storage in BR*Tools<br />&#x00A0;&#x00A0;28&#x00A0;&#x00A0; OCT/25/2012&#x00A0;&#x00A0;1780057&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 28<br />&#x00A0;&#x00A0;29&#x00A0;&#x00A0; DEC/03/2012&#x00A0;&#x00A0;1795814&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 29<br />&#x00A0;&#x00A0;30&#x00A0;&#x00A0; JAN/31/2013&#x00A0;&#x00A0;1816874&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 30<br />&#x00A0;&#x00A0;31&#x00A0;&#x00A0; MAR/26/2013&#x00A0;&#x00A0;1838852&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 31<br />&#x00A0;&#x00A0;32&#x00A0;&#x00A0; MAY/28/2013&#x00A0;&#x00A0;1865897&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 32<br />&#x00A0;&#x00A0;33&#x00A0;&#x00A0; JUL/03/2013&#x00A0;&#x00A0;1882181&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 33<br />&#x00A0;&#x00A0;34&#x00A0;&#x00A0; SEP/05/2013&#x00A0;&#x00A0;1909451&#x00A0;&#x00A0;BR*Tools<br />Corrections in BR*Tools 7.20 patch 34<br />&#x00A0; 35&#x00A0;&#x00A0;&#x00A0;NOV/14/2013&#x00A0; 1940675&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 35<br />&#x00A0;&#x00A0;36&#x00A0;&#x00A0;&#x00A0;FEB/24/2014&#x00A0;&#x00A0;1983045&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 36<br />&#x00A0; 37&#x00A0;&#x00A0;&#x00A0;APR/30/2014&#x00A0; 2011892&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 37<br />&#x00A0; 38&#x00A0;&#x00A0;&#x00A0;JUN/25/2014&#x00A0; 2034605&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 38<br />&#x00A0; 39&#x00A0;&#x00A0;&#x00A0;AUG/27/2014&#x00A0; 2059497&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 39<br />&#x00A0;&#x00A0;40&#x00A0;&#x00A0;&#x00A0;OCT/29/2014&#x00A0;&#x00A0;2086963&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 40<br />&#x00A0;&#x00A0;41&#x00A0;&#x00A0;&#x00A0;2015-01-28&#x00A0;&#x00A0;2123623&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 41<br />&#x00A0;&#x00A0;42&#x00A0;&#x00A0;&#x00A0;3/25/2015&#x00A0;&#x00A0;2147658&#x00A0; BR*Tools<br />Corrections in BR*Tools 7.20 Patch 42<br /><br />The latest patch level for a particular tool is determined by the highest patch number that refers to a BR tool. This level corresponds to the current patch available on SAP Service Marketplace. A specific patch level contains all corrections with the same or a smaller patch number.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Download the most recent BR*Tools patch from SAP Service Marketplace. The precise procedure is described in SAP Notes 12741 and 19466.</p>\r\n<p>Caution:<br />The download locations for BR*Tools 7.20 on SAP Service Marketplace have changed as of Patch 40. For more information, see SAP Note 12741.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001428529/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001428529/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001428529/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1909451", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 34 / 7.40 Patch 5", "RefUrl": "/notes/1909451"}, {"RefNumber": "1882181", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 33 / 7.40 Patch 3", "RefUrl": "/notes/1882181"}, {"RefNumber": "1865897", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "00Corrections in BR*Tools 7.20 Patch 32 / 7.40 Patch 2", "RefUrl": "/notes/1865897"}, {"RefNumber": "1838852", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 31 / 7.40 Patch 1", "RefUrl": "/notes/1838852"}, {"RefNumber": "1816874", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 30", "RefUrl": "/notes/1816874"}, {"RefNumber": "1795814", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 29", "RefUrl": "/notes/1795814"}, {"RefNumber": "1780057", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 28", "RefUrl": "/notes/1780057"}, {"RefNumber": "1764043", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for secure storage in BR*Tools", "RefUrl": "/notes/1764043"}, {"RefNumber": "1763972", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 26", "RefUrl": "/notes/1763972"}, {"RefNumber": "1735811", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 25", "RefUrl": "/notes/1735811"}, {"RefNumber": "1723121", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 24", "RefUrl": "/notes/1723121"}, {"RefNumber": "1697269", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 23", "RefUrl": "/notes/1697269"}, {"RefNumber": "1671867", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Online consistent backup for disk copy and split mirror", "RefUrl": "/notes/1671867"}, {"RefNumber": "1655221", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 21", "RefUrl": "/notes/1655221"}, {"RefNumber": "1647272", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (4)", "RefUrl": "/notes/1647272"}, {"RefNumber": "1647271", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for bigfile tablespaces in BR*Tools", "RefUrl": "/notes/1647271"}, {"RefNumber": "1647270", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 19 and 7.10 patch 42", "RefUrl": "/notes/1647270"}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356"}, {"RefNumber": "1627541", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle ASM and Exadata/ODA", "RefUrl": "/notes/1627541"}, {"RefNumber": "1625038", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP 7.20 EXT fails on Windows with BR0274E", "RefUrl": "/notes/1625038"}, {"RefNumber": "1615503", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 16 and 7.10 patch 41", "RefUrl": "/notes/1615503"}, {"RefNumber": "1605578", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 15 and 7.10 patch 40", "RefUrl": "/notes/1605578"}, {"RefNumber": "1585727", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Syntax error in the \"keep\" option with RMAN call", "RefUrl": "/notes/1585727"}, {"RefNumber": "1564526", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Core dump in BRARCHIVE when importing archivelog files", "RefUrl": "/notes/1564526"}, {"RefNumber": "1553245", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools regarding Oracle 11g", "RefUrl": "/notes/1553245"}, {"RefNumber": "1553244", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Long runtimes of BRCONNECT function \"check\"", "RefUrl": "/notes/1553244"}, {"RefNumber": "1532316", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "MISSING_STATISTICS alert directly after update statistics", "RefUrl": "/notes/1532316"}, {"RefNumber": "1523205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for eSourcing databases", "RefUrl": "/notes/1523205"}, {"RefNumber": "1515982", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle error messages during index compression with BRSPACE", "RefUrl": "/notes/1515982"}, {"RefNumber": "1502257", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "No incremental backup possible with BRBACKUP 7.20 (6)", "RefUrl": "/notes/1502257"}, {"RefNumber": "1493614", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Parallel incremental backup on disk", "RefUrl": "/notes/1493614"}, {"RefNumber": "1493613", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Rebuilding indexes after recovery fails with BR1179E", "RefUrl": "/notes/1493613"}, {"RefNumber": "1493500", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools terminate with segmentation fault (core dump)", "RefUrl": "/notes/1493500"}, {"RefNumber": "1464156", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for index compression in BRSPACE 7.20", "RefUrl": "/notes/1464156"}, {"RefNumber": "1464155", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error in BRSPACE w/ options -f|-file and -a5|-autoextend5", "RefUrl": "/notes/1464155"}, {"RefNumber": "1430669", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 11g", "RefUrl": "/notes/1430669"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1763972", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 26", "RefUrl": "/notes/1763972 "}, {"RefNumber": "1882181", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 33 / 7.40 Patch 3", "RefUrl": "/notes/1882181 "}, {"RefNumber": "1627541", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle ASM and Exadata/ODA", "RefUrl": "/notes/1627541 "}, {"RefNumber": "1909451", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 34 / 7.40 Patch 5", "RefUrl": "/notes/1909451 "}, {"RefNumber": "1764043", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for secure storage in BR*Tools", "RefUrl": "/notes/1764043 "}, {"RefNumber": "1838852", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 31 / 7.40 Patch 1", "RefUrl": "/notes/1838852 "}, {"RefNumber": "1865897", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "00Corrections in BR*Tools 7.20 Patch 32 / 7.40 Patch 2", "RefUrl": "/notes/1865897 "}, {"RefNumber": "1647272", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (4)", "RefUrl": "/notes/1647272 "}, {"RefNumber": "1464156", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for index compression in BRSPACE 7.20", "RefUrl": "/notes/1464156 "}, {"RefNumber": "1647271", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for bigfile tablespaces in BR*Tools", "RefUrl": "/notes/1647271 "}, {"RefNumber": "1816874", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 30", "RefUrl": "/notes/1816874 "}, {"RefNumber": "1795814", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 29", "RefUrl": "/notes/1795814 "}, {"RefNumber": "1780057", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 28", "RefUrl": "/notes/1780057 "}, {"RefNumber": "1735811", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 25", "RefUrl": "/notes/1735811 "}, {"RefNumber": "1723121", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 24", "RefUrl": "/notes/1723121 "}, {"RefNumber": "1697269", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 Patch 23", "RefUrl": "/notes/1697269 "}, {"RefNumber": "1671867", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Online consistent backup for disk copy and split mirror", "RefUrl": "/notes/1671867 "}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356 "}, {"RefNumber": "1655221", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 21", "RefUrl": "/notes/1655221 "}, {"RefNumber": "1647270", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 19 and 7.10 patch 42", "RefUrl": "/notes/1647270 "}, {"RefNumber": "1615503", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 16 and 7.10 patch 41", "RefUrl": "/notes/1615503 "}, {"RefNumber": "1430669", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 11g", "RefUrl": "/notes/1430669 "}, {"RefNumber": "1625038", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP 7.20 EXT fails on Windows with BR0274E", "RefUrl": "/notes/1625038 "}, {"RefNumber": "1605578", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools 7.20 patch 15 and 7.10 patch 40", "RefUrl": "/notes/1605578 "}, {"RefNumber": "1553244", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Long runtimes of BRCONNECT function \"check\"", "RefUrl": "/notes/1553244 "}, {"RefNumber": "1585727", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Syntax error in the \"keep\" option with RMAN call", "RefUrl": "/notes/1585727 "}, {"RefNumber": "1564526", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Core dump in BRARCHIVE when importing archivelog files", "RefUrl": "/notes/1564526 "}, {"RefNumber": "1553245", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools regarding Oracle 11g", "RefUrl": "/notes/1553245 "}, {"RefNumber": "1532316", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "MISSING_STATISTICS alert directly after update statistics", "RefUrl": "/notes/1532316 "}, {"RefNumber": "1515982", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle error messages during index compression with BRSPACE", "RefUrl": "/notes/1515982 "}, {"RefNumber": "1523205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for eSourcing databases", "RefUrl": "/notes/1523205 "}, {"RefNumber": "1502257", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "No incremental backup possible with BRBACKUP 7.20 (6)", "RefUrl": "/notes/1502257 "}, {"RefNumber": "1493500", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools terminate with segmentation fault (core dump)", "RefUrl": "/notes/1493500 "}, {"RefNumber": "1493613", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Rebuilding indexes after recovery fails with BR1179E", "RefUrl": "/notes/1493613 "}, {"RefNumber": "1493614", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Parallel incremental backup on disk", "RefUrl": "/notes/1493614 "}, {"RefNumber": "1464155", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error in BRSPACE w/ options -f|-file and -a5|-autoextend5", "RefUrl": "/notes/1464155 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "720", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}