{"Request": {"Number": "1932366", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 432, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017746892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001932366?language=E&token=8ACBFFF4FFEA1506AE7696266D917B32"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001932366", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001932366/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1932366"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2021.10.01"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1932366 - Configuration of an Oracle Grid Administrator Account"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Even when the software for Oracle Grid Infrastructure and Oracle Database are owned by one and the same&#160;software owner &#8218;oracle&#8217;,&#160;you can create&#160;a dedicated Grid Administrator OS account for administration of ASM, ACFS and/or cluster resources. This SAP note describes&#160;how to create and configure&#160;such a dedicated Oracle Grid Administrator account.</p>\r\n<p><span style=\"text-decoration: underline;\">Prerequisites</span></p>\r\n<p>This SAP Note is valid SAP NetWeaver on Oracle Database (11.2, 12.1) on Unix and Linux platforms (incl. Oracle Engineered Systems) where both Oracle Database Home(s) and Oracle Grid Infrastructure Home are owned by the 'oracle' OS user.</p>\r\n<p>This SAP Note does not apply to&#160;SAP NetWeaver on Oracle Database Exadata Cloud@Customer where&#160;Oracle Grid Infrastructure Home is owned by the 'grid' OS user (Reference: SAP Note&#160;2956661,&#160;white paper <a target=\"_blank\" href=\"https://www.sap.com/documents/2020/09/002eb6f2-af7d-0010-87a3-c30de2ffd8ff.html\">SAP NetWeaver Application Server ABAP/Java on Oracle Database Exadata Cloud@Customer X8M</a>)</p>\r\n<p><span style=\"text-decoration: underline;\">References / Related SAP Notes</span></p>\r\n<ul>\r\n<li>SAP note <a target=\"_blank\" href=\"/notes/1755636\">1755636</a></li>\r\n<li>SAP note <a target=\"_blank\" href=\"/notes/1930298\">1930298</a></li>\r\n<li>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2956661\">2956661</a></li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle ASM, Automatic Storage Management<br />Oracle Clusterware<br />Grid home, Database home, Oracle home</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP environments&#160;the Oracle grid stack (GI) and the Oracle database stack (RDBMS) are owned by the same software owner, namely OS user 'oracle'. This SAP note describes, together with SAP notes <a target=\"_blank\" href=\"/notes/1755636\">1755636</a> and <a target=\"_blank\" href=\"/notes/1930298\">1930298</a>, how you can separate the tasks &#8218;Oracle Software Installation&#8217;, &#8218;Oracle Database Administration&#8217; and &#8218;Oracle Grid/ASM Administration&#8217; from each other.</p>\r\n<p>Note:</p>\r\n<p style=\"padding-left: 30px;\">This SAP Note does not apply for SAP NetWeaver on Oracle Database Exadata Cloud@Customer (SAP Note <a target=\"_blank\" href=\"/notes/2956661\">2956661</a>) where Oracle Grid Infrastructure Home is owned by the 'grid' OS user.</p>\r\n<p><span style=\"text-decoration: underline;\">Job Role Oracle Software Installation</span></p>\r\n<p>'oracle' as the software owner should be used for installing and patching of Oracle homes, not for other administration tasks. Access to this user should therefore be limited to those who are responsible for the installation of the Oracle software. A secure configuration that restricts access to 'oracle&#8217; is described in SAP note 1930298.</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">Job Role Oracle Database Administration</span></p>\r\n<p>As already mentioned above, OS user &#8218;oracle&#8217; should not be used for database administration tasks. Instead, you should create one ore more dedicated OS accounts for the administration of databases. It is also possible to separate database administration for different databases by using separate ORACLE_HOMEs with corresponding administrator accounts. For details how to configure these OS accounts and ORACLE_HOMEs, see SAP note 1755636.</p>\r\n<p><span style=\"text-decoration: underline;\">Job Role Oracle Grid/ASM&#160;Administration</span></p>\r\n<p>&#8218;oracle&#8217; is a member of all relevant Oracle OS groups (OSDBA, OSOPER, OSASM,...). This allows 'oracle' to perform all kinds of administration tasks of the complete Oracle stack. However, the job role of &#8218;oracle&#8217; is &#8218;Software Owner&#8217; and not &#8218;Database Administrator&#8217; or &#8218;ASM Administrator&#8217;. Therefore, &#8218;oracle&#8217; should not be used for database or ASM administration tasks. To separate Grid/ASM administration from software administration (and also from database administration), you can create a separate grid administrator account and configure this account according to your needs. This is described in this SAP note.</p>\r\n\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Creating a Grid Administrator Account</strong></p>\r\n<p>The following configuration was derived from an SAP test installation with a 2-node Oracle RAC database and ASM.</p>\r\n<p>In this SAP note the name of the OS account for the grid administrator is 'gridadmin'. The new OS account 'gridadmin' will be created and and assigned to the corresponding OS groups for Oracle specific OS authentication. Finally the ACL will be configured to allow the administrator to manage resources of the Oracle cluster, ASM or ACFS.</p>\r\n<p>The main difference between the OS user &#8218;gridadmin&#8217; that is configured here as dedicated grid administrator account, and the &#8218;grid&#8217;&#160;account that is mentioned in the Oracle documentation to install the Oracle Grid software is that &#8218;grid&#8217; is the &#8218;software owner&#8217; of the Grid home while &#8218;gridadmin&#8217; is an administrator of resources like cluster, ASM, ACFS etc, but not a software owner.</p>\r\n<p><span style=\"text-decoration: underline;\">Procedure</span></p>\r\n<ol>\r\n<li><span style=\"text-decoration: underline;\">Step 1: create a new Grid Administrator account</span><br /><br />The&#160;account 'gridadmin' will be created and added to those OS groups which are linked into the Oracle Grid Home (see GRIDHOME/rdbms/lib/config.c)<br />Note: for RAC you must create the new account on all cluster nodes.<br /><br />The following example creates a new OS group &#8218;orasys&#8217; first, then creates the grid administrator account. Creating the &#8218;orasys&#8217; group is optional, however it makes it easier to add or remove Oracle system groups.<br /><br />Example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# groupadd orasys</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# useradd -c \"Grid Administrator\" -d /home/<USER>/span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# usermod -G asmdba,asmoper,asmadmin gridadmin</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# id gridadmin</span><br /><br />For release 12.1 gridadmin&#160;should be a member of the 'oinstall' group (as primary group).<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# useradd -c \"Grid Administrator\" -d /home/<USER>/span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# usermod -G asmdba,asmoper,asmadmin gridadmin</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# id gridadmin</span><br /><br /></li>\r\n<li><span style=\"text-decoration: underline;\">Step 2: Logon as gridadmin</span><br /><br />If ASM is configured, you can set Oracle environment for ASM using 'oraenv' script.<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">root# su &#8211; gridadmin</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ env | grep ORA</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ env | grep PATH</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">PATH=/usr/kerberos/bin:/usr/local/bin:/bin:/usr/bin</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ . oraenv</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ORACLE_SID = [grid] ? +ASM1</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ORACLE_BASE environment variable is not being set since this</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">information is not available for the current user ID grid.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">You can set ORACLE_BASE manually if it is required.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$</span><br /><br />This warning can be suppressed by running &#8218;oraenv&#8217; silently using 'oraenv -s':<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ . oraenv &#8211;s</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ORACLE_SID = [grid] ? +ASM1</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$&#65279;</span></li>\r\n<li><span style=\"text-decoration: underline;\">Step 3: Checking cluster resources</span><br /><br />Now as 'gridadmin' you can check the status of cluster resources.<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ crsctl help</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ crsctl stat res &#8211;t</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ crsctl status resource &#8211;t</span><br /><br />Example: Checking status of listener<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ srvctl status listener</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Listener LISTENER is enabled</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Listener LISTENER is running on node(s): vmkb6,vmkb5</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$</span></li>\r\n<li><span style=\"text-decoration: underline;\">Step 4: Modifying cluster resources</span><br /><br />As 'gridadmin' try starting or stopping the listener. Before you do this, check the ACL for the listener resource.<br /><br />Logon as 'oracle'<br />$ crsctl getperm resource ora.LISTENER.lsnr<br />Name: ora.LISTENER.lsnr<br />owner:oracle:rwx,pgrp:oinstall:rwx,other::r--<br />$<br /><br />This information means that&#160;user 'oracle'&#160;and members of &#180;group 'oinstall'&#160;(primary group) have full ACL privilege ('rwx'). All other users have just &#8218;read&#8217; permission ('r--'). If you are not 'oracle' and not a member of 'oinstall', you will get the following error when you try to stop or start the listener:<br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$ srvctl stop listener</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">PRCR-1065 : Failed to stop resource ora.LISTENER.lsnr</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CRS-0245:&#160; User doesn't have enough privilege to perform the operation</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CRS-0245:&#160; User doesn't have enough privilege to perform the operation</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">$</span></li>\r\n<li><span style=\"text-decoration: underline;\">Step 5: Configure ACL for 'gridadmin'</span><br /><br />Before you configure the ACL of some resources for the grid administrator, you must define the job role of the grid administrator. This job role defines which resources need to be managed by the grid administrator and which resource ACLs must be configured. <br /><br />The following is an -incomplete- list of resources a grid administrator might be responsible for:\r\n<ul>\r\n<li>starting/stopping of scan listener</li>\r\n<li>mount/unmount ACFS volumes</li>\r\n<li>starting/stopping of the CRS stack (this requires starting/stopping of database and ASM instances)</li>\r\n</ul>\r\n<br />The following is&#160;an example to show how to configure ACL for a resource. <br />In the following&#160;example we will configure the ACL of the listener resource to&#160;allow 'gridadmin'&#160;to start and stop the listener. The configuration of the ACL is performed by the software owner 'oracle'.<br /><br /><span style=\"text-decoration: underline;\">Checking ACL for Resource LISTENER</span><br /><br />Logon as 'oracle'<br />$ crsctl getperm resource ora.LISTENER.lsnr<br />Name: ora.LISTENER.lsnr<br />owner:oracle:rwx,pgrp:oinstall:rwx,other::r--<br />$<br /><br />Interpretation: only 'oracle' or members of 'oinstall' as primary group&#160;have full ACL privilege ('rwx'). All other users&#160;have just &#8218;read&#8217; permission ('r--').<br /><br /><br /><span style=\"text-decoration: underline;\">Setting ACL for Resource LISTENER</span><br /><br />When you configure&#160;ACL, you can allow operations on a&#160;resource&#160;for a specific&#160;OS user or for a given&#160;OS&#160;group. Both options are described now.<br />Note: the ACL permissions are configured by the software owner 'oracle'.<br /><br /><span style=\"text-decoration: underline;\">Option 1: allowing for a certain user</span><br /><br />As &#8218;oracle&#8217; set ACL for 'gridadmin': <br />$crsctl setperm resource ora.LISTENER.lsnr -u user:gridadmin:r-x<br />$<br /><br />Setting the 'x' flag allows starting and stopping of the resource. Setting 'w' flag would also allow to modify resource attributes.<br /><br />You can check the permissions on resource ora.LISTENER.lsnr for user &#8218;gridadmin&#8217; as follows:<br /><br />Check ACL permissions for the resource for gridadmin:<br />$ crsctl getperm resource ora.LISTENER.lsnr -u gridadmin<br />Name: ora.LISTENER.lsnr<br />r-x<br />$<br /><br />Check ACL permissions for the resource for all accounts:<br />$ crsctl getperm resource ora.LISTENER.lsnr<br />Name: ora.LISTENER.lsnr<br />owner:oracle:rwx,pgrp:oinstall:rwx,other::r--,user:gridadmin:r-x<br />$<br /><br />Now 'gridadmin' can start or stop the listener.<br />$ srvctl stop listener<br />$ srvctl start listener<br /><br /><span style=\"text-decoration: underline;\">Option 2: allowing for a certain group</span><br /><br />Check ACL before change:<br />$ crsctl getperm resource ora.LISTENER.lsnr<br />Name: ora.LISTENER.lsnr<br />owner:oracle:rwx,pgrp:oinstall:rwx,other::r--<br />$<br /><br />As &#8218;oracle&#8217; set ACL for group 'orasys':<br />$ crsctl setperm resource ora.LISTENER.lsnr -u group:orasys:r-x<br />$<br /><br />Check ACL&#160;after change:<br />$ crsctl getperm resource ora.LISTENER.lsnr<br />Name: ora.LISTENER.lsnr<br />owner:oracle:rwx,pgrp:oinstall:rwx,other::r--,group:orasys:r-x<br />$<br /><br />Now 'gridadmin' can start or stop the listener.<br />$ srvctl stop listener<br />$ srvctl start listener</li>\r\n</ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "Oracle 11.2"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5000979)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001932366/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001932366/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1930298", "RefComponent": "BC-DB-ORA", "RefTitle": "Restricting Access to Software Owner 'oracle'", "RefUrl": "/notes/1930298"}, {"RefNumber": "1755636", "RefComponent": "BC-DB-ORA", "RefTitle": "Database Administrators Segregation", "RefUrl": "/notes/1755636"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}