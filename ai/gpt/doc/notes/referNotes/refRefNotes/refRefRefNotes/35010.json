{"Request": {"Number": "35010", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 324, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014389322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000035010?language=E&token=CB65D6249589DD66F9EC16F889F76A40"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000035010", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000035010/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "35010"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 32}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Network connection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "35010 - Service connections: Composite note (overview))"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to set up a service connection to SAP. The documentation for this subject area is distributed over several guides/SAP Notes. This SAP Note provides an overview.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Service connections, SAProuter</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You must already have a physical link to SAP (for example, VPN, SNC, provider, and so on).<br /><br />The addresses of external providers as well as network providers are available in SAP Notes 33953 (network providers Europe/EMEA), 200330 (network providers CIS and Baltic states), 40739 (network providers America), and 39894 (network providers Japan).<br /><br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Table of contents:</strong></p>\r\n<p><br />1. Technical requirements<br />1.1 Setting up your network<br />1.2 Configuration of the SAProuter<br />1.3 Setting up your system<br />1.4 Testing the network connection to SAP<br /><br />2. Setting up the service connection (access by SAP to your systems, support connection)<br />2.1 Releases on the SAProuter (for customer)<br />2.2 Maintaining your system data in the SAP Support Portal<br />2.3 Setting up or activating the service connections<br />2.4 Opening the service connection in the SAP Support Portal<br /><br /></p>\r\n<p><strong>1. Technical requirements</strong></p>\r\n<p>To set up the service connection, you require a data line to SAP. A SAProuter must also be installed and configured on both sides (customer, SAP).</p>\r\n<p><strong>1.1 Setting up your network</strong></p>\r\n<p>Choose the network type that you require for your remote connection:</p>\r\n<ul>\r\n<ul>\r\n<li>Internet connection (VPN or SNC connection, see SAP Note 486688, or URL <a target=\"_blank\" href=\"https://support.sap.com/remote-support/help.html\">https://support.sap.com/remote-support/help.html</a> --&gt; Choosing your Connection Type)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Provider connection (SAP Note 33953 Europe/EMEA, SAP Note 200330 CIS and Baltic states, SAP Note 37946 Asia, SAP Note 39894 Japan, and SAP Note 40739 America/Canada).</li>\r\n</ul>\r\n</ul>\r\n<p><br />The main document for registering your connection/SAProuter IP address with SAP is SAP Note 28976 (Remote Connection Data Sheet). This SAP Note contains all of the information that you require when setting up your connection to SAP. You must therefore fill out this document completely and send it to SAP. SAP Note 28976 contains the contact addresses.<br />If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-NET in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).</p>\r\n<p><strong>1.2 Configuration of the SAProuter</strong></p>\r\n<p>Information about setting up the SAProuter is available in the SAP Support Portal at <a target=\"_blank\" href=\"https://support.sap.com/remote-support/saprouter.html\">https://support.sap.com/remote-support/saprouter.html</a> or in SAP Note 30289 \"SAProuter documentation\" (refer to the attachment).<br />If you have any problems or any questions about the SAProuter, create an incident under the component BC-CST-NI in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).</p>\r\n<p><strong>1.3 Setting up your system</strong></p>\r\n<p>The SAP Support Backbone setup is described under the link https://support.sap.com/en/release-upgrade-maintenance/maintenance-information/connectivity-to-sap.html.&#x00A0;<br />If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-NET in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).</p>\r\n<p><strong>1.4 Testing the network connection to SAP</strong></p>\r\n<p>Perform a connection test to ensure that the settings, and therefore the function of the network connection to SAP, are correct.<br />If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-NET in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).<br /><br /></p>\r\n<p><strong>2 Setting up the service connection (access by SAP to your systems, support connection)</strong></p>\r\n<p>The service connection from SAP to your systems uses only the existing connection between the SAProuter in SAP and the SAProuter that is set up in your network infrastructure and registered with SAP (SAProuter of customer). No provision is made for other service connections, such as browser-supported connections using the public Internet (without sapserv1 or sapserv2).<br />Your SAProuter enables you to access all of your SAP solutions, but you can use the maintained system data, the setting of the service accesses to be used (for example R/3 Support, HTTP Connect URLAccess, and so on) and the setting of your SAProuter (keyword 'saprouttab') to determine which accesses you want to allow. This means that a service connection allows you to have control over all access options.</p>\r\n<p><strong>2.1 Releases on the SAProuter (for customer)</strong></p>\r\n<p>The SAProuter must be registered and set up as described in paragraph 1 and the paragraphs that follow. This means that the data transfer to the SAProuter software must also be possible from the SAP system (SAPservX) to the port that is maintained in the system data (see point 2.2 for system data maintenance). The accesses by SAP to the systems must be released in the route permission table 'saprouttab' (see point 1.2 or SAP Note 30289 \"SAProuter documentation\" and refer to the attachment).<br />If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-NET in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).</p>\r\n<p><strong>2.2 Maintaining your system data in the SAP Support Portal</strong></p>\r\n<p>To enable an SAP employee to access your systems, you must completely maintain the system data in the SAP Support Portal at <a target=\"_blank\" href=\"https://support.sap.com/system-data\">https://support.sap.com/system-data</a>. The portal also describes the maintenance of the system data. Note that SAP employees can ONLY log on to systems or servers that are maintained in the system data. SAP employees CANNOT manually select the target system or server, for example, by entering their IP addresses.<br />If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-SAPSMP-SYS in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).<br /><br />If you are using the SAP Solution Manager, you can use a background job to periodically transfer your system data to the SAP Support Portal (see SAP Note 993775).<br />If you have any problems or questions regarding the SAP Solution Manager, create an incident in the SAP Support Portal at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3). For questions about the installation or configuration, create the incident under the component SV-SMG-INS, and for questions and problems regarding the setting-up of the service connection, create the incident under the component SV-SMG-SVC.</p>\r\n<p><strong>2.3 Setting up or activating the service connections</strong></p>\r\n<p>Before you can open a certain service connection, you must set it up or activate it. A list of the individual service types including the corresponding SAP Note is available here:<br /><a target=\"_blank\" href=\"https://support.sap.com/remote-support/connection-types.html\">https://support.sap.com/remote-support/connection-types.html</a></p>\r\n<p>If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-NET in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).<br /><br />If you are using the SAP Solution Manager, you can use transaction SOLMAN_CONNECT to set up the service connections. You can also migrate connections that have already been set up in the SAP Support Portal to the Solution Manager. Further support for this issue is available in the SAP Support Portal under http://help.sap.com/saphelp_sm40/helpdata/en/28/9015dd452e48fabfb86098cda89e5b/frameset.htm or<br />http://help.sap.com/saphelp_sm40/helpdata/de/28/9015dd452e48fabfb86098cda89e5b/frameset.htm. If you have any problems or questions regarding the SAP Solution Manager, create an incident in the SAP Support Portal at http://service.sap.com/message (for help, see point 3). For questions about the installation or configuration, create the incident under the component SV-SMG-INS and for questions and problems regarding the setting-up of the service connection, create the incident under the component SV-SMG-SVC.</p>\r\n<p><strong>2.4 Opening the service connection in the SAP Support Portal</strong></p>\r\n<p>SAP Note 31515 describes how to open a service connection. To do this, use this link: <a target=\"_blank\" href=\"https://support.sap.com/remote-support/remote-connections.html\">https://support.sap.com/remote-support/remote-connections.html</a>. Define the logon data for your systems as described in SAP Note 508140.<br />If you have any problems or any questions about this paragraph, create an incident under the component XX-SER-SAPSMP in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).<br /><br />If you are using the SAP Solution Manager, you can use transaction SOLMAN_CONNECT to open the service connections.<br />If you have any problems or any questions regarding the SAP Solution Manager, create an incident under the component SV-SMG in the SAP Support Portal. This is located at <a target=\"_blank\" href=\"https://support.sap.com/kb-incidents.html\">https://support.sap.com/kb-incidents.html</a> (for help, see point 3).<br /><br /></p>\r\n<p><strong>3 Procedure if a connection malfunctions</strong><br /><strong>3.1 Creating a malfunction report</strong></p>\r\n<p>SAP Notes 74313 and 307037 describe the procedure for creating a malfunction report for SAP.<br />You should always consider the priority setting when you do this (for help on setting the priority, see SAP Note 67739).<br />If you want to create a malfunction report because to network problems, use component XX-SER-NET.<br /><br />If you want to create a malfunction report over the phone, contact your local Customer Interaction Center. The phone and fax numbers for this are listed in SAP Note 560499.<br /><br /></p>\r\n<p>&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-SER-NET-NEW (New or change connection requests to SAP Support)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C3303621)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D057892)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000035010/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000035010/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "984434", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/984434"}, {"RefNumber": "962516", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Solution Manager Services", "RefUrl": "/notes/962516"}, {"RefNumber": "812732", "RefComponent": "XX-SER-NET", "RefTitle": "R/3 support service connection", "RefUrl": "/notes/812732"}, {"RefNumber": "79411", "RefComponent": "XX-SER-NET", "RefTitle": "AS/400: 5250 connection for AS/400 customers", "RefUrl": "/notes/79411"}, {"RefNumber": "767071", "RefComponent": "BC-JAS-ADM", "RefTitle": "J2EE Telnet connection in remote support", "RefUrl": "/notes/767071"}, {"RefNumber": "701588", "RefComponent": "XX-SER-NET", "RefTitle": "Setting up Service Citrix MetaFrame connection", "RefUrl": "/notes/701588"}, {"RefNumber": "605795", "RefComponent": "XX-SER-NET", "RefTitle": "Windows Terminal Server connection in remote support", "RefUrl": "/notes/605795"}, {"RefNumber": "592085", "RefComponent": "XX-SER-NET", "RefTitle": "Set up the HTTP Connect service", "RefUrl": "/notes/592085"}, {"RefNumber": "560499", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer Interaction Center: Hotline - Email - Chat", "RefUrl": "/notes/560499"}, {"RefNumber": "545519", "RefComponent": "BC-DWB-JAV-RDB", "RefTitle": "Java remote debugging", "RefUrl": "/notes/545519"}, {"RefNumber": "486688", "RefComponent": "XX-SER-NET", "RefTitle": "Schedule VPN connection to SAP network", "RefUrl": "/notes/486688"}, {"RefNumber": "48243", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Integrating the SAProuter software into a firewall environment", "RefUrl": "/notes/48243"}, {"RefNumber": "39894", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in Japan", "RefUrl": "/notes/39894"}, {"RefNumber": "37001", "RefComponent": "XX-SER-NET", "RefTitle": "Telnet connection to customer systems", "RefUrl": "/notes/37001"}, {"RefNumber": "33953", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network provider for remote connection in EMEA", "RefUrl": "/notes/33953"}, {"RefNumber": "33135", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Guide for OSS1", "RefUrl": "/notes/33135"}, {"RefNumber": "3167682", "RefComponent": "XX-SER-NET", "RefTitle": "New Partner Remote Connectivity Framework", "RefUrl": "/notes/3167682"}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connections", "RefUrl": "/notes/31515"}, {"RefNumber": "30289", "RefComponent": "BC-CST-NI", "RefTitle": "SAProuter documentation", "RefUrl": "/notes/30289"}, {"RefNumber": "202344", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Set up SAP DB connection", "RefUrl": "/notes/202344"}, {"RefNumber": "1736373", "RefComponent": "SV-PERF", "RefTitle": "SAP GV/EW Service for MDM/SRM-MDM: Technical Preparation", "RefUrl": "/notes/1736373"}, {"RefNumber": "1718597", "RefComponent": "XX-SER-NET", "RefTitle": "Service connection \"SAP NI Connection\"", "RefUrl": "/notes/1718597"}, {"RefNumber": "1698817", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in China", "RefUrl": "/notes/1698817"}, {"RefNumber": "1635304", "RefComponent": "HAN", "RefTitle": "Central note for HANA support connections", "RefUrl": "/notes/1635304"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1518015", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1518015"}, {"RefNumber": "1444218", "RefComponent": "CRM-BTX-GWI", "RefTitle": "WTS connection to Client GWI system.", "RefUrl": "/notes/1444218"}, {"RefNumber": "1415371", "RefComponent": "EHS-SRC", "RefTitle": "REACH: Setting up service connections for SAP Support", "RefUrl": "/notes/1415371"}, {"RefNumber": "1251022", "RefComponent": "SV-PERF", "RefTitle": "SAP GA Service for MDM/SRM-MDM: Technical Preparation", "RefUrl": "/notes/1251022"}, {"RefNumber": "1178684", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"SNC processing failed\"", "RefUrl": "/notes/1178684"}, {"RefNumber": "1178631", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: connection to partner broken", "RefUrl": "/notes/1178631"}, {"RefNumber": "1178628", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: internal error", "RefUrl": "/notes/1178628"}, {"RefNumber": "1178624", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"Partner not reached\" (sapserv#)", "RefUrl": "/notes/1178624"}, {"RefNumber": "1178546", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: route permission denied (...)", "RefUrl": "/notes/1178546"}, {"RefNumber": "1122407", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Prerequisites for analyzing customer messages in BW-BEX-OT", "RefUrl": "/notes/1122407"}, {"RefNumber": "1072324", "RefComponent": "SV-SMG", "RefTitle": "HTTP Connect and SAP Solution Manager", "RefUrl": "/notes/1072324"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2845271", "RefComponent": "LOD-PER-OP", "RefTitle": "Triage Checklist for SAP Profitability and Performance Management (LOD-PER-OP)", "RefUrl": "/notes/2845271 "}, {"RefNumber": "2829985", "RefComponent": "KM-SEN", "RefTitle": "SAP Enable Now Remote Access for Support", "RefUrl": "/notes/2829985 "}, {"RefNumber": "2712016", "RefComponent": "XX-SER-NET", "RefTitle": "Additional Information for SAP Solution Delivery Centers (SDC)", "RefUrl": "/notes/2712016 "}, {"RefNumber": "2503453", "RefComponent": "BC-UPG-PRP", "RefTitle": "Creating place holder cases for a planned upgrade", "RefUrl": "/notes/2503453 "}, {"RefNumber": "3359598", "RefComponent": "XX-SER-NET-RCP", "RefTitle": "Important policies, compliance and remote connectivity standards for service and support access", "RefUrl": "/notes/3359598 "}, {"RefNumber": "2392227", "RefComponent": "EIM-DH-VO", "RefTitle": "Central note for HANA Vora support connections", "RefUrl": "/notes/2392227 "}, {"RefNumber": "639680", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connection to customer systems using sapservX only", "RefUrl": "/notes/639680 "}, {"RefNumber": "28976", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Remote connection data sheet", "RefUrl": "/notes/28976 "}, {"RefNumber": "1637249", "RefComponent": "CA-MDG", "RefTitle": "MDG: Information for efficient Support Incident Processing", "RefUrl": "/notes/1637249 "}, {"RefNumber": "560499", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer Interaction Center: Hotline - Email - Chat", "RefUrl": "/notes/560499 "}, {"RefNumber": "1912318", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "WTS Connection with NLA in remote support", "RefUrl": "/notes/1912318 "}, {"RefNumber": "605795", "RefComponent": "XX-SER-NET", "RefTitle": "Windows Terminal Server connection in remote support", "RefUrl": "/notes/605795 "}, {"RefNumber": "30289", "RefComponent": "BC-CST-NI", "RefTitle": "SAProuter documentation", "RefUrl": "/notes/30289 "}, {"RefNumber": "33953", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network provider for remote connection in EMEA", "RefUrl": "/notes/33953 "}, {"RefNumber": "33135", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Guide for OSS1", "RefUrl": "/notes/33135 "}, {"RefNumber": "48243", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Integrating the SAProuter software into a firewall environment", "RefUrl": "/notes/48243 "}, {"RefNumber": "39894", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in Japan", "RefUrl": "/notes/39894 "}, {"RefNumber": "195715", "RefComponent": "XX-SER-NET", "RefTitle": "Service connection types \"BW RFC\" and \"BW GUI\"", "RefUrl": "/notes/195715 "}, {"RefNumber": "1251022", "RefComponent": "SV-PERF", "RefTitle": "SAP GA Service for MDM/SRM-MDM: Technical Preparation", "RefUrl": "/notes/1251022 "}, {"RefNumber": "1736373", "RefComponent": "SV-PERF", "RefTitle": "SAP GV/EW Service for MDM/SRM-MDM: Technical Preparation", "RefUrl": "/notes/1736373 "}, {"RefNumber": "1072324", "RefComponent": "SV-SMG", "RefTitle": "HTTP Connect and SAP Solution Manager", "RefUrl": "/notes/1072324 "}, {"RefNumber": "1698817", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Network Provider for Remote Connection in China", "RefUrl": "/notes/1698817 "}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757 "}, {"RefNumber": "1122407", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Prerequisites for analyzing customer messages in BW-BEX-OT", "RefUrl": "/notes/1122407 "}, {"RefNumber": "1178684", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"SNC processing failed\"", "RefUrl": "/notes/1178684 "}, {"RefNumber": "1178631", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: connection to partner broken", "RefUrl": "/notes/1178631 "}, {"RefNumber": "1178628", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: internal error", "RefUrl": "/notes/1178628 "}, {"RefNumber": "1178624", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: \"Partner not reached\" (sapserv#)", "RefUrl": "/notes/1178624 "}, {"RefNumber": "1178546", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "No service connection: route permission denied (...)", "RefUrl": "/notes/1178546 "}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434 "}, {"RefNumber": "962516", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Solution Manager Services", "RefUrl": "/notes/962516 "}, {"RefNumber": "812732", "RefComponent": "XX-SER-NET", "RefTitle": "R/3 support service connection", "RefUrl": "/notes/812732 "}, {"RefNumber": "701588", "RefComponent": "XX-SER-NET", "RefTitle": "Setting up Service Citrix MetaFrame connection", "RefUrl": "/notes/701588 "}, {"RefNumber": "592085", "RefComponent": "XX-SER-NET", "RefTitle": "Set up the HTTP Connect service", "RefUrl": "/notes/592085 "}, {"RefNumber": "545519", "RefComponent": "BC-DWB-JAV-RDB", "RefTitle": "Java remote debugging", "RefUrl": "/notes/545519 "}, {"RefNumber": "486688", "RefComponent": "XX-SER-NET", "RefTitle": "Schedule VPN connection to SAP network", "RefUrl": "/notes/486688 "}, {"RefNumber": "202344", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Set up SAP DB connection", "RefUrl": "/notes/202344 "}, {"RefNumber": "37001", "RefComponent": "XX-SER-NET", "RefTitle": "Telnet connection to customer systems", "RefUrl": "/notes/37001 "}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service connections", "RefUrl": "/notes/31515 "}, {"RefNumber": "1444218", "RefComponent": "CRM-BTX-GWI", "RefTitle": "WTS connection to Client GWI system.", "RefUrl": "/notes/1444218 "}, {"RefNumber": "1518015", "RefComponent": "SV-ES-SAC", "RefTitle": "Enterprise Support Prerequisites", "RefUrl": "/notes/1518015 "}, {"RefNumber": "1530309", "RefComponent": "BC-FES-ITS", "RefTitle": "SAP NI service connection for WebGUI support", "RefUrl": "/notes/1530309 "}, {"RefNumber": "1415371", "RefComponent": "EHS-SRC", "RefTitle": "REACH: Setting up service connections for SAP Support", "RefUrl": "/notes/1415371 "}, {"RefNumber": "1392411", "RefComponent": "XX-SER-GEN", "RefTitle": "Remote Access to SRM 5.0 - 7.0 solutions", "RefUrl": "/notes/1392411 "}, {"RefNumber": "79411", "RefComponent": "XX-SER-NET", "RefTitle": "AS/400: 5250 connection for AS/400 customers", "RefUrl": "/notes/79411 "}, {"RefNumber": "767071", "RefComponent": "BC-JAS-ADM", "RefTitle": "J2EE Telnet connection in remote support", "RefUrl": "/notes/767071 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}