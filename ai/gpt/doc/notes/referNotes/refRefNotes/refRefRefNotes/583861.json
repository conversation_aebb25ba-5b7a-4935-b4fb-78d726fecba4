{"Request": {"Number": "583861", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015347162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000583861?language=E&token=1D39EDA35E8D92372DF5A486C096EC90"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000583861", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000583861/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "583861"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "583861 - UNIX: Errors due to Oracle executable"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Various errors indicate problems with the Oracle executable:</p> <UL><LI>At least one of the following Oracle errors occurs when you set up the connection to the database:<br /><br />ORA-03113: end-of-file on communication channel<br />ORA-07497: sdpri: cannot create trace file<br />ORA-12500: TNS:listener failed to start a dedicated server process<br />ORA-12545: Connect failed because target host or object does not exist<br />ORA-12546: TNS:permission denied<br />ORA-01034: ORACLE not&#x00A0;&#x00A0;available<br />ORA-27121: unable to determine size of shared memory segment<br />ORA-27123: unable to attach to shared memory segment<br />ORA-27140: attach to post/wait facility failed<br />ORA-09925: Unable to create audit trail file<br />ORA-09817: Write to audit file failed.<br />&lt;unix&gt; Error: 13: Permission denied</LI></UL> <UL><LI>A parallel query fails with:<br /><br />ORA-12801: error signaled in parallel query server<br />ORA-01114: IO error writing block to file<br />ORA-27072: skgfdisp: I/O error<br />&lt;unix&gt; Error 9: bad file number</LI></UL> <UL><LI>Errors such as the following occur in the live operation:<br /><br />ORA-01115: IO error reading block from file<br />ORA-00604: error occurred at recursive SQL level 1<br />ORA-01110: data file &lt;file&gt;<br />ORA-27041: unable to open file<br />&lt;unix&gt; Error: 13: Permission denied</LI></UL> <UL><LI>Oracle files such as the alert log are created under a user other than ora&lt;sid&gt;.</LI></UL> <UL><LI>\"CONNECT INTERNAL\", \"CONNECT/AS SYSDBA\" and \"CONNECT/AS SYSOPER\" require a password.</LI></UL> <UL><LI>As ora&lt;sid&gt;, you can only start the database with svrmgrl or sqlplus (not with startsap).<br /></LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The Oracle executable in the $ORACLE_HOME/bin directory has incorrect authorizations or does not exist at all.<br /><br />Some possible reasons for this are:</p> <UL><LI>Problems when relinking the Oracle software</LI></UL> <UL><LI>Previous hardware problems</LI></UL> <UL><LI>A backup with incorrect authorizations was imported</LI></UL> <UL><LI>Manual intervention by the administrator (moving or copying files and directories including the Oracle executable)<br /></LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Check whether the \"oracle\" file exists on $ORACLE_HOME/bin, has a size other than 0 and has the \"-rwsr-s--x\" authorizations. The owner must be ora&lt;sid&gt;. The group is dba.<br /><br />If the file is empty, you can create it again by relinking the software in accordance with Note 97953.<br /><br />You can assign authorizations and the owner/group as follows:<br /><br />chmod 6751 oracle<br />chown ora&lt;sid&gt; oracle<br />chgrp dba oracle<br /><br />Also check the authorization of the $ORACLE_HOME/bin directory. This should be set to \"drwxr-xr-x\".<br /><br />Continue to ensure that the file system that contains $ORACLE_HOME/bin supports the s-bit. Do not set the system to NOSUID.<br /><br />After you carry out the adjustments, restart Oracle.If an error such as ORA-27121 continues to occur after you set the s-bit, it may be caused by hanging shared memories or semaphores that are assigned to the user that was mistakenly used to start the Oracle database until now. To correct this situation, you can use tools such as ipcs/ipcrm or carry out a reboot.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021978)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000583861/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000583861/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97953", "RefComponent": "BC-DB-ORA", "RefTitle": "UNIX: Relinking of the Oracle executables", "RefUrl": "/notes/97953"}, {"RefNumber": "761171", "RefComponent": "XX-PROJ-GSC-CPC", "RefTitle": "CPCC Service Package 2.2.300", "RefUrl": "/notes/761171"}, {"RefNumber": "723641", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-12545", "RefUrl": "/notes/723641"}, {"RefNumber": "722966", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-12546", "RefUrl": "/notes/722966"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "546006", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with Oracle due to operating system errors", "RefUrl": "/notes/546006"}, {"RefNumber": "491174", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note for ORA-01034", "RefUrl": "/notes/491174"}, {"RefNumber": "480266", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with SYSDBA/SYSOPER/INTERNAL connect", "RefUrl": "/notes/480266"}, {"RefNumber": "437362", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite note ORA-12500", "RefUrl": "/notes/437362"}, {"RefNumber": "370278", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-3113 when starting SQLPLUS or SVRMGRL", "RefUrl": "/notes/370278"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "97953", "RefComponent": "BC-DB-ORA", "RefTitle": "UNIX: Relinking of the Oracle executables", "RefUrl": "/notes/97953 "}, {"RefNumber": "491174", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note for ORA-01034", "RefUrl": "/notes/491174 "}, {"RefNumber": "546006", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with Oracle due to operating system errors", "RefUrl": "/notes/546006 "}, {"RefNumber": "480266", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with SYSDBA/SYSOPER/INTERNAL connect", "RefUrl": "/notes/480266 "}, {"RefNumber": "370278", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-3113 when starting SQLPLUS or SVRMGRL", "RefUrl": "/notes/370278 "}, {"RefNumber": "722966", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-12546", "RefUrl": "/notes/722966 "}, {"RefNumber": "761171", "RefComponent": "XX-PROJ-GSC-CPC", "RefTitle": "CPCC Service Package 2.2.300", "RefUrl": "/notes/761171 "}, {"RefNumber": "723641", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-12545", "RefUrl": "/notes/723641 "}, {"RefNumber": "437362", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite note ORA-12500", "RefUrl": "/notes/437362 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}