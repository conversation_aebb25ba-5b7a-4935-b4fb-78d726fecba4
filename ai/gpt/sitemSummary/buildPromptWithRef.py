import math

import pandas as pd
import psycopg2
import json

from summarize_note_level3 import remove_img


def batch_insert(data):
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="cce6e3ded99172321cf1dd7e681ae92",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )

        cursor = connection.cursor()
        postgres_insert_query = """INSERT INTO SITEM_SUMMARY_WITH_REF ("GUID", "itemInfo", "itemSummary", "title", 
        "textId") VALUES (%s,%s,%s,%s,%s)"""

        cursor.executemany(postgres_insert_query, data)

        connection.commit()
        count = cursor.rowcount
        print(count, "Record inserted successfully into SITEM_SUMMARY_WITH_REF table")

    except (Exception, psycopg2.Error) as error:
        if (connection):
            print("Failed to insert record into SITEM_SUMMARY_WITH_REF table", error)

    finally:
        if (connection):
            cursor.close()
            connection.close()
            print("PostgreSQL connection is closed")


def load_json(note):
    with open(f'sitemNotes/{note}.json', 'r') as f:
        data = json.load(f)
    return data


def getNoteText(note, second_ref, third_ref):
    try:
        ref_note_content = load_json(note)
        if note in second_ref:
            ref_references = getReferences(note, second_ref, third_ref, [])
        else:
            ref_references = "No Reference."

        long_text = remove_img(ref_note_content['Response']['SAPNote']['LongText']['value'])

        return f"""

            {ref_note_content['Response']['SAPNote']['Title']['value']}
            {long_text}
            References: 
                This Note refers to:
                {ref_references}
            This is then end of SAP Note {ref_note_content['Response']['SAPNote']['Title']['value']}.
        """
    except:
        return f"Note {note} not found."


def getNoteTextSummary(note):
    try:
        with open(f'noteSummary/level1/{note}.txt', 'r') as f:
            ref_note_summary = f.read()
        ref_note_content = load_json(note)

        return f"""

            {ref_note_content['Response']['SAPNote']['Title']['value']}
            {ref_note_summary}
            This is then end of SAP Note {ref_note_content['Response']['SAPNote']['Title']['value']}.
        """
    except:
        return f"Note {note} not found."


def getRefDocText(ref_doc):
    filename = ref_doc['RefTitle'].replace('/', '_').replace('\\', '_').replace(':', ' ')
    filename = f'{filename}.txt'
    with open(f'refDocs/{filename}', 'r') as f:
        doc_content = f.read()
    return f"""

        {ref_doc['RefTitle']}[{ref_doc['RefUrl']}]
        {doc_content}
        This is then end of document {ref_doc['RefTitle']}[{ref_doc['RefUrl']}].

        """


def getReferences(notenumber, first_ref, second_ref, third_ref):
    result = []
    note = str(notenumber)
    if note in first_ref:
        for ref_note in first_ref[note]:
            if ref_note['RefNumber'] != "":
                result.append(getNoteText(ref_note['RefNumber'], second_ref, third_ref))
            else:
                result.append(getRefDocText(ref_note))
    if len(result) > 0:
        return '\n        '.join(result)
    else:
        return 'No Reference.'


def getReferencesSummary(notenumber, first_ref):
    result = []
    note = str(notenumber)
    if note in first_ref:
        for ref_note in first_ref[note]:
            if ref_note['RefNumber'] != "":
                result.append(getNoteTextSummary(ref_note['RefNumber']))
            else:
                result.append(getRefDocText(ref_note))
    if len(result) > 0:
        return '\n        '.join(result)
    else:
        return 'No Reference.'


act_df = pd.read_csv('activities.csv', keep_default_na=False)
sitem_df = pd.read_csv('sitem.csv', keep_default_na=False)

with (open('ref_map/first_ref.json', 'r') as f1,
      open('ref_map/second_ref.json', 'r') as f2,
      open('ref_map/third_ref.json', 'r') as f3):
    first_ref = json.load(f1)
    second_ref = json.load(f2)
    third_ref = json.load(f3)

act_dict = act_df.groupby('GUID').apply(lambda g: g.to_dict(orient='records')).to_dict()
sitem_list = sitem_df.to_dict('records')

big_ref_items = set(json.load(open('ref_map/big_ref_item.json', 'r')))

result = []

for sitem in sitem_list:
    guid = sitem['GUID']
    note = sitem['SAPNOTE']
    activities = "      No Activities"
    if guid in big_ref_items:
        references = getReferencesSummary(note, first_ref)
    else:
        references = getReferences(note, first_ref, second_ref, third_ref)
    if math.isnan(note):
        print(f'Warnning, this sitem have no note: {str(sitem)}')
        continue
    note_content = load_json(note)
    long_text = remove_img(note_content['Response']['SAPNote']['LongText']['value'])
    if (guid in act_dict):
        activities = []
        for act in act_dict[guid]:
            add_info = act['ACTADDINFO']
            if len(add_info) == 0:
                add_info = "No additional information."
            activities.append(f"""
            Activity: {act['ACTTITLE']}
            Phase: {act['ACTPHASE']}
            Condition: {act['ACTCONDITION']}
            Additional Information: {act['ACTADDINFO']}
            """)
        activities = ''.join(activities)
    item_info = f"""
    ID: {sitem['TEXTID']}
    Title: {sitem['TITLE']}
    Business Impact Note: {sitem['SAPNOTE']}

    {note_content['Response']['SAPNote']['Title']['value']}

    {long_text}

    References: 
        This Business Impact Note refers to:
        {references}

    Activities:
{str(activities)}
    """

    result.append((guid, item_info, '', sitem['TITLE'], sitem['TEXTID']))

batch_insert(result)
