SAP Note 2628704 addresses the necessary code adaptations for existing SAP ERP ECC60 systems converting to or upgrading to SAP S/4HANA On-Premise Edition 1809 (or higher versions) in the context of the Amount Field Length Extension (AFLE) that has been carried out in S/4HANA.

Summary of the note:

The note provides guidance on how to adapt custom ABAP code due to the extension of selected currency amount fields and related data types. Currency amount fields have been extended from 9-22 digits including 2 decimals, to 23 digits including 2 decimals, affecting DEC, CHAR, and NUMC data types that may hold amount values.

This note emphasizes that while most development artifacts within SAP will be consistently adjusted, manual adjustments may be required for certain artifacts such as BAPIs (Business Application Programming Interfaces) and RFC (Remote Function Call) modules due to the nature of the RFC protocol, which demands binary compatibility for changes in field length.

For BAPI and RFC interfaces, an additional parameter with extended length has been introduced to maintain compatibility. The note recommends paying attention to code involving such interfaces and manually checking and adapting it as needed.

Two other SAP Notes are referenced for further guidance and background information:

1. SAP Note 2628724 provides instructions for adapting code involving BAPIs and RFCs when calling them locally (within the same system) or remotely (across different systems). It also addresses the introduction of new parameters or fields for compatibility and how to deal with these adjustments when coding.

2. SAP Note 2628654 describes the motivation and scope of the amount field length extension project, including why it is necessary for handling larger financial figures, especially by banks and financial institutions.

3. SAP Note 2628040 offers general information about the amount field length extension, what areas are affected, how existing domains and data elements are replaced automatically, the necessity to decouple untargeted areas, and ensuring compatibility with external interfaces.

As a result, developers responsible for systems that are undergoing conversion or upgrade to S/4HANA need to analyze and adjust custom ABAP code to ensure compatibility with the extended fields. The note helps to identify where adjustments might be necessary and guides developers through the analysis and adjustment process, particularly for interface calls that involve the affected data elements.