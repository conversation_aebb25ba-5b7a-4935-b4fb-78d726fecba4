SAP Note 2199837 addresses changes in field lengths for certain fields within the interfaces of specific RFC (Remote Function Call) function modules. This change affects structures used by these function modules. 

The symptom addressed by the note is the alteration in the lengths of the affected fields in the following RFC function modules:

1. COCF_SN_CREATE_EAM
2. COCF_SN_CREATE_PP
3. COCF_SN_GET_LIST_EAM
4. COCF_SN_GET_LIST_PP

In these function modules, the corresponding changes in structures are:

- For COCF_SN_CREATE_EAM and COCF_SN_CREATE_PP, structure RIQS5 is affected, with fields BAUTL, MATNR, EMATNR, and RM_MATNR undergoing length changes.
- For COCF_SN_GET_LIST_EAM and COCF_SN_GET_LIST_PP, structure COCF_S_SN_LIST_FLAT is affected, with fields MATNR, EMATNR, and RM_MATNR undergoing changes.

Other terms: The note mentions "Shift note" but does not elaborate on it.

The reason for these changes is the extension of the material number length from 18 to 40 characters.

As a consequence of the field length changes, customers who use these RFC function modules in their custom code must adjust the lengths of the corresponding fields in their custom source code to accommodate the new lengths.

There are no references cited in the note, indicating that this note is self-contained and does not refer to any other SAP notes or documentation.