SAP Note 1830886 addresses the issue of field mapping between a business partner's first name and last name during Customer/Vendor Integration (CVI) synchronization in SAP. The standard system logic is explained for synchronizing business partner data to customer or vendor master data and vice versa, including how names are handled depending on the total character length and the field availability.

In detail, the standard synchronization behavior is as follows:

- When synchronizing from business partner to customer or vendor, the first and last names are combined into the field NAME1 of the customer or vendor as long as they do not exceed 40 characters together. If the combined length exceeds 40 characters, the first name goes to NAME1 and the last name to NAME2.

- The synchronization direction from customer or vendor to business partner is not available as of S/4HANA release 1511. Originally, if both "Name 1" and "Name 2" were filled, "Name 1" would map to the business partner's first name and "Name 2" to the last name. If "Name 2" was empty, "Name 1" would map to the business partner's last name.

The note explains that this logic was maintained for backward compatibility with older versions of CVI, but recognizes that some customers might prefer a different mapping, specifically wanting the last name to map to NAME1 and the first name to map to NAME2.

To accommodate such preferences, the note suggests creating an implementation of the BAdI (Business Add-In) CVI_CUSTOM_MAPPER. The note provides instructions for implementing methods map_bp_to_customer and map_customer_to_bp or map_bp_to_vendor and map_vendor_to_bp within the BAdI to override the standard logic. It also includes an example of how to implement the 'map_bp_to_customer' method in the enhancement implementation.

In summary, SAP Note 1830886 provides guidance on how to customize the mapping of the first name and last name fields for synchronization between business partners and customers or vendors using a BAdI, to cater to different customer requirements that may not align with the standard system logic.