SAP Note 1771304 addresses an issue where not all database connections from the table DBCON are permitted or listed in the value help when executing the report RDA_MAINTAIN. This problem causes limitations for users trying to use database connectivity features.

The note does not specify the reasons or prerequisites causing this issue, suggesting that it might be a straightforward technical problem within the program or the way it interacts with the DBCON table.

As a solution, SAP recommends implementing the correction instructions attached to this note. The corrective update is provided within the Support Package SAPK-10001INSWT2DB, indicating that the installation of this package should resolve the issue.

This note also refers to SAP Note 1694697, which details the SAP Business Application Accelerator powered by HANA. While speaking to the broader context of HANA acceleration and related issues (including this DBCON connectivity concern), note 1694697 describes the end of the accelerator's distribution and support parameters, referencing other notes that deal with associated installation and operational challenges. However, the focus of note 1771304 is specifically on permitting all database connections for users using the RDA_MAINTAIN report.