SAP Note 1855275 addresses an issue where loading data via a 3.x data flow into an InfoCube with the mass cube writer in SAP BW (Business Warehouse) fails. The errors encountered are RSAU 499, which occur in the functions `write_ic` and `UPDATE_INFOCUBE` for the target InfoCube.

The root cause of the problem is identified as a program error.

To resolve this issue, SAP provides the following solutions, depending on the version of SAP NetWeaver BW:

1. For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010). Details about this package can be found in SAP Note 1810084 titled "SAPBWNews NW 7.30 BW ABAP SP10".

2. For SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1): Import Support Package 09 (SAPKW73109). More information is provided in SAP Note 1847231, "SAPBWNews NW BW 7.31/7.03 ABAP SP9".

3. For SAP NetWeaver BW 7.40: Import Support Package 03 (SAPKW74003). Details are found in SAP Note 1818593, "SAPBWNews NW BW 7.4 ABAP SP03".

In urgent situations where immediate correction is needed, SAP suggests implementing advance correction instructions. However, users should first refer to SAP Note 1668882 for guidance on using transaction SNOTE, which is the standard tool for applying SAP Notes.

Lastly, SAP Notes referenced above may be accessible before the release of the corresponding Support Package, indicated by the term "Preliminary version" in the short text of the SAP Note.