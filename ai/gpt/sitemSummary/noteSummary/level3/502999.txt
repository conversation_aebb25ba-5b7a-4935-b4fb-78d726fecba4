SAP Note 502999 outlines the process for installing a 6.20 kernel into a system with a 6.10 Web Application Server (AS) to correct an error in the 6.10 kernel.

**Key Points:**

- The 6.20 kernel is downward-compatible and can be used to correct the error without the need for an R/3 upgrade.
- Installation applies to all systems with Web AS 6.10 (component SAP_BASIS).
- The 6.20 kernel has been available to customers since July 2002.
- Caution is advised as the 6.20 kernel may not be available for all SAP products on all platforms. Specific combinations of supported SAP products, operating systems, and database versions can be found at http://service.sap.com/platforms.
- For AIX users, particularly those on BW 3.0B and EBP 3.5, it is recommended to use AIX 4.3.3 with Oracle 8.1.7, and to upgrade to AIX 5.1 when available (planned for November 2002).
- Database upgrades might also be necessary before upgrading the kernel. 
- Links to relevant SAP Notes for database compatibility are provided.
- After installing a 6.20 kernel, only 6.20 patches should be used for kernel corrections.
- The existing 6.10 GUI is compatible with the 6.20 kernel, so a new front-end installation is not required.

**Installation Steps:**

1. Stop the SAP system and saposcol service.
2. Terminate SAProuter and delete the statistic file.
3. On the host of the central instance and all application servers, insert and mount the 6.20 kernel CD.
4. Follow platform-specific instructions for saving the old kernel, unpacking the new one, importing additional programs if required, and starting the SAP System and services post-upgrade.
5. Delete automatically generated Stored Procedures on SQL servers. Execute BW-specific reports if on a BW system.

**Additional Information:**

- Front-end software installation from the CD is optional and described in the guide for installing SAP front-end software for PCs version 6.10.
- Caution is highlighted when dealing with certain executables (especially disp+work), as later corrections might be needed through patches available on the SAP Service Marketplace.
- A procedure for resetting to the old kernel is described in case the new kernel does not perform as expected. 

In conclusion, SAP Note 502999 provides a detailed procedure to correct an error in the 6.10 kernel by installing a downward-compatible 6.20 kernel, advising care with database and platform compatibilities, and offering a path to revert to the old kernel if necessary.