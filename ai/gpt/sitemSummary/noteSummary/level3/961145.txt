The SAP Note 961145 addresses an issue where long variable names, those exceeding 8 characters, are not properly handled in the MDX (Multidimensional Expressions) environment when used with SAP BI 7.0, which leads to incorrect UNIQUE_NAME creation. This problem is classified as a program error.

The note specifies that the solution to this problem is to import Support Package 09 for SAP NetWeaver 2004s BI (BI Patch09 or SAPKW70009) into the BI system. The details of this Support Package are outlined in SAP Note 0914303 titled "SAPBINews BI 7.0 SP09." This Note provides more information about the Support Package and is to be released to customers.

For those who need an immediate resolution, the note suggests using the correction instructions provided. It also mentions that related notes might be available to give advance information before the release of the Support Package. However, such notes would contain the term "preliminary version" in their short text, indicating that they are an early release and not the final version.