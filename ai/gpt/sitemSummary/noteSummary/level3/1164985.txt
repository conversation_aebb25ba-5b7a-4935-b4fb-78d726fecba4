The SAP Note 1164985 addresses an issue where error message DB 634 is displayed when executing drilldown reports with a variant that includes period or year fields in the selection screen. Additionally, users are unable to enter the initial value "#" for date or seven-digit period/year fields (format YYYPPP) on the selection screen, preventing the execution of the selection with the initial value.

Key terms related to this issue include drilldown reporting, various transaction codes (such as KCR0, KE30), error messages (DB632, DB 634), variants, initial values, the hash symbol (#), period/year fields (PERIO), and the fiscal year (JAHRPERBL).

The cause of this problem is identified as a program error.

The solution provided applies to all SAP releases higher than 6.03 and involves several steps:
1. Creation of the function group KYPG_CONV in package KCDD with transaction SE80.
2. Implementation of the correction instructions provided in the note.
3. Creation of specific domains (KCDD_PERIO and KCDD_DATS) and corresponding data elements (also called KCDD_PERIO and KCDD_DATS) in package KCDD using transaction SE11, which include conversion routines and allow the "#" as an initial value.
4. Regeneration of the affected reports.

It also instructs users on how to convert old variants to accommodate the changes using the program ZKCDD_CONV_VARIANT_PERI, which is related to corrections from another SAP Note, 1160685. This program must be run after importing the transport from the said note. Users are advised to ensure that the user setting for the date format matches the original setting used when the variant was saved, to avoid any errors during the conversion process.

In summary, this SAP Note provides a corrective action for the drilldown report variant selection issue, detailing steps for remediation and advising users on handling old variants and user settings to ensure a smooth transition.