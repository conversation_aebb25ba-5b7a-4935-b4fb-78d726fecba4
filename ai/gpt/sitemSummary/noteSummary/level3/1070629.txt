SAP Note 1070629 is a comprehensive FAQ regarding migration to the new General Ledger (G/L) Accounting in SAP. It addresses common questions and concerns associated with the transition from classic G/L Accounting to the new G/L structure and the implementation of new functions in SAP systems.

Key points summarized from the note:

1. Certification for migration to new General Ledger Accounting is covered in SAP Note 868278.

2. A detailed migration guide for the new G/L can be found on SAP's support portal (support.sap.com/glmig).

3. To calculate data volume and evaluate the effects on the system, SAP Notes 820495 and 1045430 should be referenced.

4. Restrictions during new G/L migration regarding SAP CFM (Treasury) and/or SAP CML (Loans) exist due to technical limitations, and SAP Consulting should be contacted for further procedures.

5. Information on restrictions around document splitting during and after migration can be found in SAP Notes 966000 and 985298.

6. Special Purpose Ledgers can be transferred to the new G/L if they comply with its requirements. Currency considerations in ledger migration are also discussed.

7. In multi-client systems, configuration, migration, and activation can be independently managed for new G/L for each productive client.

8. It is not recommended to upgrade to mySAP ERP 2004 (ECC5.0) or SAP ERP 6.00 (ECC6.0) and migrate to the new G/L in the same fiscal year.

9. A local currency changeover must be performed in a different fiscal year to the migration to the new G/L if document splitting is activated.

10. A current copy of the production client is recommended for test migrations, and database and operating systems should be comparable to the production environment.

11. Various checks for data consistency in classic G/L should be carried out using specific SAP programs before starting test migrations.

12-30. Additional points cover various topics, including recommendations for account control in G/L accounts before test migration, the IMG path for new G/L in test systems, the addition of fields to tables, setting up transport systems, and more.

31-39. Further FAQs address concerns about transactions, events related to document splitting, clearing documents, batch input, partner assignments, and transaction FAGL_MIG_SIM_SPL.

40. The relationship between document splitting and validation is explained, with different validation steps to be applied before and after document splitting.

41-51. Tips and guidelines cover zero balance clearing accounts, postings to previous fiscal years, handling inactive company codes, BAdIs for balance carryforward, transaction FBCB considerations, and more.

52-60. Additional questions range from transferring planning data from CO-OM to the new G/L, handling local currency changeover in the new G/L, and why the program FAGL_MIG_OPITEMS_CRESPLIT cannot be parallelized.

61-63. The new general ledger's compatibility with various SAP modules and systems, considerations for the database before productive migration, and the possibility of minimizing downtime for productive migration are discussed.

64-66. Topics include deactivating classic Profit Center Accounting after activating the new G/L, tables required for migration, and changes to Customizing the new G/L after it's been used productively.

67-69. The consistency of data with Asset Accounting and reconciliations with Controlling are covered.

70-80. Special topics include handling of allocations for balance sheet accounts, line item displays, functional area and segment updates, and accessing information on the SAP General Ledger Migration service.

81-83. Clarifications about the difference between the migration date and live migration date, compatibility of the new G/L with FI-CA (Contract Accounting), and whether a Unicode system is required for migration are provided.

84-86. The ability to upgrade to mySAP ERP and migrate to the new G/L, considerations for implementing parallel accounting, and the usage document splitting post-migration are discussed.

87-89. Additional FAQs touch upon the use of profit center mass maintenance, ledger-specific postings with RFBIBL00, and the inclusion of new customer fields during migration.

90. Lastly, FAQ 90 is highlighted, which describes where and how to install the migration cockpit in the system.