SAP Note 635608 provides information on release restrictions for SAP R/3 Enterprise 47x200. It outlines specific functions and components that are either released with restrictions, require approval or consultation with SAP before use, or are not released for productive use. Each restricted feature has specific prerequisites and notes indicating the state of the release and providing relevant contact information for further inquiries.

In summary, here's a breakdown of the different statuses for various functions and components detailed within the note:

1. **Release with Restrictions**: Several components and functions such as BC-FES-WGU, CA-JVA, CA-JVA-JVA, CFM-TM, CO-PC-ACT-WIP, etc., are released with certain limitations. Customers should observe these limitations for a productive environment and can refer to the specified SAP Notes or contact the provided email addresses for further information.

2. **Release with Approval of SAP or After Consultation with SAP**: This status applies to numerous components like CA-JVA-PRC, CFM-TM, FS-CMS, IS-B-BCA, and others. Before using these features, customers must seek approval or consult with SAP directly through the provided contact emails.

3. **No Release**: There's a list of features that are explicitly not released for usage, such as Hedge Management in CFM-TM and Family Pricing in LO-MD-RPC. Essentially, these components are not to be used in a productive environment until a release has been announced by SAP.

4. **Previously Restricted Functions Now Available**: The note also lists several functions that had restrictions in the past but are now released. Past restrictions can be checked against specific SAP Notes mentioned in the document.

Furthermore, the note references other SAP Notes for detailed information on particular topics like Unicode or platform compatibilities. The status of the restrictions mentioned is aligned with the last change date of this SAP Note.

When planning to use these components or functions, customers should proceed with caution, consulting with SAP when required and thoroughly testing any features with restrictions in a non-productive environment to ensure they meet the necessary requirements before proceeding to a live usage scenario.