SAP Note 518307 addresses an issue that arises during the conversion of TR (Transaction Receiver) Business Partners (BP) to SAP BP when those business partners have a deletion flag. According to the symptom described in the note, when processing the conversion in blocks, the first TR BP with a deletion flag in a block is not converted, which is the expected behavior. However, the subsequent BPs with deletion flags within the same block are incorrectly converted.

This behavior is identified as a program error. The note informs us that it has been completely revised by SAP Note 541293 and the program logic has changed to also convert all partners, even those marked with the deletion flag.

To remedy this issue, the note provides instructions for implementing source code changes. It recommends replacing the original FORM routine 'block' in the program code with a new FORM routine 'block' as laid out in the correction instructions, which are marked by "START OF INSERTION" and "END OF INSERTION" in the note.

The other terms mentioned in the note are "RFTBUP01" and "BP000-DEL_IND," which likely refer to the program and a field or indicator related to the deletion flag on the business partner record respectively.

As an SAP expert, one would be expected to follow the instruction in the note or refer to SAP Note 541293 for the revised program logic to ensure that BPs with deletion flags are handled correctly during the conversion process.