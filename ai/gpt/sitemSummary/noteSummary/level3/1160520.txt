SAP Note 1160520 pertains to the behavior of SAP Business Intelligence (BI) when used in conjunction with the BI Accelerator (BIA) for non-cumulative queries, particularly concerning stock values and query performance.

Here's a summary of the note:

**Symptom:**
The SAP Note addresses the functioning of BI with BIA for non-cumulative queries.

**Other Terms:**
References to non-cumulative values, BIA, BI, stock values, query performance, TREX, and NCUM (non-cumulative).

**Reason and Prerequisites:**
The note does not explicitly list reasons or prerequisites but seems to assume a working knowledge of BIA and non-cumulative data in BI.

**Solution:**
The document provides a general overview of non-cumulative calculation, followed by performance tips for non-cumulative queries, through the links to SAP Help documentation.

Key points from the solution section include:
- Using BIA can speed up the retrieval of stock values and changes from the infocube data. However, it does not speed up the computation of absolute stock values, as this computation occurs in the BI analytics engine.
- The Aggregation Manager Query Statistics in BIA will show two types of data access during non-cumulative queries: one for the latest stock values and one for stock changes.
- For non-cumulative queries, the validity table is always considered, and it's suggested to use as few validity-determining characteristics as possible to optimize performance.
- Compressing non-cumulative infocubes regularly is crucial to reduce data volume and maintain query performance. The BIA index should be rebuilt after compression to utilize compressed data for non-cumulative infocubes.
- The note advises performing a performance measurement to understand the impact of compressed vs. uncompressed data on query performance and determine the frequency with which the BIA index should be rebuilt for non-cumulative infocubes.

The note finishes with a step-by-step guide for measuring the performance impact of uncompressed data and determining the need for rebuilding the BIA index for non-cumulative infocubes.