SAP Note 710720 provides information on Release 6.40 of the SAP GUI for Windows, including new features, limitations, compatibility issues, delivery, and maintenance details.

**Summary of SAP Note 710720:**

1. **New Functions:**
   - Improved support for Unicode, especially beneficial for Asian language scenarios.
   - Enhanced accessibility; for details refer to SAP Note 740785.
   - Increased usability for UI elements and lists.
   - Redesigned SAP logon with better usability and new features.
   - Optimized icon design for both SAP GUI and application icons.
   - Support for Windows XP Theme for those using Classic Design SAP GUI.
   - Introduction of Web SAP Console, a successor of the SAP Console.
   - New ABAP Editor (as of patch level 10 for SAP GUI 6.40), with more features, for use with SAP NetWeaver 2004s and higher.

2. **Additional Functions from SAP GUI 6.20:**
   - Installation compression to reduce data transfer via SAPSetup.
   - Delivery of Adobe Template Designer with the SAP GUI.

3. **Limitations:**
   - SAP GUI 6.40 does not support older Windows versions like Windows 95, Windows 98, Windows ME, and Windows NT. It supports Windows 2000 and Windows XP/2003 Server.

4. **Compatibility:**
   - SAP applications running on Web AS 6.30/6.40 can use SAP GUI 6.20, but to utilize new features, SAP GUI 6.40 is needed.
   - First-time SAP software users should start with Release 6.40 due to its superior Unicode support and accessibility.

5. **Delivery:**
   - A beta program for Unicode pilot customers was available from mid-December until June 21, 2004, with SAP GUI provided for testing purposes only.
   - The official general release was on June 21, 2004, and is available on the SAP Service Marketplace.
   - The SAP GUI 6.40 is distributed on a DVD included with the SAP NetWeaver 6.40 package.

6. **Maintenance:**
   - For maintenance information of the SAP GUI for Windows 6.40, refer to SAP Note 147519.

This note is essential for users who plan to upgrade or have upgraded to SAP GUI 6.40, as it contains information about new features and changes from the previous version. Users should note the compatibility and system requirements to ensure they meet the necessary prerequisites for a successful installation and operation.