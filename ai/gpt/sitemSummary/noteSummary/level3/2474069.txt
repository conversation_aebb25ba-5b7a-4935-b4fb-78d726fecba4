SAP Note 2474069 addresses the issue where General Ledger (G/L) planning data and transactions are inaccessible after migrating from an ERP system to an S/4HANA Finance or S/4HANA system. The classic FI-GL (new) planning is unsupported in S/4HANA, having been replaced by BPC for S/4HANA.

**Symptom:**
Users are unable to access G/L planning transactions or Integrated Management Information System (IMIS) activities for G/L planning post-migration, and planning data columns show values of 0.

**Reason:**
Legacy ERP FI-GL (new) planning is obsolete in S/4HANA and replaced by BPC for S/4HANA.

**Solution:**
The note outlines steps to temporarily reactivate G/L planning functions in S/4HANA. It includes implementing specific SAP Notes, running the report FINS_SET_DEPR_STATUS, making entries into the view FINS_DEPR_OBJECT via SM30, setting up various transactions, and ensuring that program entries are included if using BAPI for external data transfer.

It also specifies conditions for continuing the use of the old G/L planning, such as limitations on ledgers and customer fields. Furthermore, for greenfield or classic G/L migrations, additional steps involve setting up ledgers and currencies, executing report ZCOPY_ENTRIES_FOR_GL_PLANNING, and importing planning layouts.

Users must add recommended transactions to their favorites manually since the traditional menu paths and IMG activities are no longer present. Instruction is given for handling plan/actual reporting transactions and copying data to the reactivated planning tables.

Lastly, note 2474069 is not needed if reactivation is only for FI-SL planning transactions; in this case, SAP Note 2403964 would be required instead.