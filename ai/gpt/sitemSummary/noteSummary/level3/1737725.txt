This SAP Note 1737725 revolves around the release and information pertaining to the Support Package (SP) Stacks for SAP Enhancement Package 3 for SAP Customer Relationship Management (CRM) 7.0. 

Key information and directives include:

- Reading the note completely BEFORE applying SP Stacks for SAP Enhancement Package 3 for SAP CRM 7.0.
- Regularly checking the note for updates post-SP stack release, documented under "Changes made after Release of SP Stack <XX>".
- General information regarding SP Stacks can be found on SAP Service Marketplace with schedules for maintenance deliveries.
- Using SAP Solution Manager's Maintenance Planner for planning system maintenance and add-on installation. Maintenance Optimizer is no longer supported.
- Applying the latest version of SPAM prior to any other Support Packages.

**Installation & Upgrade Information**:

- Upgrades are possible from any Support Package level of the supported release.
- For specific components like CPRXRPM and MapBox, prerequisite SAP Notes must be applied before the upgrade.
- For SAP Business Suite 7 Innovations 2013, including Enhancement Package 3 for SAP CRM 7.0, dual-stack is not supported anymore.

**Important Consideration**:

- With SAP Business Suite 7 Innovations 2013 initially delivered with SP01-stack, systems must have at least SP stack 01 for productive usage.
- For planning a NetWeaver upgrade, refer to the SAP NetWeaver Upgrade Master Guide for specifics about prerequisites, upgrade paths, and dependencies.

**Support Package Stack 23 (03/2023)**:

- This stack is based on SAP NetWeaver 7.40 SP Stack 29 (01/2023).
- Before update to SP Stack 23, minimum required SAP HANA database revision is 122.

**SP Equivalence Levels**:

- The equivalence levels determine upgrade paths to Enhancement Packages.
- The Maintenance Planner calculates the correct Support Packages.
- Minimum SP Stack Levels for JAVA instances are defined to simplify maintenance in large landscapes.

**SAP NetWeaver SP Stack Levels**:

- Minimum SP Stack levels defined based on Landscape Patterns (HUB or SIDECAR).
- Recommended NetWeaver SP Stacks for JAVA instances below the defined level.

**Important Notes to be applied as part of the stack**:

- Various SAP Notes are listed with descriptions touching upon specific issues and improvements like "CRM WebUI Account Search when using Street field" or "High Runtime of Account Search."

**Information regarding support for SAP HANA 2**:

- SAP HANA database 2.0 SP SPS02 Revision 22 or higher is released for SAP CRM 7.0 EhP 3 at all SP stack levels.

The note advises on a range of installation prerequisites, important considerations, and information regarding upgrade paths for different components within the landscape. It also emphasizes the usage of the Maintenance Planner and the importance of keeping systems updated according to the recommended SP stack levels for both ABAP and Java instances. SAP Notes associated with SP Stack 23 are enumerated for direct application. Additionally, support for SAP HANA 2 is highlighted. Users are suggested to refer to this note for any updates post-SP stack release.