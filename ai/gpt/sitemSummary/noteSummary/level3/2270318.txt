SAP Note 2270318 addresses the discontinuation of the SEM Banking functionality in SAP S/4HANA systems. It is relevant for customers who are transitioning to SAP S/4HANA and have previously been using SEM Banking.

Key points from the note are:

1. SEM Banking is not included in SAP S/4HANA.
2. Parts of SEM Banking had been replaced by applications like Bank Analyzer, but there are still functions with no direct successors.
3. SEM Banking has been technically disabled in SAP S/4HANA by:
   - Hiding the SEM Banking menu.
   - Hiding the SEM Banking implementation guide (IMG).
   - Causing central SEM Banking transactions to produce a terminating error message.
4. All SEM Banking transaction codes, related to "Datapool," "Profit Analyzer," "Risk Analyzer," and "Strategy Analyzer," will not be available in SAP S/4HANA.
5. Clients using SEM Banking must consider alternatives, such as replacing SEM Banking functionality with Bank Analyzer or maintaining SEM Banking on a separate ERP system.
6. Customers should consider archiving or deleting obsolete SEM Banking data before migrating to SAP S/4HANA to prevent unnecessary data transfer.
7. Customers need to conduct a specific analysis to ensure that only obsolete data is archived or deleted.
8. The note provides guidance on determining the relevancy of SEM Banking functionalities within the system.

Related actions and checks regarding SEM Banking in the context of an SAP S/4HANA transition are also advised. The note mentions SAP Note 2211665 for more details related to custom code implications of the change.