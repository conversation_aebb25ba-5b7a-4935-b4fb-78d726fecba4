SAP Note 2562922 addresses an issue in the Revenue Accounting module where Revenue Accounting Items (RAIs) for planned invoices (identified as SDPI source item type) are incorrectly processed under a new accounting principle using the transaction FARR_RAI_PROC_NEWACP. The problem arises when these planned invoice RAIs are moved to a 'Processed' status (status 4) even if their posting date is before the transfer date. Additionally, if the Inception Date of the related order RAI is also before the transfer date, the system fails to create a performance obligation for the new accounting principle, resulting in the error message 628(FARR_RAI) stating "Performance obligation to be invoiced could not be determined."

The note identifies the cause as a program error where planned invoices are incorrectly sent to the Revenue Accounting (RA) engine and should not be reprocessed for the new accounting principle.

The solution advised in the note is to implement the fixes provided in the SAP Note or the corresponding support package, implying that there is a correction available. After applying the fix, planned invoice RAIs (SDPI) should not be processed by the transaction FARR_RAI_PROC_NEWACP, and this should resolve the issue. The SAP Note should be applied to prevent such incorrect processing in the system. 

Additional terms related to the issue are FARR, Revenue Accounting, Initial Load, Migration, FARR_RAI628, and FARR_RAI 628, which could be keywords used to find this SAP Note in the support system.