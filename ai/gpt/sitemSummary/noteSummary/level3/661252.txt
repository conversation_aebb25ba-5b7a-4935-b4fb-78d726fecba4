SAP Note 661252 provides detailed guidance for upgrading to Basis Release 6.40 on IBM DB2 for z/OS (DB2-z/OS). Here's a summary of the key points covered in the note:

- **Symptom**: The note outlines additional procedures required for upgrading to SAP Basis Release 6.40 on DB2-z/OS.

- **Other Terms**: Upgrades, updates, and maintenance levels for DB2 UDB on OS/390 and DB2-z/OS.

- **Reason and Prerequisites**: This note is specific to upgrades to Basis Release 6.40 and should be reviewed before starting the upgrade process.

- **Solution**: 
  - A reminder to check the note again before upgrading as it is regularly updated.
  - The upgrade can be performed on AIX, zLinux, z/OS, and Windows (except Solaris).
  - JDK 1.4 is the minimum Java version requirement on all operating systems.
  - For high-availability systems, certain requirements and upgrade strategies must be followed.
  - Any incorrect documentation, for instance, unsupported operating systems, should be corrected with AIX, and zLinux actually being supported.
  - Various preparatory actions must be taken before starting the upgrade, such as:
    - Consistency checks and adjustments for database fields.
    - DB2-z/OS setup with a specific ZPARM parameter value.
    - Installation of the required version of DB2 Connect.
    - Applying the latest DDIC corrections and upgrade repairs.
    - Exchanging certain upgrade tools and libraries.
  - Specific actions and checks during various upgrade phases.
    - Phase INITPUT_PRE: Option to convert all indexes to NOT PADDED for performance.
    - Phase HIAV_CHK: Checks for high-availability system status.
    - Phase DBPREP_CHK: Rebuilding any reported pending indexes.
    - Phase DIFFEXPDDIV: Instructions for handling specific error messages.
    - Phase XPRAS_UPG: Solutions to potential issues during this phase, like cleaning up shared memory.
  - Post-upgrade actions, such as resetting ZPARM parameters and addressing inconsistencies in transaction DB02.

By following the detailed steps and taking note of the cautions and prerequisites mentioned, a successful upgrade to Basis Release 6.40 should be achievable for systems running on DB2-z/OS. It is emphasized that the note should be referred to just before starting the upgrade since it is regularly updated with the latest information.