SAP Note 715176 addresses an issue where the revaluation document is incorrectly posted with an incorrect sign during return deliveries with movement types 122 or 123, which is a cancellation of the return into the previous month. This error occurs when the current standard price differs from the standard price in the previous period. Consequently, the amounts recorded in the SAP Business Warehouse (BW) have mixed up plus/minus signs.

Key terms related to this issue are:
- SHKZG
- ZU_ABGANG
- Movement type 122 and 123
- LMCRSF02
- XMCMSEG-BWSWITCH
- Price change
- 2LIS_03_BF, which refers to a SAP BW datasource for Inventory Management
- Valuation row
- shkum
- FUELLEN_ZUABGANG_MSEG
- BWART 122 and 123 (Movement types for return deliveries)
- Oli1BW
- T156-XSTBW
- XSTOR
- ROCANCEL
- MCEX_UPDATE_03 (an ABAP function module used for updating BW data)

The prerequisite for this issue to occur is that a standard price change is implemented followed by a return delivery or cancellation for the return (either BWART 122 or 123) into the previous month. The system erroneously posts the revaluation amounts with the same sign regardless of whether the standard price increased or decreased.

The solution provided in the note is to implement a program correction. This implies that some code adjustments are required to resolve this issue. However, specific details about the programming correction are not included in this summary and would typically be found in the attachment or detailed description that accompanies the SAP Note.