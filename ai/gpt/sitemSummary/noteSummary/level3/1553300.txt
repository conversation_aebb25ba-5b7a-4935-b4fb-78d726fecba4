SAP Note 1553300 introduces the 7.20 EXT kernel, an alternative to the standard 7.20 kernel for all SAP NetWeaver releases that are supported by the 7.20 kernel. The 7.20 EXT kernel is necessary because the standard 7.20 kernel maintenance ended in Q1/2015, after which it has been replaced by the 7.21 kernel as the standard SAP kernel release.

Reason for this new kernel:
The SAP kernels are tied to specific versions of Operating Systems (OS) and compilers. When these become outdated due to the end of vendor support, customers face support challenges. To ensure compatibility with supported versions of OS and compilers and to stay compliant with regulations like SOX or FDA which require the use of supported software environments, SAP provides updated kernels.

Supported NetWeaver releases:
The 7.20 EXT kernel is compatible with various SAP NetWeaver releases from 7.0 through 7.3 and their enhancement packages (EhPs). Specific NetWeaver versions that are compatible with the 7.20 EXT kernel are listed in the note.

How to use the 7.20 EXT kernel:
The operating system and/or database release may need to be upgraded to use the 7.20 EXT kernel. Compatibility can be confirmed through the Product Availability Matrix on the SAP Service Marketplace.

Limitations:
It's recommended to use either the standard 7.20 kernel or the 7.20 EXT kernel consistently across all parts of an SAP system (central instance and application servers). However, if you are running different releases of the same operating system platform which are not all supported by one kernel version, you may need to use both kernels simultaneously and deploy them manually.

Delivery:
The 7.20 EXT Kernel is available for download through the SAP Service Marketplace. It can be applied during system upgrades, system updates, and the deployment of Support Package stacks.

More information:
Details on the 7.20 downward compatible kernel can be found in SAP Notes 1629598 and 1636252. For information on the deployment, reference is made to SAP Note 1553301.