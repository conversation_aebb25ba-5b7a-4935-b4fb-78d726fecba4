SAP Note 547397 addresses an issue in the structure BPAR_ROLE1 which is used during the business partner conversion process in CFM (Corporate Financial Management) 2.0. The fields LOW and HIGH within this structure are incorrectly using the data element BP_ROLETYP when they should be using BP_ROLETYP_OLD. This discrepancy is causing problems with the conversion process.

To resolve this issue, SAP recommends either importing an R/3 Support Package which includes the fix or manually updating the data element for fields LOW and HIGH in the structure to BP_ROLETYP_OLD. The steps to manually correct this are as follows:

1. Use Transaction SE11 to access Data Dictionary (DDIC).
2. Select the 'Data type' option and enter 'BPAR_ROLE1'.
3. Choose 'Change' function key.
4. Update fields LOW and <PERSON>IGH to use the data element BP_ROLETYP_OLD.
5. Activate the structure after making these changes.

The note does not mention any prerequisites for applying the fix and does not list any further terms or reasons beyond what is necessary to understand the issue and solution.