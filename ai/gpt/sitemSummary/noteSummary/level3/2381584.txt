SAP Note 2381584 addresses the adaptation of ALE (Application Link Enabling) interface generation for BOR (Business Object Repository) objects to accommodate the extended material number field length in SAP S/4HANA, specifically from 18 to 40 characters.

Symptom:
Users need to ensure that when generating ALE services for BOR objects after the material number length extension, the extended keys are taken into account.

Reason:
In SAP S/4HANA, starting with the on-premise edition 1511, the material number field was extended, which requires regeneration of ALE interfaces for BOR objects.

Solution overview:
The note provides step-by-step guidance for adapting BAPI-ALE interfaces to handle the extended material number field length, including:

1. Adding new long parameters to your BOR methods (if needed).
2. Maintaining hierarchical dependencies between table parameters of a BAPI (if exists).
3. Repairing existing IDoc segments that were generated before the S/4HANA conversion, to align with the changed data element references from MATNR to MATNR18.
4. Regenerating the ALE interfaces via transaction BDBG after the S/4HANA transition.
5. Adjusting the existing coding that calls the generated ALE outbound function module to ensure that both the old and new material number fields are populated.
6. Implementing key mapping for custom BOR objects if their keys were extended.
7. Adjusting the creation of application links for instance-independent BOR methods with importing parameters that have names matching the components of the logical BOR object key.

Additionally, the note mentions compatibility with older versions of interfaces and describes the new structure of ALE interface generation with respect to BAPIs, IDoc, and outbound/inbound function modules.

The note ensures that with the update in S/4HANA, the transaction BDBG will consider the extended key field correctly and specifies that previous restrictions from note 2232497 no longer apply. It also references additional notes for background information on field length extension (2215424) and adapting BOR methods (2216654).

To conclude, SAP Note 2381584 provides instructions for dealing with changes in the ALE interface generation process due to the material number field length increase in SAP S/4HANA, ensuring data integrity and system compatibility post-extension.