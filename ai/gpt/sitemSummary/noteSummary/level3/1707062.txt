SAP Note 1707062 addresses an issue encountered when multiple processes within an SAP instance attempt to generate a GUI status. The symptom described here is that if several processes try to do this simultaneously, only the first one is able to update the database, while the others perform the generation in memory (inline) without updating the database. This in-memory data is then released on the next database commit.

There is a mention of a related issue where question marks could appear in the menu bar, which seems to be an associated symptom.

To prevent unnecessary locks on the tables D347T and EUDB, it is recommended to also implement the correction provided in another SAP Note, 1721936.

The solution given is to use a dispatcher and work process package (disp+work package) that matches the patch level specified under "SP Patch Level", although in the information provided, the specific patch level is not disclosed.

In summary, the note provides guidance on a GUI status generation issue involving multiple processes and advises using a specific patch level to resolve the problem while also suggesting implementing corrections from a related note to prevent database lock issues.