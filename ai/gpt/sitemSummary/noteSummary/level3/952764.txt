SAP Note 952764 addresses an issue with the incorrect buffering of authorization variables specifically of the type customer exits for values, intervals, selection options, and hierarchy nodes within SAP systems. The symptoms associated with this error can vary and are influenced by whether or not the user intends for the variables to be buffered.

Key points from this note include:

- The incorrect sequence of processing due to incomplete buffering.
- A warning not to confuse 'variables in authorizations' with 'variables filled from authorizations' as they are distinct and not related to the customer exits in queries.
- Note 942799 is mandatory before implementing the solution provided in this note, which is part of Support Package 7. Manual steps are required and can be found in the long text of Note 942799.
- The resolution involves importing Support Package 09 for SAP NetWeaver 2004s BI (BI Patch 09 or SAPKW70009). 
- Additional details about Support Package 09 can be found in SAP Note 914303 titled "SAPBINews BI 7.0 SP09". While this note may be available before the official release of the Support Package, its short text may still indicate "Preliminary version" until then.

In summary, SAP Note 952764 addresses the issue of incorrect variable buffering in authorizations and requires SAP Note 942799 to be addressed first. The solution involves importing a specific support package and possibly consulting related notes for further details.