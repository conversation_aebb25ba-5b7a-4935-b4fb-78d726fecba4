SAP Note 641748 is an instructional note on how to import APO Support Package 25 (SAPKY30A25) for APO Release 3.0A. The note emphasizes the importance of this support package for customers using the SNP heuristics and provides the necessary information on release restrictions, liveCache version, and prerequisites for implementation.

Key points from the note include:
- SAP offers a feedback survey regarding the quality of the APO Support Packages.
- SNP heuristic users should consider note 605624 for additional information.
- Note 427957 contains release restrictions as of APO Support Release 3.
- Recommended liveCache version 7.4.2.xx is now available for APO 3.0A customers.
- Prerequisites are detailed for various components, including SAP BASIS, ABA, BW, KERNEL, FRONTEND, APO liveCache, APO Optimizer, and the SAP R/3-Backend.
- Instructions for SPAM update are provided to ensure users operate with the current status before implementing the Support Package.
- Order of implementation should follow the sequence: SAP BASIS, ABA, BW, and APO. Import of BW Support Packages must not be queued.
- APO Support Package 25 requires a complete installation/upgrade from the delivery of May 15th,2000 and implementation of all APO Support Packages up to the current status or as per the initial support release.
- Supported languages for APO Release 3.0 are listed.
- The SAP Service Marketplace is identified as the central point for downloading SAP software components.
- Direct links are provided to the surveys, detailed information, prerequisites, and components related to APO Release 3.0A.

In summary, SAP Note 641748 guides customers through the process of updating their systems with APO Support Package 25, highlighting prerequisites, sequence of steps, and resources for successful implementation.