The SAP Note 2638840 addresses an issue that occurs during data migration to S/4HANA 1709 FPS01 using the Migration Cockpit. Specifically, the problem happens during the 'Simulate Import' step of the migration process, where data is unexpectedly transferred to the target system when it shouldn't be.

This issue is observed for migration objects set with the option 'Process One Instance at a Time,' such as the 'Bill of Material' migration object. The note establishes that the problem can arise when data is transferred through files or using staging tables in the LTMC (on-premise).

To resolve the issue, the note advises implementing the provided corrections in the affected S/4HANA system. Additionally, users are instructed to manually regenerate the runtime object using the transaction LTMOM. The SAP Note applies to SAP S/4HANA 1709 FPS01 and includes correction instructions for proper implementation.