SAP Note 741280 addresses a particular issue encountered in SAP releases from 4.0B up to and including 4.6C where creating a customer-specific parameter effectivity does not generate an entry in the SMODILOG table. This problem can lead to data loss in the AEEF, AEPF, and TECS tables during an upgrade to a release higher than 4.6C as these customer-specific parameter effectivities may get lost.

The note specifies that the issue arises when users utilize customer-specific change numbers with parameter effectivity by creating parameters via Transaction OS60 and assigning them to an effectivity type using Transaction OS61.

As a solution, the note recommends that users should manually check the SMODILOG table with the key OBJ_NAME = CCEFFE* to verify the absence of entries despite having customer-specific entries in the structure. If entries are missing, the user should manually create SMODILOG entries. The note suggests making a dummy change - adding and then immediately deleting a new structure field to the CCEFFE1 structure in Transaction SE11 - to force the creation of a SMODILOG entry.