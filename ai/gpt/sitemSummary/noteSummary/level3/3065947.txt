SAP Note 3065947 addresses an issue where, during data migration, multiple conditions with the same validity periods are being incorrectly overwritten in the "MM - Purchasing info record with conditions" object when using either the "Migrate Your Data – Migration Cockpit" app or transaction "LTMC". The issue occurs specifically when the "Condition Sequence Number" column in the XML template is filled with the same values for more than one condition.

The root cause of the problem is that the "Condition Sequence Number" should have distinct values for each condition, even if they share the same validity period. When different validity periods are involved, the "Condition Sequence Number" must match.

The resolution is to ensure that, for migrating multiple conditions with the same validity periods, different values for the "Condition Sequence Number" are maintained. Examples of correct and bad templates are provided in the note to illustrate the proper way to fill out the XML template.

Keywords associated with this SAP Note are "Condition Sequence Number," "Validity Period," and "SIF_PURCH_INF_V2."