The SAP Note 2590824 addresses an issue faced during the pre-check report when converting from EHP8 for SAP ERP 6.0 SP08 (or higher) to SAP S/4HANA where obsolete objects generated by the Purchase Order Generation Tool (POGT) cause errors. After conversion, POGT is not supported in SAP S/4HANA, and any related objects that were copied to the target system need to be removed.

Symptoms include errors in the consistency check (return code 7), activation errors related to DDIC objects generated by POGT, and inconsistencies reported in condition type Maintenance Groups.

To fix this issue, SAP Note 2591835 must be implemented as a prerequisite because it contains the necessary DDIC changes. After that, SAP Note 2590824 provides a cleanup program named RFM_CLEANUP_POGT_OBJECTS, which needs to be executed in all systems—development, test, and production—after conversion. This is done using the SA38 transaction. The implementation of the note and the cleanup program should also be included in the SPAU request to ensure that the cleanup is available and can be carried out in all systems following the conversion.

In summary, this note provides a solution to clean up obsolete POGT-generated objects after converting to SAP S/4HANA by providing a cleanup program that should be executed in all systems post-conversion.