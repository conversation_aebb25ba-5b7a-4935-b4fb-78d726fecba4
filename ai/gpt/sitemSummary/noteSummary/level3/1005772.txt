SAP Note 1005772 addresses an issue encountered with formula variables that are meant to be replaced from a characteristic key before aggregation in SAP BW systems. 

**Symptom**: Users attempt to replace a formula variable using a replacement path with the corresponding characteristic from the characteristic key before aggregation. However, due to Note 993286, the resulting values are erroneously displayed as 'X'.

**Other Terms**: The note references terms such as REPPATH, FLAGR, and 1ATTRKEY.

**Reason and Prerequisites**: In BW 3.x versions, formula variable replacement could occur before aggregation in calculated key figures but after aggregation in formulas. In BI 7.0, this replacement happens only after aggregation, which leads to correct values in both calculated key figures and formulas. However, this difference causes a problem after upgrading to BI 7.0 where calculated key figures may still only deliver a value 'X'.

**Solution**: 
- The note provides a correction where users can run program RSR_VAR_REPPATH_AGGREGATION to set the variable calculation time before aggregation.
- An alternative solution suggests using the Query Designer from Frontend Patch 15 to create the variable as a formula variable with replacement from the attribute "Characteristic key."
- The note states that Frontend Patch 15 will accompany details found in Note 991095 once it is released.
- It instructs to import Support Package 12 for SAP NetWeaver 2004s BI, detailed in Note 914306, to resolve the issue.
- In urgent scenarios, it is possible to implement advance correction instructions provided they are available.
- Key prerequisites include implementing other specific SAP Notes (932065, 935140, 948389, 964580, 969846, and 975510), which address transaction SNOTE to prevent deimplementation issues and syntax errors.

The SAP Note emphasises checking the preliminary status of the notes referenced and recommends looking at Note 875986 for guidance on using the SAP Note Assistant to avoid implementation problems.