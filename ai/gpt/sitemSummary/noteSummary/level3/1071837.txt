SAP Note 1071837 addresses a specific issue where saving a new clinical order (CORD) in SAP systems under certain conditions results in the creation of duplicate entries in the N1COMPA database table. The affected application components are Radiology and Transport Order.

The cause of the problem is identified as a program error related to the case assignment class (CL_ISH_CASE_CHANGE) and the clinical order process both performing a save operation. As a result, this leads to the system writing two identical entries (except for the key fields).

The solution provided involves running a correction report named RN1_CORRECT_CORDER_N1COMPA to delete the duplicate entries. The note gives instructions for different system versions:

For i.s.h.med/IS-H Version 4.72, users should unpack the file HW1071837_472.zip.
For i.s.h.med/IS-H Version 6.00, users should unpack the file HW1071837_600.zip.
After unpacking the relevant file, users are instructed to import the orders into their system and implement the provided source code correction.

It is important to note that the attachment containing the correction can only be downloaded from the SAP Service Marketplace, not via OSS (Online Service System). References to other SAP Notes 480180 and 13719 provide additional information on importing attachments.

In summary, this note gives instructions to address and rectify the issue of duplicate database entries being created when saving clinical orders, by providing a correction report and source code for users to implement.