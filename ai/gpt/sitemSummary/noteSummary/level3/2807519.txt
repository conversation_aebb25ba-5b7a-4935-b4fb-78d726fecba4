SAP Note 2807519 addresses issues related to the on-the-fly target cost calculation for manufacturing orders within SAP S/4HANA 1909. Specifically, this note provides solutions for analyzing target costs at goods receipt and through Fiori applications F1780 (Production Cost Analysis) and F3331 (Analyze Costs by Work Center/Operation).

Symptoms include difficulties in analyzing target costs for manufacturing orders, errors in order standard costs calculation, and errors in target costs calculation for manufacturing orders. It also addresses missing target cost information in the Fiori applications.

To resolve these issues, SAP enhanced its applications to enable the analysis of target costs on the fly, starting with SAP S/4HANA OP1809. These enhancements allow users to analyze production costs and target costs for collective orders in Fiori Apps F1780 and F3331, with considerations for different currencies and valuation strategies.

Key enhancements include:
1. Target costs calculation for collective orders based on Order Plan Costs (PLANORD01) and Order Standard Costs (PLANORD02) in ACDOCP.
2. Support for viewing production costs in various currencies in Fiori App F3331.
3. Posting date logic in ACDOCP for plan and standard costs with specifics on scheduling and valuation dates, and overhead posting.

There are several remarks about:
- The handling of posting dates for plan and standard costs and their impact on the order schedule.
- On-the-fly target cost calculation supports specific scenarios and cost estimates.
- Origin indicators are necessary for valuation quantity updates in ACDOCP.
- Targets costs calculated on the fly will not be overwritten by period-end variance calculations.

The note also mentions restrictions, including:
- On-the-fly target cost calculations do not include stock revaluation.
- Incompatibility with joint production collective order scenarios and mixed costing products.
- Plan costs update in ACDOCP is only supported for legal valuations based on the leading ledger.

For detailed innovations, the note refers to SAP Note 2661581. Users affected by the mentioned issues should review this note to understand the implications for their specific system and to apply solutions accordingly.