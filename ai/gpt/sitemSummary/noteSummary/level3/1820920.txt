SAP Note 1820920 addresses an issue where an error occurs when running a query in SAP NetWeaver Business Warehouse (BW). The error involves system program SAPLR<PERSON><PERSON> and form RI8_RTIME_REDUCE-02.

Symptom: 
The error arises specifically when the query has compounding characteristics and occurs in the following context:
- Compounding characteristic 0FISCVARNT is used as a fixed filter
- Compounding characteristic 0FISCYEAR serves as a dynamic filter.
- An additional compounding characteristic, 0FISCPER (related to 0FISCVARNT) is in the drill down and has unposted values enabled.
- The dynamic filter includes an unassigned # filter value along with other specific filter values.

Solution:
The solution to resolve this error is to import the relevant Support Package for your BW system version as described in the note. The corrected versions for various SAP NetWeaver BW are as follows:
- BW 7.00: Import Support Package 31 (refer to SAP Note 1782745)
- BW 7.01 (EHP 1): Import Support Package 14 (refer to SAP Note 1794836)
- BW 7.02 (EHP 2): Import Support Package 14 (refer to SAP Note 1800952)
- BW 7.30: Import Support Package 10 (refer to SAP Note 1810084)
- BW 7.31 (EHP 3): Import Support Package 8 (refer to SAP Note 1813987)
- BW 7.40: Import Support Package 3 (refer to SAP Note 1818593)

The note advises that if the error needs urgent correction before the Support Package is released, users can refer to the correction instructions provided in the note. Prior to applying the correction instructions, users are encouraged to check SAP Note 875986 regarding the use of transaction SNOTE.

Additionally, the Note mentions that these corrections might be available before the release of the Support Package, but they may be referred to as a "preliminary version" at that stage.