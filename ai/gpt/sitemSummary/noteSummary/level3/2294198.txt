SAP Note 2294198 addresses the configuration and usage of custom form templates with the new output control in SAP S/4HANA. Here's a summary of the note:

**Symptom:**
SAP S/4HANA uses a new output management system. Detailed documentation is available on help.sap.com for versions 1709 and higher, while this note provides information for versions 1511 and 1610 about creating custom form templates.

**Other Terms:**
Output management, output control, custom form template.

**Reason and Prerequisites:**
SAP provides default templates for every business application, but these templates may need to be customized. Non-standard form technologies are supported, including SAPscript, Smart Forms, PDF-based print forms without fragments, and the new technology of PDF-based print forms with fragments which permits reusing common components.

**Solution:**
Custom form templates can be created for use with the new output control, but they and their corresponding print programs must be compatible with the new system.

When utilizing existing form technologies, backend access is required for:
- SAPscript (transaction SE71)
- Smart Forms (transaction SMARTFORMS)
- PDF-based print forms without fragments (transaction SFP)

Register these forms in the customization activity "Assign Form Templates" (see SAP Notes 2292539 for details) to use with new output control.

Print programs must only send rendered documents to the spool and must follow several specific restrictions outlined in the note. For example, they must not raise certain types of messages, must handle output parameters correctly, and must not refresh the transactional buffer. Any existing print programs must be reviewed and potentially updated to meet these new requirements.

Additionally, the new output control provides a default preview functionality that might not be guaranteed for form templates used in previous output control frameworks – refreshing the transactional buffer could lead to exceptions and system dumps.

**Examples:**
The note provides examples of how to set up a custom Smart Form and a custom PDF-based print form with fragments for billing documents, including registering the form templates and defining business rules for output determination. It advises using the Fiori app "Maintain Form Templates" for the new form technology and referencing related SAP Notes 2292646 and 2292681 for additional details.

Overall, this SAP Note provides guidance on creating and implementing custom form templates within the output control framework of SAP S/4HANA, emphasizing the need for compatibility and adherence to specific guidelines for existing and new form technologies.