SAP Note 1013015 - Validity check of KST

Symptom:
The note addresses an issue where the system performs a validity check for a cost center using the current date every time the cost center is entered.

Other Terms:
References to a function module (REMD_EMPGE_TO_OBJNR) and a method (EMPGE_TO_OBJNR) within class CL_REDB_GENERIC are mentioned.

Reason and Prerequisites:
The system was not designed to perform a time-dependent check of the cost center consistently across all areas.

Solution:
The solution provided by this note involves different treatments for the validity check of the cost center in contract processing. For the distribution of conditions, the system will only verify the cost center's existence. However, when checking against posting parameters, the system will also consider the cost center's validity period and display a warning message if there is an issue. The contract can be saved but will not be activated if there's a problem with the validity period.

The note provides advance correction instructions including three manual changes:

1. The 'FINAL' indicator should be removed from the method IS_VALID_IN_PERIOD in the attributes of the class CL_RECA_BUS_OBJECT.
   
2. Two messages must be created in transaction SE91 with the 'self-explanatory' indicator. These messages belong to message class RECAAP and are numbered 079 and 089, providing text for cost center validity-related warnings.

3. The note instructs to change the data type 'REEX_COSTCENTER' using transaction SE11, including an update to the 'Group' column for the INCLUDE 'BAPI0012_CCOUTPUTLIST' by entering the value 'DBTAB'.

The note specifies that these changes can only be implemented if the SAPKGPAD04 support package is installed or for the ERP2005 system where the advance correction applies.