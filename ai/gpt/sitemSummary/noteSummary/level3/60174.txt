SAP Note 60174 addresses an issue with the factory calendar AM that contains incorrect special rules. The problem is that the calendar AM's special rules incorrectly mark the entire month as a working day rather than just the last day of the month as intended.

The cause of this issue is identified as the XPRA program RSSCALX2, which is responsible for creating the factory calendars AJ and AM if they don't already exist. There is a flaw in the program that led to the erroneous creation of special rules for calendar AM.

The solution provided in this note is differentiated based on whether customers have already installed Release 3.0C.

For customers who have already installed Release 3.0C:

1. They are instructed to start the transaction SCAL to change the factory calendar.
2. Select the factory calendar AJ or AM.
3. Delete the selected calendar.
4. Then exit factory calendar maintenance and start transaction SE38.
5. Enter "RSSCALX2" as the program in SE38.
6. Execute the program.
7. A report log should confirm that the calendars were recreated.
8. Then exit the report log.

After following these steps, factory calendars AJ and AM should be correctly created.

For customers who have not yet installed Release 3.0C:

1. They are instructed to apply a correction to the program RSSCALX2.
2. After making the correction, they should follow the same steps 1 to 8 as provided for those with Release 3.0C.

Following these steps will resolve the problem with the special rules in the factory calendar AM, ensuring the last day of the month is correctly marked as a working day.