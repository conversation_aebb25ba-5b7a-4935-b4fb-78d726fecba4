SAP Note 2806070 provides information on using the embedded Extended Warehouse Management (EWM) in the SAP S/4HANA 1909 on-premise edition. It outlines the available versions (Basic Warehouse Management and Extended Warehouse Management), setup instructions, and major differences between EWM in SAP S/4HANA 1909 and decentralized EWM 9.5. The note includes the simplified integration of embedded EWM into other SAP S/4HANA applications and additional functionality within EWM in S/4HANA. 

However, there are specific restrictions when comparing embedded EWM to other functions generally available in SAP S/4HANA, such as batch derivation, batch-specific UoM, special stock indicators, and valuation of stock in transit processes with EWM-managed locations. The note provides guidance on existing workarounds and related notes such as SAP Note 2432414 for batch-specific UoM and active ingredients, and SAP Note 1822497 for valuated stock in transit processes.

Additionally, there are restrictions related to the integration with Quality Management (QM), covering various aspects like inspection lot management, first article inspection, and handling of quality certificates. Users need to refer to other specified SAP Notes for detailed information on UI restrictions and decentralized EWM usage, BI data sources applicable to EWM, and features in EWM that do not use single-system integration qualities. 

The note includes attachments such as the "Basic Settings for EWM in SAP S/4HANA, On-Premise Edition" starting guide and the document "EWM Deployment Differences" which lists exceptions between different EWM versions. To adapt materials and batches created in earlier releases for use in EWM, SAP S/4HANA provides reports like PRD_SCM_GUID_CONVERSION and RVB_BATCH_GUID_GENERATOR. Additionally, integration between S/4HANA EWM 1909 and S/4HANA TM 1909 is handled through A2A services, and further details can be referenced in SAP Note 1984252.