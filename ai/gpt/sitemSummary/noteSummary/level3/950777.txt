SAP Note 950777 addresses an issue where a PartProvider is not being read by a query sent to a MultiProvider. The cause of the issue is described as a characteristic in the MultiProvider that is not assigned to any MultiProvider characteristic, indicated as IDENT. This characteristic is set as a constant in the PartProvider, referred to as CHACONST.

To resolve this, the following solution is proposed:
- For SAP NetWeaver 2004s BI systems, the recommendation is to import Support Package 09 (BI Patch 09 or SAPKW70009). Details about the Support Package can be found in the related SAP Note 0914303 with the title "SAPBINews BI 7.0 Support Package 09".

The note mentions that in urgent cases where immediate resolution is necessary, customers can refer to the correction instructions provided. These instructions may be available in advance of the official Support Package release. In such cases, the short text will indicate that it is a "preliminary version".

Keywords associated with this issue are Query, MultiProvider, CHACONST, and IDENT. Users affected by this issue should look for the release of the referenced Support Package and the related informational Note 0914303 for a detailed description and any updates concerning the availability and implementation of the fix.