SAP Note 1742572 addresses an issue where custom implementations of the Business Add-In (BAdI) BADI_DS_DSAL_FUNC do not execute as expected. This BAdI is used for implementing customer functions that modify the log display in transaction DSLOG, which is the signature tool log display.

Symptoms described in the note include:
- Users have created and activated a custom implementation for the BAdI BADI_DS_DSAL_FUNC.
- Upon accessing transaction DSLOG to view the signature logs, the custom functions are not available or executed in the log display.

Other terms relevant to this issue are:
- Signature tool
- Log display
- Digital signature
- DS
- DSLOG
- BADI_DS_DSAL_FUNC
- ES_DS_DSAL_DISPLAY_LOGS
- IF_EX_DS_DSAL_FUNC

The note specifies that the cause of the issue is a program error.

The solution provided is:
- To implement the correction instructions that are relevant for the specific SAP release.
- Users should then save and activate the changes to resolve the issue.

The note implies that after following the solution instructions, the custom BAdI implementations should operate correctly within the signature tool log display.