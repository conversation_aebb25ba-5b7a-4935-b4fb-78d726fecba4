SAP Note 877345 addresses a specific issue encountered during the business partner conversion of phase II where the program RFTBUH05 requires an existing transport order number to be entered on the selection screen. This requirement also applies to related programs RFTBUH03, RFTBUH04, and RFTBUH06.

**Symptom:**
When running the business partner conversion in production mode using the aforementioned programs, the user must enter a valid transport order in the order/task field, which is mandatory.

**Other Terms:**
These are some keywords and program names related to the issue: <PERSON><PERSON>, UPART02, business partner, BPUM_CTL, TRKORR, RFTBUH02_1.

**Reason:**
The necessity for the order number arises because the program RFTBUH02_1 mistakenly treats customer-specific data elements ending with "_OLD" as relevant for conversion. This incorrect interpretation leads to an ID being recorded in the table BPUM_CTL, which in turn affects subsequent programs (RFTBUH03 to RFTBUH06) by making the order/task field mandatorily ready for input. If no conversion-relevant data is found, no entry in the order/task field can be made, and no ID is noted in the BPUM_CTL table.

**Solution:**
To resolve this issue, the note provides a correction that can be applied using the Note Assistant or by importing a specified Support Package. The solution involves several subsequent tasks:

1. Reset the status of previously executed programs to "space" using Transaction "UCU4" (project: 0003).
2. Delete the contents of the TRKORR field in the BPUM_CTL table using Transaction "SE16".
3. Remove every customer-specific data element using Transaction "UCBPUM_ROL" (Project: 0003).
4. Restart the business partner conversion for phase II.

These steps are intended to remove incorrect data and statuses that are causing the conversion process to require an unnecessary transport order number, allowing for the restart of phase II conversion without this issue.