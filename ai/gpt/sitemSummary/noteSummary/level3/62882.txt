SAP Note 62882 addresses an issue with Logistics Information System (LIS) regarding the subsequent settlement in the field catalog for the key 'EKOK'. The symptom described is an error message M2609, which occurs when creating a custom information structure in the purchasing application (application '02'). This error states that the table field is missing from the Dictionary when key figures from the 'Subsequent settlement' field catalog are included in the new information structure. The fields mentioned include KMEIN, PERIV, and VWDAT in table MCKONA, which although the table exists, these fields do not.

Other terms related to this issue include volume-based rebates, subsequent settlement, creating information structures, and transactions MC21 and MC22.

The reason for this problem is cited as incorrect entries in Customizing.

The proposed solution is to copy and execute the attached report ZDELEKOK. This report aims to delete the incorrect, unnecessary entries. It also mentions that this issue is corrected in SAP Release 3.1G.