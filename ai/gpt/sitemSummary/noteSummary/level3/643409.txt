SAP Note 643409 addresses the use of the EXT kernel during system upgrades and is specifically relevant to customers who are already using the EXT kernel on their source release.

The note outlines the steps and considerations required when upgrading systems operating with an EXT kernel. It also specifies details for upgrades to SAP R/3 4.6C Support Release 2 or SAP APO 3.1 and highlights the need for using a special EXT kernel available on EXT kernel CDs. Instructions are provided for replacing the upgrade kernel with the EXT kernel. 

Key points from the note include:

1. **Replacing the Kernel CDs**:
   - Instructions for upgrading to SAP R/3 4.6C SR2 or SAP APO 3.1 using the EXT kernel.
   - Steps to retrieve the necessary SAPEXE.SAR and SAPEXEDB.SAR archives from the EXT kernel CDs.
   - A process to replace the upgrade kernel with the EXT kernel on different operating systems.
   - For DEC OSF True 64, an additional step is required to replace the R3up with a new version.
   - A cautionary note regarding the requirement for certain minimum versions of the upgrade tools for systems running on the SAP DB database.

2. **Environment Variables**:
   - Specific instructions for ORACLE databases and upgrades to a product with Basis 6.20.
   - Setting necessary environment variables to ensure successful connections to Oracle 9.2 databases.
   - A reference to SAP Note 540021 for more information on the environment variables that need to be set.

3. **Further Adjustments**:
   - Procedures for upgrading with HP-UX 32-bit and Oracle 9.2, emphasizing the need to adjust startup files and eventually replace them with the correct paths for the 8.1.* clientlib after the upgrade.

The note emphasizes that it requires the attention of customers who are already using the EXT kernel and includes references to additional SAP Notes for detailed information regarding specific procedures and environment settings.