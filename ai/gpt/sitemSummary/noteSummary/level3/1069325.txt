SAP Note 1069325 addresses issues encountered when creating a constant rule in SAP NetWeaver 7.0 BI. The problems described include the following symptoms:

1. The system fails to change the rule type to "constant."
2. The creation of a transformation from an update rule or a transfer rule results in a short dump (a type of system failure in SAP).

The cause of the issues is identified as a program error. 

The solution provided is to import Support Package 15 for SAP NetWeaver 7.0 BI, identified by the code SAPKW70015. This support package is detailed further in Note 991095, titled "SAPBINews BI7.0 Support Package 15," once it is released to customers.

In cases where immediate resolution is required, the note advises implementing the correction instructions as an advance correction. Before doing so, users are instructed to first read Note 875986, which offers information about using transaction SNOTE (an SAP tool to implement corrections).

Additionally, the note indicates that even before the Support Package is formally released, preliminary information may be available to users. If this is the case, the short text of the note will include the phrase "Preliminary version."