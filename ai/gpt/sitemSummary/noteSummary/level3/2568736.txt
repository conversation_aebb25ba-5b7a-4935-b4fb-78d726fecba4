SAP Note 2568736 addresses a scenario where an organization is preparing to convert their SAP ERP system to SAP S/4HANA or upgrade their existing SAP S/4HANA system. To facilitate this preparation, it is important to analyze the impact on the system based on simplification items. Part of this impact analysis involves understanding the usage data, or how frequently certain ABAP reports, transactions, remote function calls, or URLs have been used over the past months—data that is available in ST03N.

The note identifies a challenge: when using a copy of the production system to perform the readiness check, ST03N data cannot be directly copied over. To solve this problem, SAP provides a simple report within this note that allows the collection of ST03N data from the production system to be used in the target system where the SAP Readiness Check will be executed.

The prerequisites for using the solution include implementing a minimum version 77 of SAP Note 2399707 in the target system.

The provided report, named TMW_RC_MANAGE_ST03N_DATA, needs to be executed in both the production system to download the ST03N data and in the target system to upload the same data. Afterwards, a new SAP Readiness Check should be performed following SAP Note 2913617.

There's a limitation due to memory constraints when downloading ST03N data: it cannot exceed 1.5 million records and 3 months of data. However, a workaround is provided for those who wish to collect the full period of the setting result by making an entry in the BCOS_CUST table using transaction SM30.

Tips included in the note explain that the uploaded ST03N data is stored in a temporary table and is solely for SAP Readiness Check runtime collection, meaning it will not be visible in the standard ST03N function. Additionally, for those who choose not to implement the note, SAP provides a sample local report in the note's attachment that can perform the same download and upload tasks for ST03N data.