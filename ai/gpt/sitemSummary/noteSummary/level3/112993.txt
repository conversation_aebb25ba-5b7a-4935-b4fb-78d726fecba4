SAP Note 112993 addresses an issue specific to SAP Release 3.1I related to tax code handling for debit side settlement in purchasing. It builds on the changes suggested in a prior SAP Note, 97157, by implementing additional program modifications that allow the use of the functions provided in Note 97157, also in Release 3.1I.

**Symptom:**
The note highlights the need not to change the status from 'Released internally' and references potential risks as detailed in Note 97157. This note is strictly applicable only to Release 3.1I.

**Other Terms:**
The issue relates to subsequent settlement in purchasing, which can impact reports and transactions such as RWMBON01, MEB4, MEB2, MEU2.

**Reason and Prerequisites:**
For understanding the underlying problem and the approach for implementation, users are directed to reference Note 97157.

**Solution:**
1. Users are instructed to ensure that the tax code field (VBRP-MWSKZ) is preserved during the upgrade and to create a backup if necessary.
2. The source code corrections specified in part 1 of Note 97157 should be implemented. While part 2's implementation is also a possibility, it has not been tested by SAP.
3. In addition to the steps above, users should implement the corrections provided in SAP Note 112993.
4. SAP emphasizes the importance of conducting additional tests after making these changes.

Users should follow the steps carefully, account for necessary backups, and engage in comprehensive testing to ensure stability after the implementation. For complete details regarding the issue addressed by this note and the solution implementation, users are advised to consult SAP Note 97157.