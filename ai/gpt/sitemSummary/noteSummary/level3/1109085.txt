SAP Note 1109085 provides instructions for pilot customers on how to implement enhanced functionality for "DELT@" tailored to 'Accidentes sin baja' (RATSB), excluding the generation of MultiRATSB. It details prerequisites, which include installing SAP Note 1039411 before the new functionality, and provides guidance on installing two provided CAR files: one with workbench objects (SYST-file) and the other with customizing objects (CUST-file), which need to be installed in sequence due to dependencies.

The note also instructs customers to perform further customizing activities in the Spanish language (ES) by using the transaction SM30 to maintain table views. Specifically, it describes how to adjust entries in views V_T591A for Infotype '0968' and V_T588M for modulpool 'MP096800'. The note states that only the German text is included in the advanced delivery file, and thus Spanish texts for the infotype need to be entered. Finally, it references two additional SAP Notes, 480180 and 13719, for further information on how to install advanced delivery files and preliminary transports, respectively.