The SAP Note 1990560 addresses an issue where the initial delivery of multicurrency accounting included the ledgers 1L, 1D, 0D, and 0M, which could cause problems in various scenarios. The solution is to delete these ledgers and related customizing.

Key points from the note:
- Reason for the note: Incorrect delivery of certain ledgers in the multicurrency accounting setup.
- Other Terms: References to ledger groups and multicurrency accounting such as 0MCA, 1MCA, MCA1, MCA2, MCA3, MCA4.
- Solution: Users are instructed to either implement correction instructions or import the relevant support package that makes the program GLE_MCA_DELETE_LEDGERS available for use.
- For S/4 HANA (S4CORE), sFIN 2.0 (SAP_FIN 720), and ERP 6.0 EHP 8 (SAP_APPL 618), the program GLE_MCA_DELETE_LEDGERS is already included in the delivery.
- Users of Release sFIN 2.0 (SAP_FIN 720) must also implement SAP Note 2159137 for additional corrections.
- The report GLE_MCA_DELETE_LEDGERS is provided to help in deleting the unwanted ledgers and related customizing.
- Correction to textual information (Text 001) is mentioned, where the incorrect ledger group is referenced, and users should implement SAP Note 2625280 to adjust this.

In summary, this SAP Note provides a solution for removing incorrectly delivered ledgers that came with the initial set-up of multicurrency accounting, by using a provided report or applying the relevant support packages and follow-on notes.