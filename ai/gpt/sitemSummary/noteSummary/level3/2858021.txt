SAP Note 2858021 is an FAQ document that provides guidance on how to handle the fiscal year change in Asset Accounting within SAP S/4HANA Cloud, as well applicable to SAP Fiori Launchpad in SAP S/4HANA on-premise. It answers specific questions that users may have about preparing for and executing the year-end closing and beginning of a new fiscal year in Asset Accounting.

Summary of key points from the SAP Note:

1. **Fiscal Year Preparation**: To post in the new fiscal year (YYYY+1), users must carry forward balances using the "Carry Forward Balances" app (App ID: F1596). This application should be successfully run for all ledgers, and it's necessary to ensure the previous fiscal year (YYYY-1) is closed.

2. **Posting Periods**: Postings in the new fiscal year (YYYY+1) require that posting periods are maintained using the "Manage Posting Periods" app (App ID: F2293), typically managed by the General Ledger Accountant.

3. **Number of Open Fiscal Years**: In Asset Accounting, two fiscal years can be open at once, before opening a new fiscal year, confirm that the previous fiscal year has been closed.

4. **Identifying Closed Fiscal Years**: To find the last closed fiscal year in Asset Accounting, check the "Make Company Code Settings – Asset Accounting-Specific" app or the "Execute/Undo Year-End Closing" app (App ID: FAA_CMP) for the relevant company code and ledger.

5. **Current and Last Closed Fiscal Years**: For a newly defined company code, the current and last closed fiscal years are based on the legacy data transfer date in the first legacy data transfer segment, accessible via the aforementioned company code setting apps.

6. **Balance Carryforward in New Company Codes**: A balance carryforward is essential even if no business transaction was posted in previous years to establish baseline figures for the current fiscal year and ensure proper postings.

7. **Creating Asset Master Records in New Company Codes**: Error messages such as AY159 about incomplete fiscal year information can be addressed by defining the closed and current fiscal years or by creating a legacy data transfer segment.

8. **Re-Running Balance Carryforward**: An initial balance carryforward for the current fiscal year is usually sufficient, as follow-on postings will be carried forward automatically.

9. **Closing Fiscal Years**: Closing a fiscal year, like YYYY-1, involves completing the calculation and posting of depreciation for all assets, finalizing legacy data transfers, and reconciling data. It also may require a recalculation of depreciation if legacy data transfers occur during the fiscal year.

The note also provides further instructions, listing necessary apps and procedures, and emphasizes issues such as the importance of finalizing legacy data transfer and meeting prerequisites for closing fiscal years within the Asset Accounting module.