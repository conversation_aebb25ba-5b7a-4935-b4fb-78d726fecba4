SAP Note 959890 addresses an issue where an UNCAUGHT_EXCEPTION type short dump with the exception CX_RSR_X_MESSAGE occurs within transaction RSBBS when maintaining Goto targets for an incorrect query. The error is due to a program error.

The solution to this problem is to import the relevant Support Package(s) for your specific SAP BW (Business Warehouse) version. Each SAP BW version has its own specific Support Package that needs to be imported:

1. For BW 3.0B, import Support Package 32 (BW 3.0B Patch 32 or SAPKW30B32).
2. For BW 3.10 Content, import Support Package 26 (BW 3.10 Patch 26 or SAPKW31026).
3. For BW 3.50, import Support Package 18 (BW 3.50 Patch 18 or SAPKW35018).
4. For BW 7.0, import Support Package 09 (BW 7.0 Patch 09 or SAPKW70009).

Each of these Support Packages will become available after the release of specific SAP Notes that describe them in more detail. These are Note 0914949 for BW 3.0B, Note 0935962 for BW 3.1 Content, Note 0928661 for BW 3.5, and Note 0914303 for BW 7.0.

Additionally, if the correction is urgent, there is an option to implement correction instructions in advance. This early information may be available before the Support Packages are officially released, at which time the short text of the notes might still contain the phrase "preliminary version".