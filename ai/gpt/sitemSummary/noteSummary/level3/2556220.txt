SAP Note 2556220 addresses the simplification of DCS-Based (Derivative Contract Specification) Market Data Management for financial transactions in the context of a system conversion to SAP S/4HANA, on-premise edition 1709.

- **Issue Summarized**: For organizations undertaking a system conversion to SAP S/4HANA, there is a need to adjust how market data for financial transactions are managed, specifically concerning Commodity Risk Management. The previous system might have used various types of market data (quotation source, quotation type, quotation name), which now need to be converted to a DCS-based format in S/4HANA.

- **Key Concepts**: It mentions key terms such as Commodity Risk Management, Derivative Contract Specification (DCS), and Market Identifier Code (MIC). Commodity-related transactions are the focus here.

- **Reason**: The conversion is necessary because, starting from SAP S/4HANA 1709, commodity market data is managed solely based on DCS for derivative categories like Commodity Futures, Listed Options, and Commodity Forward Index.

- **Solution Overview**:
  - DCS is now the only basis for financial transaction pricing data.
  - Modifications have been made in the Financial Transaction Management and related commodity processes to facilitate the use of DCS-based market data, which includes supporting DCS-based commodity curves, price adjustments for unpriced commodity derivatives, etc.
  - Businesses that used non-DCS-based market data in their SAP ERP must convert their market data to align with DCS standards.
  
- **Actions Required**:
  - Organizations should review their current SAP ERP implementations to identify usage of non-DCS-based market data.
  - Such data must be converted to be DCS-compliant.
  - A detailed process for converting financial transactions pricing data from the old market data concept to the new DCS-based concept is outlined in an additional SAP note (2553281 - Cookbook Deprecation of Commodity ID in Commodity Risk Management).

This note is critical for any business in the financial sector that manages commodity market data and is transitioning to SAP S/4HANA, as it outlines changes in managing derivative contract specifications and the necessary steps to convert existing data to the new system.