SAP Note 105333 addresses an issue encountered when posting a goods receipt for a purchase order that contains a return item and has negative delivery costs attributed to it. The symptom described in this note is that the SAP system throws an error message M7050 stating "Balance not zero ...," which prevents the transaction from being successfully posted.

The note provides the context that this problem occurs specifically when using transaction MB01, which is the transaction code for posting a goods receipt for purchase orders. 

The reason for the error is that the system attempts to post the entire stock along with the goods movement during the returns process, which is likely causing an imbalance in the financial accounting entries, resulting in the error message.

The note concludes by offering a solution, which involves making certain corrections as detailed within the attached documents provided with the note. These corrections are necessary to successfully post such transactions without encountering the balance error. Users would need to implement the changes or corrections provided in order to resolve the issue.