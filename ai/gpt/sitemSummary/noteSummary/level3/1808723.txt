SAP Note 1808723 addresses several issues with the Bank Reconciliation Statement functionality, specifically for a pilot customer in China.

Symptom:
The symptoms outlined in the note include an inability to print the bank reconciliation statement, a lack of functionality for automatically ticking and clearing open items, and difficulties in displaying cleared open items in the statement. Users are required to manually tick and clear open items, as the system does not propose matched items automatically.

Other Terms:
The note references terms including PRL item, payment release list item, payment block, instruction key, SAPFPAYM_MERGE, and the electronic bank statement, which relate to the payment processing and bank reconciliation process in SAP.

Reason and Prerequisites:
The note is applicable only to pilot customers in China. Customers should be on a minimum of Support Package (SP) 05, and all object changes mentioned are covered by Enhancement Package 6 (EHP6) SP06.

Solution:
The solution proposed in the note requires the customer's system to be updated to EHP6 SP05. It includes enhancements to the Electronic Payment Integration for China (EPIC) with new features that are provided only via the Support Packages. These enhancements include the ability to print and save the bank reconciliation statement as a PDF, automatic clearing of ticked open items through program F-03, a new checkbox to display cleared items, and a BADI to define custom matching logic for clearing open items.

Risk and Restrictions:
The note advises caution when using Transport (SAR) files instead of installing the correct Support Package, highlighting the risks and limitations. There might be no updates to Transport files when objects are modified, and the files may become obsolete without warning. The note emphasizes that the content of Transport files is only valid until that content is made available through Support Packages or CLC Packages. It also points out that translations and certain system text updates are only provided through these packages.

The note also directs readers to check SAP Notes 1318389, 13719, 212876, and 480180 for further guidance on handling Transport files. Additionally, it stresses the importance of following the specified sequence when implementing more than one Transport file to avoid installation failures and warns that future installations of Support Packages must include packages that delivered the changes in previous Transport files to ensure object versions are not reverted to older ones.