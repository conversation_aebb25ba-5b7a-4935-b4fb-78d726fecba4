SAP Note 690900 discusses how users of SAP Real Estate Management (RE-FX) can define, display, and change custom user-defined fields in master data tables using the Business Data Toolset (BDT) as of release 470x200.

Symptom:
Customers want to add custom fields to RE-FX master data tables such as real estate contracts or other related tables, and have these fields available in the corresponding maintenance dialogs.

Other Terms:
The note references several technical terms related to RE-FX like RECN, REBDxx, RESCxx, and REAJCG which represent different RE-FX objects such as contracts, business entities, settlement units, etc.

Reason and Prerequisites:
The note indicates that customers should have Support Package 04 or higher which contains an example function group REGC_EXT_EXAMPLE. Users are cautioned to use the appropriate enhancement method for real estate master data instead of the general BDT events to ensure consistency across different data processing scenarios (interface, legacy data transfer, BAPI calls).

Solution:
The note provides a detailed step-by-step example of how to add user-defined fields to the real estate contract master data table. The steps are as follows:

1. Add fields to master data tables by editing the .INCLUDE structure in the ABAP Dictionary using Transaction SE11.
2. Copy the provided example function group in the BDT and adapt the copied group for your custom application.
3. Adjust the copied subscreen for your custom fields and if needed, add other screens or function modules.
4. Make various configurations in the BDT to ensure that the custom fields and functionalities are integrated into the existing maintenance dialogs. This includes:
   a) Publishing your application for each relevant object.
   b) Publishing the defined screens to the BDT.
   c) Creating field groups and views for the new fields.
   d) Defining sections and assigning views to display the fields correctly.
   e) Adjusting screen sequences if you have introduced new screens.
5. Finally, testing the changes in Transaction RECN or the relevant transaction for the object type you've enhanced.

Tables are provided in the note displaying the technical names and transaction codes relevant to various RE-FX objects for Release 470x200 and higher releases. The solution ensures the fields are added correctly and are usable in the application's user interface. The SAP standard naming conventions must be followed, with custom objects and enhancements typically starting with 'Y', 'Z', or 'X' (for partner enhancements).