SAP Note 1080518 addresses an issue regarding excessively large space occupied by expired server side cookies in the SSCOOKIE table, which is utilized by BSP (Business Server Pages) Applications in SAP environments.

**Symptom**: The primary concern indicated in the note is that the SSCOOKIE table may become too large due to the accumulation of expired server side cookies, leading to a potential need for maintenance.

**Other Terms**: Terms mentioned include SSCOOKIE, which is the table in question, and two reports, BSP_CLEAN_UP_SERVER_COOKIES and BSP_SHOW_SERVER_COOKIES, which are used for managing cookies.

**Reason and Prerequisites**: This note is relevant for systems where there's a significant number of expired server side cookies causing the SSCOOKIE table to consume an unacceptable amount of database space.

**Solution**: The proposed solution consists of the following steps:

1. Verify if the SSCOOKIE table is indeed occupying excessive space in the database through Transaction DB02.
2. Identify expired records in the table by using the BSP_SHOW_SERVER_COOKIES report or by using Transaction SE16 and filtering records with an EXPIRYD (Expiry Date) that is past the current date.
3. Execute the report BSP_CLEAN_UP_SERVER_COOKIES in order to delete expired cookies from the SSCOOKIE table.
4. For regular maintenance and to avoid future space issues, it is recommended to schedule the BSP_CLEAN_UP_SERVER_COOKIES report to run periodically in each productive client.
5. The note also references SAP Note 1011158 regarding the buffering of the SSCOOKIE table, indicating that as of Basis release 640, buffering is disabled by default but can be enabled for single record (full) buffering if the table does not undergo frequent changes.

The note underlines the importance of database management and regular cleanup of expired data to maintain optimal system performance and avoid unnecessary consumption of database resources.