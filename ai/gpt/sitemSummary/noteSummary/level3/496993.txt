SAP Note 496993 deals with the authorization setup necessary for shift planning transactions in SAP, specifically emphasizing the importance of combining base authorizations from the Personnel Planning authorization object (PLOG) with structural authorizations managed via Transaction OOSP.

Summary:

Shift planning in SAP requires both display and maintenance authorizations for master and time data, as well as additional authorizations related to the Organizational Management environment, such as temporary assignments and requirement maintenance.

The note lists the object types relevant for shift planning:
- C (Job)
- O (Organizational unit)
- P (Person)
- Q (Qualification)
- OK (Qualifications catalog)
- S (Position)
- SR (Requirements)

For the shift planning transaction (Transaction PP61), base authorizations (PLOG) are required for:
- Infotypes 1002 (Description), 1027 (Site-Dependent Info), 1039 (Shift Group) for object type O; infotype 1001 (Relationships) for object types C, O, P, Q, S, SR, with function code and subtype both set to * (wildcard for all). You can also specify particular subtypes that are used in shift planning.

Structural authorizations (Transaction OOSP) are also necessary for various object types, detailing whether there's a maintenance or display authorization, object IDs, and evaluation paths.

For the requirements maintenance transaction (Transaction PP67), base authorizations are needed for:
- Infotypes 1000 (Object), 1001 (Relationships) for object types C, Q, S, SR, and infotype 1049 (Requirements Attributes) for object type SR. It specifies the function code and subtype as * (all).

For the report RHPSOLL_DEL (Transaction PP6C), a base authorization is required for:
- Infotype 1000 (Object) for object type O, function code, and subtype * (all).

Additional hints in the note suggest updating structural authorization changes immediately in the search dialog and reviewing Note 375216 for settings related to temporary assignments' tolerance time in the shift planner.

In essence, the note elucidates what authorizations are essential for personnel involved in shift planning to correctly access and modify data within their authorization scope. It mentions different types of organizational objects and the corresponding infotypes and function codes that users would need permission for, in order to perform their tasks in the shift planning transactions PP61, PP67, and PP6C. It also gives practical advice for ensuring immediate visibility of authorization changes and configuring access to data post temporary assignments.