SAP Note 1824130 addresses an issue where, during the execution of a Data Transfer Process (DTP) within a process chain (PC), if a dump occurs, the red (unsuccessful) branch of the process chain does not execute as expected even with CCMS/RZ20 monitoring in place. Normally, a callback to the DTP is executed only during the second check, which then sets the DTP to red and notifies the chain to execute the red branch. After implementing the corrections provided in this note, the first check of the chain or DTP will trigger the red branch execution upon a dump occurrence.

The problem is identified as a program error, and the solution is to import the relevant Support Package for the respective SAP NetWeaver BW version. The note provides specific details for each version:
- For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031).
- For SAP NetWeaver BW 7.01, import Support Package 14 (SAPKW70114).
- For SAP NetWeaver BW 7.02, import Support Package 14 (SAPKW70214).
- For SAP NetWeaver BW 7.11, import Support Package 12 (SAPKW71112).
- For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010).
- For SAP NetWeaver BW 7.31, import Support Package 8 (SAPKW73108).
- For SAP NetWeaver BW 7.40, import Support Package 3 (SAPKW74003).

Each of these Support Packages can be considered available when separate SAP Notes describing them in more detail are released to customers (SAP Note references are given for each Support Package).

For urgent cases where immediate resolution is required, it is suggested to implement the correction instructions as an advance correction. However, the note advises to first read SAP Note 875986, which includes information on how to use transaction SNOTE.

Finally, the note mentions that preliminary versions of the SAP Notes for the Support Packages may be available before the official release. If this is the case, they will have the words "Preliminary version" in the short text.