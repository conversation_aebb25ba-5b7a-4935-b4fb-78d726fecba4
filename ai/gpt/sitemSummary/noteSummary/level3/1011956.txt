SAP Note 1011956 addresses an issue specific to the Swiss version of SAP for the Healthcare industry (IS-H CH). It involves the RNWCHSKH1 program, which did not fully populate the AGR segment of the statistics for inpatient locations in healthcare institutions. This issue affects the data transfer to an external program (SES).

Key points from the note:

- **Relevance**: The note is relevant only to customers using the Swiss country version of SAP.
- **Symptom**: The RNWCHSKH1 report did not fully fill the AGR segment, which is part of the basic data required for hospital statistics.
- **Other Terms**: Keywords related to this issue include various record types like AGR, AL1, AL2, AH1, AH2, AH3, AH4, AFI, ATA, and ABE.
- **Reason**: The full provision of record types was not originally planned in the software.
- **Solution**: The system has been updated to automatically fill in additional record types. There's also a BAdI (Business Add-In) available for users to supply any record types not covered by the standard IS-H functionalities with data sourced from other SAP components.
- **Implementation**: Detailed steps are given for implementing the solution, including downloading and unpacking the relevant correction instruction files (specific to SAP IS-H versions 4.63B, 4.72, and 6.00) from the SAP Service Marketplace, and instructions on importing these updates into the SAP system.
- **Customization**: Instructions are provided for those who want to use the BAdI to modify or extend the data records for hospital statistics, including how to create an implementation in transaction SE18 and how to enhance methods with customer-specific requirements.

The note also references other notes (480180 and 13719) for additional information on importing attachments. Users are instructed to manually implement the transport requests contained in the attachment before applying the attached correction instructions.