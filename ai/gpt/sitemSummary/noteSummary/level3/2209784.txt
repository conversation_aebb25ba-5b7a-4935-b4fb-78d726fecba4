SAP Note 2209784 addresses the need for pre-transition checks of the EC-CS (SAP Consolidation) application before upgrading to SAP S/4HANA. It specifically mentions that if a material number (MATNR) field is used as a customer-defined subassignment in the EC-CS data model, this must be adjusted prior to the upgrade.

The reason for the requirement is that in SAP S/4HANA, the material number's length has been extended to a maximum of 40 characters, which is longer than what the EC-CS data model's characteristics can support.

The solution provided by this note includes correction instructions that implement a consistency check for the EC-CS data model to see if a MATNR field is used. In case the check reveals that a MATNR field is indeed being used as a customer-defined subassignment, customers are advised to contact SAP Support by creating an incident in the EC-CS application component via SAP Service Marketplace.

The background provided clarifies that adjusting the EC-CS data model will require the conversion of existing EC-CS transaction data, which might result in the loss of information because MATNR fields need to be removed from the model.