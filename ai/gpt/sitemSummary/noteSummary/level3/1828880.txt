SAP Note 1828880 addresses an issue where queries that utilize hierarchy node selections on a virtual characteristic in SAP NetWeaver Business Warehouse (BW) return incorrect data or fail to display any data.

Symptom:
- The problem manifests when executing a query with hierarchy node selections specifically on a virtual characteristic.

Other Terms:
- The note mentions several technical terms and abbreviations, such as SFC, CHARET, SIDRETFL, SIDRET_0, FEMSn, CHECK_FEMS_CHANMID, XFL-KEYFL, which are related to the internal workings of the BW system.

Reason:
- The cause of the issue is identified as a program error within the BW system.

Solution:
The note suggests the following solutions for different versions of SAP NetWeaver BW:
- For BW 7.30: Import Support Package 10 (SAPKW73010), detailed in SAP Note 1810084 "SAPBWNews NW 7.30 BW ABAP SP10."
- For BW 7.31: Import Support Package 08 (SAPKW73108), detailed in SAP Note 1813987 "SAPBWNews NW BW 7.31/7.03 ABAP SP8."
- For BW 7.40: Import Support Package 03 (SAPKW74003), detailed in SAP Note 1818593 "SAPBWNews NW BW 7.4 ABAP SP03."

Additionally, for urgent situations, it is possible to implement the correction instructions as an advance correction. However, before doing so, one must read SAP Note 875986, which provides guidelines on how to use transaction SNOTE.

Furthermore, the SAP Notes mentioned for the detailed descriptions of the support packages may already be available before the actual release of the support package. If they are available, their titles may still include the phrase "Preliminary version."