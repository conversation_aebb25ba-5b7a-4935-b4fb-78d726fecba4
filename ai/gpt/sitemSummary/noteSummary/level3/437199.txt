SAP Note 437199 addresses an issue with the settlement per customer billing document without an accounting document. This problem arises during the settlement of a rebate arrangement when the system incorrectly generates error messages indicating that the billing document has not yet been forwarded to Financial Accounting or the condition record has not been settled and no settlement documents can be created. Additional error messages may also appear when trying to release the customer billing document to accounting.

The root of the problem is that no account determination procedure was assigned to the customer billing document type, which results in the document being marked with status D (accounting document not required), rather than the expected status C (accounting document created). Without the proper status, the system does not process the document correctly for the subsequent settlement, and incomes are not recorded in the proper info structures, preventing a final settlement of the rebate arrangement.

The solution provided in this note requires the implementation of an attached program correction. However, a correction for Release 4.5 is mentioned as pending and could take time to be available. Once the program correction is implemented, affected rebate arrangements must be recompiled to update the incomes. The note instructs running reports RWMBON07 (for vendor rebate arrangements) or RWMBON37 (for customer arrangements) without the 'Check run' indicator and with '000' as the version to recompile incomes properly. The online documentation should be consulted for more details on how to use these reports. Finally, for systems earlier than Release 4.0B, SAP Note 114111 should also be considered.