SAP Note 1399658 addresses an issue in transaction SWIA (Process Work Item As Administrator), where the system fails to perform an authorization check when a user attempts to logically delete a work item. The authorization object in question is S_WF_WI with activity 15, which corresponds to the administration task of setting the work item status to "Logically Deleted". Without this authorization check, users may delete work items without proper permissions.

The solution provided in this note ensures that after implementing the necessary corrections, the system will check the appropriate authorization for each work item that is to be logically deleted using transaction SWIA. Essentially, the note resolves a security issue by guaranteeing that only authorized users can logically delete work items.