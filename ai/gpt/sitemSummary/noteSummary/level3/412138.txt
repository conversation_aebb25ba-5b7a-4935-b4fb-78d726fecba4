SAP Note 412138 addresses an issue with incorrect results in non-cumulative queries within partitioned InfoCubes in SAP Business Information Warehouse (BW) when using an Oracle database. Specifically, the error occurs when the partition characteristic (like 0CALMONTH or 0FISCPER) differs from the non-cumulative cube's most detailed time-reference characteristic, and the data has been aggregated.

To resolve this issue, the following steps are recommended:

1. Import the respective Support Package into the BW system:
   - For BW2.0B, use Support Package 16 (SAPKW20B16). More information is provided in SAP Note 0374845, titled "SAPBWNews BW 2.0B Support Package 16."
   - For BW2.1C, use Support Package 08 (SAPKW21C08). SAP Note 0384997, titled "SAPBWNews BW 2.1C Support Package 08," contains further details.

2. After importing the Support Package, rebuild the affected cubes (BasicCubes or AggregateCubes) or execute the repair report SAP_REPAIR_NCUM_PART contained in the Support Package.

3. Be aware that the repair report also has an error which is rectified with additional source code corrections provided in the note. This particular error in the report is corrected with Patch 17 for BW2.0B or Patch 9 for BW2.1C. It is important to note that the attached source code correction only fixes an error in the correction program and not the original issue.

It's important to note that SAP Note 110934 provides general information on BW Support Packages, and the availability of the Support Packages is contingent upon the release of the aforementioned SAP Notes 0374845 for BW2.0B and 0384997 for BW2.1C.

Before the release, the short text of SAP Note 0374845 will state "preliminary version," indicating that the final details are still pending. Users experiencing this problem should follow the outlined solution and reference the related SAP Notes for more detailed information on the Support Packages and correction instruction.