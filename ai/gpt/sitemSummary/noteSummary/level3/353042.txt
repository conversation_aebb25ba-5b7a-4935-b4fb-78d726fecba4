SAP Note 353042 addresses a specific issue where certain fields (such as BWGEO, BWGEOO, BWGVP, BWGVO, BWNETWR, BWMNG, etc.) in DataSources (like 2LIS_02_SCL, 2LIS_02_ITM, 2LIS_03_BF, 2LIS_03_UM, 2LIS_40_REVAL, and others) are not being updated. This results in no data being written to various InfoCubes (e.g., 0RT_C*, 0PUR_C01, 0CP_PURC1, etc.), even though data is arriving in BW (Business Warehouse).

The root cause of this problem is the process key fields (0PROCESSKEY and 0BWAPPLNM) of the InfoSources not being populated, causing update routines of the InfoCube to skip data insertion due to a conditional check on the PROCESSKEY field. 

The solution provided in this note is to activate the determination of the process key using Transaction MCB_ within the OLTP IMG for BW, available using Transaction SBIW in the R/3 source system. The user must select the appropriate industry sector; for example, 'Standard' and 'Consumer products' for R/3 standard customers, and 'Retail' for R/3 Retail customers.

If historical data already exists in the setup tables (e.g., through Statistical Setup Programs), it must be deleted with Tx LBWG before performing the setup to ensure the system generates a valid transaction key for each data record. This corrected data should then be loaded into the connected BW system using a 'Full update' or initialization of the delta process.

If these steps are not successful, Note 315880 should be consulted, and the application indicator 'BW' should be set to active using Transaction 'BF11'.

The note also references related SAP Notes 157317, which should be reviewed to ensure relevance, and 352344 which addresses the process key and reversals in Inventory Management.