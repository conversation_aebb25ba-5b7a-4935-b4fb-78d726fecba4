SAP Note 890821 addresses two issues with the CRM BSP Framework and provides a correction for Service Pack 06 (04).

Symptom 1 describes an issue with inconsistent behavior when filtering in the MEDL-list. Using the filter-popup-trigger button works correctly, but initiating filtering with the RETURN key or the filter icon does not.

Symptom 2 indicates a problem with navigation failing sometimes because URL parameters are read case-sensitively.

There is no specific "Other Terms" section provided.

The note does not explicitly state the reason for these issues but indicates that they are addressed in Correction CRM BSP Framework, SP06 (04).

The solution to these problems includes an update to the State Manager, which now returns URL parameters in a case-insensitive way for better error tolerance.

The note also instructs users to apply certain changes manually:
1. Execute transaction "sotr_edit."
2. Click on "Create."
3. Fill out the create screen in English with the following details:
   - Package = CRM_BSP_FRAME_GENERIC
   - Alias = CRM_BSP_FRAME_GENERIC/VALUE_HELP_TITLE
   - Object Type = WAPP
   - Text = Value Help Selection
4. Save

It is also noted that the alias CRM_BSP_FRAME_GENERIC/VALUE_HELP_TITLE should be maintained in all languages necessary for the user's requirements.

This summary summarizes the key points and instructions laid out in the SAP Note. Users who encounter these symptoms should follow the described manual steps to correct the issues.