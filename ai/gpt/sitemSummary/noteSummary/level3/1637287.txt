SAP Note 1637287 addresses the implementation of a new interface design for the SAP GUI for HTML (WebGUI), which is available with SAP Kernel 7.21/7.22. This new rendering technology is relevant to systems running SAP_BASIS versions 700, 701, 710, or 711.

Key information from the note includes:

- By default, these SAP_BASIS versions will still use the conventional "Trade Show Design" with the downward-compatible kernel (DCK) concept, which means there is no change for users.

- Users can opt to switch to the new design by setting the parameter `~webgui_new_design=1` in the ICF service definition. This change can be done per service.

- The new design offers several enhancements:
  - Standards mode support.
  - Support for custom themes using URL parameters `sap-cssurl` and `sap-cssversion`.
  - Standard themes like SAP Blue Crystal, SAP Corbu, SAP Tradeshow Plus, SAP Tradeshow, and SAP High Contrast Black are supported (from SAP Kernel 721 Patch Level 138).
  - Unified rendering technology, used across Web Dynpro ABAP/Java, BSP, etc., to prevent visible transitions between applications using different SAP technologies.
  - Ability to import data from Excel into TableControl and ALVGrid using the clipboard.
  - Compatibility with Apple Safari 5 on Mac OS 10.5/6 or higher, with the caveat being exclusive to the SAP GUI for HTML.

To implement this solution, it is necessary to have SAP Kernel 7.21 Patch Level 215 or higher installed in the system. Configuration adjustments for WebGUI can be made by calling transaction SICF, navigating to the webgui service, and adding the mentioned parameter in "GUI Configuration".

Furthermore, the note references another SAP KBA (Knowledge Base Article) 2108843 for a step-by-step guide on how to use the ~webgui_new_design parameter.