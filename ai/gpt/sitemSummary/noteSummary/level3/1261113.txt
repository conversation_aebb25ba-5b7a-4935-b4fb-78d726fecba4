The SAP Note 1261113 addresses an enhancement of the BAdI (Business Add-In) FVD_LOAN_POST to enable the possibility of changing the field AUFNR (order number) for customer-specific use. This functionality was not available before implementing this note. The enhancement was delivered in response to previous notes, notably SAP Note 899513, which introduced the BAdI FVD_LOAN_POST originally designed to allow custom field modifications.

The note gives clear instructions for implementing the enhancement, which involves making changes to the interface of the BAdI FVD_LOAN_POST. The user must perform the following steps:

1. In the interface of BAdI FVD_LOAN_POST, there's a need to copy the method CHANGE_PRCTR_IN_BSEG to a new method called CHANGE_AUFNR_IN_BSEG, which will be responsible for filling the order number. This is done via transaction SE18.

2. In the new method CHANGE_AUFNR_IN_BSEG, the user should rename the parameter C_PRCTR to C_AUFNR, update the associated type to match BSSBSEG-AUFNR, and revise the description to "New Order Number".

3. Once the modifications are made, the changes should be activated.

If a user desires to create a separate implementation, the note explains that this is possible through transaction SE19, where the user can define their own values according to the new business requirement. 

To resolve the issue fully, users are advised to implement the advance corrections provided by the note or import the relevant Support Package that includes these corrections. 

The note mentions other key terms related to the enhancement, such as LOAN_POST, SE18, SE19, BSSBSEG, and ACCIT. These terms are likely references to the functions and transaction codes within SAP that are related to the BAdI FVD_LOAN_POST and the processes for managing accounting documents and loans.