SAP Note 1364916 pertains to an issue where migrating a transformation that includes a formula can lead to errors. Specifically, users may encounter an ABAP runtime error 'ASSERTION_FAILED' or an error message R7 105 during the 'After_Import' processing phase for the object type TRFN (activation mode). This issue occurs due to a program error, typically after a transformation is generated through migration from transfer or update rules.

The resolution for this problem is addressed in Note 1369395, which provides a repair tool to automatically correct the metadata inconsistency that causes the issue. Moreover, the note advises importing specific Support Packages into your SAP NetWeaver BI system to address the error, depending on the version you are using:

- SAP NetWeaver BI 7.00: Import Support Package 22 (SAPKW70022), and refer to Note 1325072 for more details.
- SAP NetWeaver BI 7.01: Import Support Package 05 (SAPKW70105), and refer to Note 1324445 for more details.
- SAP NetWeaver BI 7.02: Import Support Package 01 (SAPKW70201), and refer to Note 1332017 for more details.
- SAP NetWeaver BI 7.10: Import Support Package 08 (SAPKW71008), and refer to Note 1260071 for more details.
- SAP NetWeaver BI 7.11: Import Support Package 03 (SAPKW71103), and refer to Note 1263691 for more details.

If the issue is urgent, users can implement the correction instructions provided in the note as an advance correction. However, before making these changes, users must familiarize themselves with Note 875986, which offers information about using transaction SNOTE (a transaction used for managing SAP Notes within the SAP system).

Additionally, even if the Support Package has not officially been released, the note provides that the referenced SAP Notes might already be available in a preliminary version, which will be indicated by the words "Preliminary version" in the short text of the note.