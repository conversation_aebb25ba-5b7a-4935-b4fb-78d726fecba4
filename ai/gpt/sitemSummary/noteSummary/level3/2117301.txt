The provided SAP Note 2117301 addresses the issue of the table _SYS_XS.JOB_LOG continuously growing in size on systems running SAP Lumira Server. SAP Lumira and associated features on SAP HANA are no longer being actively developed, as highlighted in the SAP Knowledge Base Article 2195103.

The environment affected by this issue includes systems with SAP Lumira Server on SAP HANA.

The issue can be reproduced by having the SAP Lumira Server installed and running for some time, after which it can be observed that the table _SYS_XS.JOB_LOG grows over time. This growth is due to the jobs related to the Discover feature of SAP Lumira Server, specifically the jobs named sap.bi.discover.logic.vizd.backgroundjobs::VDSchedulerJob_1, sap.bi.discover.logic.vizd.backgroundjobs::VDSchedulerJob_2, and sap.bi.discover.logic.vizd.backgroundjobs::VDSchedulerJob_3 writing information to this table. There are no automated housekeeping jobs built into SAP HANA for cleaning up this table.

The resolution provided in the note involves running an SQL command to manually delete entries in the _SYS_XS.JOB_LOG table that are older than a specified number of days. However, the note includes a strong warning that modifying SAP HANA System Tables can cause serious issues that might necessitate reinstalling the entire system. SAP is not responsible for any issues that arise from table modifications and advises backing up the SAP HANA system before making any changes.

The provided SQL command for cleanup is:
DELETE FROM "_SYS_XS"."JOB_LOG" WHERE "FINISHED_AT" < add_days(now(), -5);

The keywords associated with this SAP Note are Table Full, Large, Logging, Filling up, Space, and Lumira.