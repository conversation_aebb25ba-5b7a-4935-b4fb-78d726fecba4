SAP Note 947483 addresses an issue where a query on a MultiProvider does not display any data when no key figures are included in the query and the MultiProvider does not contain a BasicCube.

Summary of the SAP Note 947483:
- **Symptom**: Users may encounter a situation where their queries return no data when executed.
- **Other Terms**: This issue is relevant when dealing with queries built on MultiProviders, specifically without key figures.
- **Reason**: The root cause of the problem is identified as the MultiProvider lacking a BasicCube combined with the absence of key figures in the query.
- **Solution**: The solution provided is to import Support Package 09 for SAP NetWeaver 2004s BI (with the identifier BI Patch 09 or SAPKW70009) into the BI System. This Support Package should resolve the issue as detailed in SAP Note 0914303 titled "SAPBINews BI 7.0 Support Package 09". The note contains additional details regarding the Support Package.
- If there's an urgent necessity to fix the issue, correction instructions are available for immediate use.
- It's also noted that the mentioned additional information might be accessible before the official release of the Support Package, which would still be marked as a "preliminary version" at that time.

Action required for SAP users facing this issue is to apply the mentioned Support Package following the release and instructions of Note 0914303 or to follow the provided correction instructions if immediate resolution is needed.