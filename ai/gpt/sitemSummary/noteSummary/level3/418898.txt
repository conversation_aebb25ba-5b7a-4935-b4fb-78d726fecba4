SAP Note 418898 addresses enhancements to the Finnish LUM2 format for making foreign currency payments. It introduces functionality for payment orders and handling exchange rates provided in electronic bank statements.

The new solution requires adjustments in the Payment Medium Workbench (PMW) and the DME Engine, replacing program RFFOFI_U. Changes to file layouts can now be managed through Customizing, removing the need for code changes. 

The note outlines several Customizing actions that users must perform:

1. Adjust the Note to Payee for payment orders, allowing up to four fields of information for vendors based on company requirements.
2. Assign the PMW format and Note to Payee to the payment method for foreign payments within the country specifics.
3. Create or assign selection variants for the payment program to generate payment media for foreign payments.

Additionally, the note instructs users to use program RFEBFILUM00 to convert LUM2 files from the bank into MultiCash format and includes a reference to Note 418855 for a function module to post foreign currency payments in the Electronic Bank Statement program.

Transport requests provided on the SAP server should be imported in a specified order. However, issues may arise due to new fields in table FEBPDO that are not included in the transports—I.e., domains for the fields PROCESS_CSB and X_HISTORY. Users are advised to either assign these fields to an existing 1-character domain or delete them from the structure.

For further information on SAP server transports, Note 13719 is referenced.