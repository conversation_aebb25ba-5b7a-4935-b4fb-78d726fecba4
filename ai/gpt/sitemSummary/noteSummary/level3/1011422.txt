The SAP Note 1011422 addresses an issue where selections for fields that are marked as 'Cannot Be Selected' in the metadata of a DataSource can be incorrectly transferred to the DataSource when a VirtualProvider is based on a direct access data transfer process.

Key points from the note are as follows:

Symptom:
- Selections for non-selectable fields in the DataSource metadata are being transferred to the DataSource when using a VirtualProvider based on direct access.

Reason:
- The cause of this issue is identified as a program error.

Solution:
- Update to SAP NetWeaver 2004s BI by importing Support Package 09 (BI Patch 09 or SAPKW70009) into the BI system. This package will be detailed in SAP Note 914303 titled "SAPBINews BI 7.0 Support Package 09," which should be released to customers.

Additional Information:
- In urgent situations, there's a possibility to apply correction instructions as an advance correction.
- Before implementing these corrections, it's necessary to first implement other specific SAP Notes (932065, 935140, 948389, 964580, 969846, 975510, 983212, and 1000448) as they contain information about transaction SNOTE, which is crucial for preventing issues and syntax errors when implementing or deimplementing certain notes.
- The aforementioned corrective SAP Notes may be available even before the Support Package release, labeled with "Preliminary version" in the short text.
- SAP Note 875986 should be referred to before implementing an advance correction, which provides important information about the SAP Note Assistant to avoid problems during implementation.
- Additionally, SAP Notes 970793 and 988509 should also be considered in relation to this issue.

In summary, this SAP Note provides a solution to a specific problem involving incorrect data selection for VirtualProviders and offers a recommended Support Package update along with a sequence of other Notes to be implemented to ensure smooth correction application.