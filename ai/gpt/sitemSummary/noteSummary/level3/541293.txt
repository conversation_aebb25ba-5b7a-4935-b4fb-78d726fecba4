SAP Note 541293 addresses an issue encountered after executing phase II of a business partner (BP) conversion, where certain partner numbers are missing from the table VDGPO. This table is created during the conversion to replace Transportation (TR) BPs with SAP BPs.

The issue arises because the conversion does not account for entries in table BP000 that do not have an associated SAP BP number due to a deletion flag. As a result, there are data inconsistencies where these partners' numbers are not entered into the VDGPO table, and it becomes impossible to exit a contract during maintenance.

The solution provided is to ensure all partners are converted during phase I of the business partner conversion, including those marked for deletion. This is achieved by modifying the report RFTBUP01 according to the program corrections attached to the note. SAP Notes 518307 and 445507 are also mentioned as being revised and partially revoked.

There is no alternative provided, as revising the logic of conversion phase II would be too extensive and potentially disruptive to clients already using the phase. Instead, the note recommends the less extensive changes offered therein.

To avoid converting unwanted TR BPs, the note suggests archiving those TR BPs not to be used in contracts before starting conversion. This will remove them from table BP000 and prevent their conversion to SAP BPs.

In summary, to correct data inconsistencies post BP conversion, one should convert all partners during phase I, despite deletion flags. The required program adjustments are in the note, and a process for excluding TR BPs from conversion is given.