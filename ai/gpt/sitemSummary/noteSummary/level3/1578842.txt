SAP Note 1578842 addresses the topic of FI (Financial Accounting) inconsistencies that occur in a test system but are not present in the production system. 

Key points from the note include:

- The note is relevant when FI inconsistencies are found in the test environment that are not replicated from or seen in the production environment. It also applies to cases where inconsistencies are corrected in the test system before applying the fixes to the production system.

- The reasons and prerequisites section reiterates the note's applicability to situations where FI inconsistencies are unique to the test system and are not observed in the production system.

- The solution provided by the note clarifies when SAP support can be contacted. If there is an FI inconsistency in the production system or an issue in the production system that is also present in the test system, users can log a support message under the component FI-GL-GL-X.

- However, SAP will determine whether corrections should be tested in the test system before applying them to the production system or if they should be directly implemented in the production system. This decision is based on the complexity of the issue.

- In cases where an FI inconsistency occurs only in the test system (and not in the production system), SAP support will not assist in resolving such issues.

- The recommended action in such scenarios is to create a fresh copy of the production system into the test system.

- If users still require assistance with inconsistencies in the test system, they must understand that it will be regarded as a remote consulting (RC) issue, which is a chargeable service from SAP.

- For more information on remote consulting services, users are advised to refer to OSS Note 83020.

In summary, SAP Note 1578842 explains the policy about support for FI inconsistencies, indicating when SAP will provide assistance for free and when the support becomes a chargeable service, particularly emphasizing that issues unique to test systems without affecting the production environment fall into the latter category.