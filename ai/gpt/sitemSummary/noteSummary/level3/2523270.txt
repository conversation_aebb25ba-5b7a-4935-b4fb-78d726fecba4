SAP Note 2523270 addresses an issue faced when updating a modified migration object with custom fields and/or structures in SAP S/4HANA using transaction code LTMOM. Users may encounter a popup message preventing them from switching to change mode.

The popup message is a result of receiving new migration content as part of a system upgrade. LTMC (Legacy Transfer Migration Cockpit) enforces consistency with the content delivered by SAP, and therefore prompts for an update to avoid any loss of data due to custom modifications to migration objects.

Resolution options provided include:

1. Keeping the modified migration object: 
   - Users can update their modified object in LTMOM using the 4th button in the toolbar, or they can copy the migration object to create a new one that will not receive updates, as it won't reference the standard SAP content.

2. Maintaining the reference to SAP content:
   - Users must perform an update of the migration object as indicated in an attachment (not included in the summary) and then re-add their custom fields afterward.

The note also includes a link with instructions on using LTMC for migration in S/4HANA On-Premise (OP) and lists relevant keywords like LTMOM, LTMC, migration object, custom modifications, generate, migration object modeler. The image/data in the note is from SAP's internal systems and is not representative of real data.