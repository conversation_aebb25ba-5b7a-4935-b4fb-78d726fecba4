SAP Note 2443857 provides release information specific to the Fiori application "View Deferment Events" as relevant to the Oil & Gas Upstream Operations Management (UOM) sector. This note outlines the following key points:

- The application "View Deferment Events" is introduced for Deferment Analysts to view and manage existing deferment events within the oil and gas network objects. It includes capabilities for maintaining these events and handling event attributes like period, code, and tags.
- It lists the key features of the application, such as the ability to view and maintain deferment events.
- The note specifies that it is subject to regular updates, advising users to check it frequently.
- A significant enhancement for the application "View Deferment Events" is delivered with Stack FPS2 of the product version S/4HANA OP 1610.
- The Fiori application requires the OData service UPS_DEF_EVENT to be registered, and the gateway role SAP_BR_DEFERMENT_ANALYST_IOG to be added to the user's profile.
- Links to additional information about the Fiori application can be found in the SAP Fiori apps reference library.
- Other terms associated with this note are Fiori, UI, UPS_DEF_EVTS1, and UOM.
- As prerequisites, it refers to other notes—2346431 for general installation and infrastructure topics for the product version 'S/4HANA OP 1610' and 2356208 for 'Fiori for S/4HANA OP 1610'.
- The solution provided is to apply Stack FPS2 of product version S/4HANA OP 1610 on both the frontend and backend server systems to ensure that the Fiori application performs as intended.

For comprehensive installation and infrastructure guidance, users are directed to check the release information from related notes, namely SAP Notes 2444372, 2356208, and 2346431. This additional information will give users a broader context on general release information for UOM applications and Fiori for SAP S/4HANA 1610, as well as detailed prerequisites and solution steps for the application of Stack FPS2.