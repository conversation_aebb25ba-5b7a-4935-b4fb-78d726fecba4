SAP Note 2125387 deals with the issue of activating a Core Data Services (CDS) view named V_RRP_STOCK_SUM on Microsoft SQL Server (MSS). When attempting to activate the CDS view, users face an error because the sql_variant data types are incompatible in the addition operator. This error also affects the import process of ERP 6.0 EHP7 SP7 when using SPAM, SAINT, or Software Update Manager (SUM); with the import process aborting during the IMPORT_PROPER phase and logging errors such as 'DDLS DDL_CDS_STOCK_SUM could not be activated' in the LONGPOST.LOG file.

The note specifies that the problem arises when support package SAPKH61707 is a part of the import.

As a solution, the note advises:

1. Before importing ERP 6.0 EHP7 SP7 stack using SPAM or SAINT, users must import this SAP note. If the import has already been started and an error is encountered, it says to contact SAP Support on component BC-UPG-OCS to reset the import queue and then apply the note. Afterwards, the report RUTDDLSACT should be run with 'DDL_CDS_STOCK_SUM' as the DDLSource, and then the import process should be restarted.

2. When the import is done using SUM, it might finish with the CDS view DDL_CDS_STOCK_SUM left inactive. After <PERSON><PERSON> completes, the note should be imported, and then users should reactivate the CDS view using the report RUTDDLSACT, entering 'DDL_CDS_STOCK_SUM' in the DDLSource field and executing it.

The note also mentions that the described error is resolved in SAP Basis version 740, support package 11.

There are no references provided in this SAP Note.