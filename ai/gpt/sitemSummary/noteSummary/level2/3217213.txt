SAP Note 3217213 addresses an issue where users are unable to deactivate a reversible Business Function after it has been transported to another system in the 'turned on' state. Users attempting to turn off the business function encounter the error message SFW474, which indicates the function has already been transported in the 'on' state.

The note explains that the inability to switch off the business function is by design, to avoid unpredictable impacts on the business if it were turned off after being transported to a production system.

The solution provided in the note offers a way to bypass this restriction and deactivate the reversible Business Function. The steps include:

1. Implementing the correction suggested in the SAP Note using transaction SNOTE or by installing the corresponding support package.
2. Running the report `SFW_OP_MODE_REVERSIBLE_BF`, which shows the current operation mode for reversible Business Functions. This report also allows users with the necessary permissions to change the setting to permit the deactivation of the reversible Business Function.

The note strongly recommends that after the reversible Business Function has been successfully turned off, the setting should be reverted back to its initial state, which is to not allow reversible Business Functions to be switched off in the system.

This note does not refer to any other notes or references.