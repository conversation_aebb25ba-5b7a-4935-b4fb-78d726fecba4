SAP Note 2781450 addresses an issue encountered when migrating equipment or functional location data into SAP S/4HANA on-premise using the SAP S/4HANA Migration Cockpit. Specifically, users face an error message stating "Unexpected company code in the translation rule MAP_BUKRS" when trying to load data for migration objects such as "Equipment," "PM -Equipment," "Functional location," or "PM - Functional location."

The cause of this error is that the cost center is specified in the source XML file, but the corresponding company code is missing, leading to a non-user-friendly and confusing error message. 

The solution provided in this note is twofold:
1. Users should ensure that they input the correct company code in the source XML file.
2. Implementing the Transport-based Correction Instructions (TCI) attached to this note will help users receive an improved error message that more accurately describes the issue.

Additionally, this particular note references SAP Note 2793418, which serves as a central correction note for various data migration content issues related to SAP S/4HANA 1809. Note 2793418 compiles issues and provides related notes for detailed explanations and solutions, including this note (2781450) for the issue with the unexpected company code in equipment migration.

To address these content problems, the TCI provided in the note will only update the SAP delivered migration objects and won't affect any existing modified or copied migration objects. Users with customized migration objects will not benefit from the corrections in this TCI and should refer to SAP Note 2543372 for guidance on implementing TCIs.