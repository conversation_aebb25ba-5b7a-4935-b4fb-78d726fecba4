SAP Note 3085487 pertains to the transition of Australia-specific HCM (Human Capital Management) applications from Web Dynpro for Java to Web Dynpro for ABAP (WD4A, WDA), which is SAP’s standard UI technology for web applications in the ABAP environment. 

This note highlights the need for the shift because, from the year 2017, maintenance for HCM Australia applications developed in Web Dynpro for Java will be discontinued. As a result, customers are advised to implement these applications using Web Dynpro ABAP instead, which are available from enhancement package 7 (EHP7) onwards.

The specific Australian HCM applications mentioned in the note that should be now deployed using Web Dynpro ABAP are:
- Superannuation Australia PS (Infotype 0507)
- Absence for Australia PS (Infotype 0573)
- Activity with Higher Rate of Pay (Infotype 0509)

No data migration is needed to transition to these ABAP-based applications. The note also includes a link to the documentation for further details regarding these infotypes.

The note includes no references to other documents or notes. It is a standalone document outlining the necessary action points for SAP customers using Australian HCM applications within the public sector.