SAP Note 1480563 addresses an issue where the layout of a transaction appears differently when it is displayed as part of the reorganization (REORG) process compared to its direct display in the backend system. The specific transactions that are affected include those related to the reorganization of profit centers and Funds Management (FM) documents, such as displaying documents in FB03 or using the RFFMCCF_DISP transaction.

**Symptom**:
Users observe that the layout of a transaction document or screen is different when accessing it through the REORG process compared to directly accessing it in the backend.

**Other Terms**:
Keywords related to this issue include reorganization of profit center, FM documents reorganization, display document, FB03, and RFFMCCF_DISP.

**Reason and Prerequisites**:
The issue arises because a different layout is applied when the transaction is run in batch mode during the REORG process.

**Solution**:
The SAP Note provides correction instructions that should be implemented by the users. Once applied, the transaction will be launched directly, not in batch mode, which should ensure that the layout is consistent with direct backend system display.

**References**:
This note references SAP Note 1471153, which provides a composite overview related to profit center and FM reorganization. It serves as a central source for identifying related notes and gives information about prerequisites, such as the need for business functions FIN_GL_REORG_1 or PSM_FM_REASSIGN, and the possible requirement to check for additional license fees. It also stresses the necessity of having SAP Note 1668882 in the user's system for the correct implementation of notes in certain SAP Basis versions.

In summary, SAP Note 1480563 offers a solution to correct the display issue for transactions viewed out of the REORG process, ensuring that the document/screen layout is consistent with the layout seen when directly displaying transactions in the backend system.