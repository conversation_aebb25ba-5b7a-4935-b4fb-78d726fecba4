SAP Note 2448371 addresses an issue wherein the InfoObject 0CUSTOMER is not being correctly filled in certain Inventory Management advanced DataStore Objects (aDSOs) within SAP HANA-optimized BW Content. Specifically, the note mentions inaccurate data appearing in the analysis executed on HANA CompositeProvider /IMO/V_MMIM01 which impacts several aDSOs related to inventory management:

- /IMO/D_MMIM01 (Material Stock - Quantities)
- /IMO/D_MMIM02 (Material Stock - Values)
- /IMO/D_MMIM10 (Material Movements - Documents)

The problem is caused by incorrect mapping in the transformations:

- RSDS 2LIS_03_BF to TRCS /IMO/MMIM_IS01
- ADSO /IMO/CMMMIM01 to TRCS /IMO/MMIM_IS01 (with a specific Transformation GUID)
- ADSO /IMO/CMMMIMH1 to TRCS /IMO/MMIM_IS01 (with a specific Transformation GUID)

The solution provided in the note is to update to the latest support package which contains the correct mapping for the InfoObject 0CUSTOMER, now properly associated with the source field KUNNR in the specified transformations. 

The note recommends installing the following support packages to resolve the issue:
- BI Content 757 SP16 (SAPK-75716IN<PERSON>CONT) or later
- BW/4 HANA Content 100 SP04 (SAPK-100004INBW4CONT) or later

Furthermore, the note advises reactivating the content after the support packages are applied. There are no references to other SAP Notes within this note.