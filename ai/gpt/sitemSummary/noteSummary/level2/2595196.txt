SAP Note 2595196 details the release notes for the archive *70JDS*.SAR of Software Provisioning Manager (SWPM) 1.0. This note covers the no longer supported Java and Dual-Stack options of SAP NetWeaver 7.0, including TREX 7.0, SAP Business Suite products, and SAP Solution Manager releases.

**Symptom**:
This note chiefly addresses the support status of Java and Dual-Stack options for SWPM 1.0 as it relates to specific SAP NetWeaver 7.0 Java and dual-stack solutions, including TREX 7.0, SAP Business Suite products, and certain SAP Solution Manager releases.

**Other Terms**:
Terms referenced include Software Provisioning Manager 1.0, dedicated archive handling, and specific NetWeaver releases.

**Reason and Prerequisites**:
The note mentions that the SAP NetWeaver 7.0 Java and TREX 7.0 have reached end of maintenance by the end of 2017. It advises upgrading to a more recent version and suggests consulting SAP Note 1648480 for recommended upgrades. Additionally, it refers to handling of unsupported operating systems in SAP Note 2505142.

**Solution**:
This note outlines the continued coverage for the mentioned NetWeaver Java and dual-stack scenarios provided via the archive *70JDS*.SAR and stipulates the use of the guide 'swpm10_sp22_processes_70x_java_dualstack_trex.pdf' even for later service pack versions of SWPM 1.0.

The note includes a table listing various reported issues related to different platforms and their descriptions, indicating fixes or specific observations made on particular dates.

General sections like "Documentation" and "How to get" provide guidance on accessing guides and obtaining the necessary SAR file, inclining users to use the latest SWPM SP22 guides. The note lists the specific steps to download and extract the SWPM media and to secure the necessary "Kernel for installation/SWPM" and Java media.

Further parts of the note contain segments with detailed advice for planning and preparation, running the SWPM, and descriptions of issues and resolutions associated with system copy, installation, and post-installation activities across multiple platforms and database combinations.

Numerous problems are addressed with solutions ranging from environmental variable settings, handling of LABEL.ASC files, JDBC driver issues on IBM i, to problems associated with operating system PTFs and specific database platforms.

Lastly, the note ends with reminders for follow-up activities, troubleshooting tips, and references to additional SAP Notes that offer further information or solutions for particular issues discussed within the note.