SAP Note 688720 addresses issues with the where-used list for transactions in area menus, specifically in transaction SE43 up to Basis Release 4.6B. The problems identified include incomplete lists, disappearance of transactions from the list after an area menu change, and the list showing too many transactions.

The note states that programming errors are responsible for the missing transactions and unnecessary entries are due to the standard processing in transactions SE41 (Menu Painter) and SE43 (Area Menu Editor). For example, a transaction that is deleted from a menu branch of an area menu might still be listed because it may be used elsewhere in the area menu.

The solution offered involves importing a specified support package (for Basis Release 4.6B, 4.6C, or 4.6D) or implementing a correction available in the note (as of Basis Release 4.0B). To fill gaps in the where-used list, it suggests:

1. System-wide correction by executing report SAPRSEUB, which may take several hours, according to the guidelines of SAP Note 28022.
2. Correction for an individual area menu by executing the RS_CUA_TCODE_INDEX function module in test mode with specific parameters for the object name.

The note, however, points out that the support package and note correction do not remove unnecessary entries in the where-used list. It provides a manual process for removing these entries in SE43, which involves selecting and deleting unused functions after executing the menu function "Utilities --> Unused objects," and generating the area menu again.

The note also refers to SAP Note 28022, which deals with an incomplete where-used list for SAP objects in customer systems. Note 28022 details the recommendation to use the report SAPRSEUB to build an index for the where-used list, the potential memory requirement, and the considerable duration of the report execution. It stresses that the index does not update automatically after system upgrades or package imports necessitating a re-run of SAPRSEUB to keep the index up-to-date. It also suggests checking SAP Note 2234970 for information regarding the EU_INIT job, which should not run concurrently with SAPRSEUB.