SAP Note 1694903 - SEG: FIN_GL_REORG_SEG should check whether New G/L is active

Summary:

- **Issue**: The business function for segment reorganization, identified as FIN_GL_REORG_SEG, should include a validation to check if the New General Ledger (New G/L) accounting functionality is active within the SAP system.

- **Relevant Terms**: The terms related to this note are FIN_GL_REORG_SEG and NewGL, which refer to the functionality for financial general ledger segment reorganization and the New General Ledger in SAP, respectively.

- **Cause of Issue**: The note identifies that the requirement for a check is due to a program error.

- **Prerequisites for Resolution**: A prerequisite mentioned for applying the solution provided in this note is that the user's system should be on Enhancement Package 6 (EhP 6) for SAP.

- **Resolution**: To resolve the issue, users are instructed to apply the correction instructions attached with the note.

The note does not directly provide the details of the correction instructions but informs users that they are attached for application. It also does not reference other notes but is itself referenced by two other composite notes:

1. **SAP Note 1627018**: Related to segment reorganization, which provides guidance on the topic and advises users to check related notes for more detailed solutions.
   
2. **SAP Note 1471153**: Concerns the reorganization of profit centers and Funds Management, advising to check related notes for profit center reorganization and to contact their SAP Account Executive regarding additional license fees.

In essence, SAP Note 1694903 specifies an improvement to the segment reorganization business function that would involve adding a check to ensure that the New G/L functionality is active before proceeding with the reorganization tasks. Users need to apply the provided corrections to fix the identified program error and should be on the specified release level to do so.