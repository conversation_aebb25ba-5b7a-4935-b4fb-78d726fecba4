SAP Note 1655743 addresses the auditing of dynamically created or deleted ABAP source code. It proposes two levels of loggings: an overview logging in the Security Audit Log and a detailed logging in the database.

The overview logging records when ABAP source code creation or deletion occurs, who executed these changes, and which reports were used. Detailed logging, on the other hand, also logs the source code itself alongside administrative and program attributes.

For the overview logging, one needs to activate it via transaction SM19 in the Security Audit Log, while detailed logging requires setting the profile parameter abap/dyn_abap_log to "on" using RZ11.

Logged data include unique identification numbers for events, event types, and fingerprints of generated source codes. Review of this data can be done in the Security Audit Log using transaction SM20 for overview records, or via SE16 for database entries where the generated source is stored, and can be viewed using the ABAP program RDYNABAP_SHOW.

Performance impact is minimal with logging enabled, though detailed logging leads to large data volume in tables DYNABAPHDR and DYNABAPSRC.

The SAP system periodically checks these tables and deletes obsolete records after a certain duration, defined by the abap/dyn_abap_log_storage_days profile parameter (recommended at 27 days). Batch deletion can be managed with the abap/dyn_abap_log_deletion_rows profile parameter (defaulting to 10,000 entries per step).

SAP Note 1655743 also makes references to related notes:
- SAP Note 1885830, dealing with unexpected program terminations and excessive deletions of dynamic ABAP source code, with a solution to either implement a correction or import a Support Package.
- SAP Note 1789606, describing an issue with recording long source code lines and providing a solution via kernel patch.
- SAP Note 1693438, discussing the downporting of the "Auditing of dynamic ABAP source code" functionality to Basis Release 7.31, which requires a Support Package and a kernel patch.

This summarization of SAP Note 1655743 elucidates the logistics of auditing dynamic ABAP source code in SAP systems, detailing how to activate the necessary loggings, assess the impact on performance, and manage the data lifecycle within the audit tables.