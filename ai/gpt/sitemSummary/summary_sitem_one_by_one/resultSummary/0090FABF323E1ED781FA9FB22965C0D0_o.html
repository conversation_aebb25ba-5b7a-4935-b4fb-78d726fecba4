<h3>SI7: FIN_MISC_PDCE</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2442292">2442292 - 2442292 - S4TWL - PDCE - Product Design Cost Estimate</a></strong></p>
<p><strong>Description:</strong></p>
<p>The PDCE solution is not available in SAP S/4HANA. Reason for this is that even in SAP ERP this solution was barely used by customers anymore and customers had moved to the recommended successor solution (SAP Product Lifecycle Costing or PLC) which is also available in SAP S/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The product design cost estimate is a solution for planning, monitoring, and optimizing the costs of new products during the planning, design, or quoting phase. The costs of many products are largely determined by engineering. Therefore, you use the product design cost estimate in an early phase of the product life cycle. At that point, the product costs can only be approximately estimated with reference to an incomplete data basis, but you can set the course for the real costs that occur later.</p>
<p><strong>Main transactions not available in SAP S/4HANA on-premise edition:</strong></p>
<ul>
<li>UAMO          Product Design Cost Estimate</li>
<li>UAMO_REPORTING       UAMO Reporting</li>
<li>UAST       Costing Solutions: Structure Maint.</li>
<li>UA_CE_REPORT          Costing Engine Analysis</li>
<li>UA_DATABASIS          Define Data Basis</li>
<li>UA_IMG          IMG for Costing</li>
<li>UA_STRUC_MAINT1      Costing Solutions: Structure Maint.</li>
</ul>
<p>You can see the whole list of affected PDCE transactions in the content which is delivered for PDCE in the custom code check. For more detail please refer to notes 2190420, 2241080 and the AS ABAP documentation.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Remove all potential references in your custom code to PDCE objects as these may no longer use any more in SAP S/4HANA. Please use the SAP S/4HANA specific custom code check in SAP code inspector for this purpose.</p>
<p>If you still need a functionality equivalent to PDCE, please look into introducing the recommended successor functionality (SAP Product Lifecycle Costing or PLC). For details please see attached documents.</p>
<p>If you had built custom code extensions on top of PDCE, please look into if you still need them on (SAP Product Lifecycle Costing or PLC) and if required re-implement them there.</p>
<p><strong>Reference Notes:</strong> 
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2441395">2441395 - You cannot perform a conversion to SAP S/4HANA because of PDCE business functions being active in your productive system.</a></li>
</ul>
</p>