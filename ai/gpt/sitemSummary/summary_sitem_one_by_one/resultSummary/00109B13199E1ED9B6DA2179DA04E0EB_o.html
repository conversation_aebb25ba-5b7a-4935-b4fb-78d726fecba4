<h3>SI2: Logistics_WM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2889253">2889253 - S4TWL - Task & Resource Management</a></strong></p>
<p><strong>Description:</strong></p>
<p>Task and Resource Management (LE-TRM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Extended Warehouse Management (SAP EWM).</p>
<p><strong>Business Process Impact:</strong></p>
<p>No immediate influence on business processes expected related to Task and Resource Management. The functionality Task and Resource Management (LE-TRM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Task and Resource Management (LE-TRM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>
<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>
<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Task &amp; Resource Management development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2882809">2882809 - Scope Compliance Check for Stock Room Management</a>, <a href="https://launchpad.support.sap.com/#/notes/2269324">2269324 - Compatibility Scope Matrix for SAP S/4HANA</a></p>