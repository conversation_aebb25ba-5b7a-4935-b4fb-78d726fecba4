<h3>SI2: Cross Industry - BRIM - Common Object Layer (COL)</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2270317">2270317 - 2270317 - S4TWL - BRIM - Common Object Layer (COL)</a></strong></p>
<p><strong>Description:</strong></p>
<p>The Common Object Layer Framework (COL framework) can be used to distribute data (e.g. master data, provider contracts) from the SAP CRM System to the SAP Convergent Charging and SAP ERP System. In SAP S/4HANA, on-premise edition the distribution between these systems is done directly, the framework is not provided any more. The BRIM solution covers CRM, FI-CA (ERP) and SAP Convergent Charging components/systems. The Common Object Layer was developed in SAP_APPL to provide the technical possibility to distribute master data from ERP to SAP Convergent Charging without using the software component FI-CA. However, no customer used this feature. The BRIM solution is always used with the FI-CA or CI component. Thus, the Common Object Layer is not available in SAP S/4HANA, on-premise edition. The distribution of objects is directly done in the software component FI-CA now.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected for customers: the master data distribution still works in SAP S/4HANA, on-premise.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>To check if you distribute master data with the help of the COL framework without using the component FI-CA, call the transaction SE16 to check the table BADIIMPL_ENH. Enter "ISX_MD_COL_SCENARIO" and "ISX_MD_COL_ADAPTER" in the field BADI_NAME. Check if there are any entries starting with Z* or Y* in the column BADI_IMPL (BAdI Implementation). Be aware of the change that the Common Object Distribution is unavailable in SAP S/4HANA, the distribution is done directly in FI-CA component. No business process influence expected.</p>