{"guid": "0050569455E21ED5B3E17678390B409E", "sitemId": "SI1: SD_GENERAL", "sitemTitle": "S4TWL - SD Simplified Data Models", "note": 2267306, "noteTitle": "2267306 - S4TWL - SD Simplified Data Models", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Business Value</strong></strong></p>\n<p>Data model simplifications in Sales and Distribution (SD) offer the following benefits:</p>\n<ul>\n<li>Reduced memory footprint through</li>\n<ul>\n<li>elimination of index tables</li>\n<li>elimination of status tables</li>\n<li>simplified document flow</li>\n<li>fewer aggregate tables</li>\n</ul>\n<li>Increased performance due to the integration of status tables into document tables.</li>\n<li>Field length extension of SD document category allows for functional scope enhancements <br/>with new code values for SD document category.</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>The following data model simplifications have been implemented for the SD area:</p>\n<ul>\n<li>Elimination of status tables VBUK, VBUP: Status fields have been moved to the corresponding header and item tables - VBAK and VBAP for sales documents, LIKP and LIPS for deliveries, VBRK for billing documents.<br/>This leads to increased performance, e.g.:</li>\n<ul>\n<li>Selection of document header data and document header status requires one SELECT statement (VBAK) instead of two SELECT statements (VBAK and VBUK). Similar for the item tables.</li>\n<li>Views containing document header data and document header status can be built without JOIN condition.</li>\n</ul>\n<li>Simplification of document flow table VBFA</li>\n<li>Field length extension of SD document category:</li>\n<ul>\n<li>Data element VBTYP (Char1) has been replaced by data element VBTYPL (Char4);</li>\n<li>Field VBTYP_EXT (Char4) has been eliminated</li>\n</ul>\n<li>Elimination of redundancies – Document index tables VAKPA, VAPMA, VLKPA, VLPMA, VRKPA, VRPMA</li>\n<li>Elimination of redundancies</li>\n<ul>\n<li>Rebate index table VBOX: See simplification item \"SD Rebate Processing replaced by Settlement Management\", SAP Note <a class=\"sapMLnk sapMLnkMaxWidth sapMLnkSubtle\" href=\"javascript:void(0);\" id=\"__link8-__clone52\" tabindex=\"0\" target=\"_blank\">2267377</a>.</li>\n<li>LIS tables S066, S067: See simplification item \"Credit Management\", SAP Note <a class=\"sapMLnk sapMLnkMaxWidth sapMLnkSubtle\" href=\"javascript:void(0);\" id=\"__link8-__clone60\" tabindex=\"0\" target=\"_blank\">2270544</a>.</li>\n</ul>\n</ul>\n<p>The changes are described in more detail in the guidelines attached to the related SAP Note 2198647.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>No effects on business processes are expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ol>\n<li>Adapt customer code according to the custom code check results, see SAP Note 2198647 for a detailed description.</li>\n<li>If you have added one or more append structures to database tables VBUK or VBUP (document status header and item table) in the source release to store additional data for documents that are persistent in tables VBAK/VBAP, LIKP/LIPS, or VBRK, and these fields are to be used in SAP S/4HANA, you must ensure that the appended fields are added to the respective document tables.</li>\n</ol>\n<p>An append field to the document header status table VBUK must be added to one or several of the document header tables VBAK, LIKP, or VBRK. This decision depends on which of the document types the respective field is relevant for. An append field to document item status table VBUP must be added to one or more of the document item tables VBAP or LIPS. Again, the choice depends on which of the document types the respective field is relevant for.</p>\n<p>Each append field must have the same name and the same data type as it does in the source table, and each one has to be appended to the table-specific status include structure (DDIC structures VBAK_STATUS, LIKP_STATUS, VBRK_STATUS, VBAP_STATUS or LIPS_STATUS). Only then will the field be considered by the automatic data conversion from VBUK to VBAK/LIKP/VBRK and from VBUP to VBAP/LIPS, as well as by read function modules that can be used to replace direct SELECTs to the deprecated status tables VBUK/VBUP.</p>\n<p>Do not choose a different field name or data type. Do not append the field directly to the document table. It is possible to have several append structures for one status include structure.</p>\n<p><em>When and where do you have to add the append fields to the status include structures in order to have the data transferred automatically during the SAP S/4HANA system conversion?</em></p>\n<p>In the development system, you have to add the fields during phase ACT_UPG, when you are prompted for SPDD. In all other systems, you may use modification adjustment transports or customer transport integration during the upgrade-like conversion.<br/>The append fields must be contained in one of the custom transport requests from the development system.</p>\n<p>Until target release S/4HANA 1610: The conversion pre-check class CLS4H_CHECKS_SD_DATA_MODEL delivered via SAP Note 2224436 provides a check for such append fields and issues a warning if such append fields are recognized in the source release. The warning refers to the necessary action to add the append fields to the status include structures.</p>\n<p>As of target release S/4HANA 1709: The check for append fields is integrated into the upgrade tools (SUM). The check is executed automatically as part of phase ACT_UPG. If append fields to VBUK or VBUP are found SUM prompts a confirmation whether adjustmants have been done and whether the upgrade/conversion can be continued.<br/>The check only finds appends fields if they belong to the namespaces Z* or Y*.<br/>Append fields of a namespace /*/, e.g. /ABC/, do not lead to a confirmation prompt as the upgrade tools cannot safely distinguish the different owner types of /*/ namespaces (customers or partners or SAP-managed components).<br/>The check is also not executed if there is already a SPDD transport integrated into the conversion procedure.<br/>(See above, \"When and where do you have to add the append fields to the status include structures...?\")</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Transition Worklist Item is relevant if the custom code check shows results that refer to note 2198647.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"168\">\n<p>Conversion pre-checks</p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP Note 2224436</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"168\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Customer code has to be adapted according to the cookbooks in SAP Note 2198647.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 7, "refer_note": [{"note": "2224436", "noteTitle": "2224436 - <PERSON>4TC SAP_APPL - Checks for SD Data Model", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Pre-Transition Checks for the SD data model have to be executed before the upgrade to S/4.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions in this note. The checks will be executed during the transition process.</p>\n<p>This note refers to an 'old' pre-check class (used up to conversion with target release 1610).<br/>As of conversion target release 1709 this pre-check class is not needed any more.</p>", "noteVersion": 8}, {"note": "2267377", "noteTitle": "2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>The Settlement Management Solution provides a single point of entry to maintain and to administrate  contract-related conditions. Condition contract settlement allows to set up different rebate scenarios and settlement processes to fulfill your business requirements by customization. Flexible data sources, business selection criteria, settlement calendar and further features allow a streamlined contract entry and contract processing. Condition Contract Management offers an overview of actual business volume even for retroactively contracts and a rich functionality to control open settlements, business volume and accruals.</p>\n<p><strong>Description</strong></p>\n<p>In general, SD Rebate Processing is not available within SAP S/4HANA. The functional equivalent of SD Rebate Processing in SAP S/4HANA is Settlement Management.</p>\n<p>However, there is one exception: CRM TPM customers can still use SD Rebate Processing for their business process, but have to adapt to an SAP S/4HANA-optimized solution (cp. simplification item Optimization of SD Rebate Processing for TPM Customers, note 2267344).</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>In SAP S/4HANA, Settlement Management replaces SD Rebate Processing, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. New agreements can only be created based on condition contracts. Therefore, the corresponding transaction codes VBO1 resp. VB(D for the creation of rebate agreements is not available anymore.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"229\">\n<p>Transaction not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"366\">\n<p>VBO1     Create Rebate Agreement<br/>VB(D      Extend Rebate Agreements</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>See the attached documents for more detailed information.</p>\n<p>Some countries like countries in Latin America require localization. For example Latin American authorities require invoicing with pre-assigned number through transaction IDCP. This functionality is not available as of now for settlement management documents. Please verify if settlement management solution fulfills the local requirements in the your specific countries and reach out to the SAP localization team in case you have questions.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p>Use SE16 to check table KONA: If table KONA contains records with ABTYP = ‘A’ then SD Rebate Processing is used.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2226380</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 7, "refer_note": [{"note": "2267344", "noteTitle": "2267344 - S4TWL - Optimization of SD Rebate Processing for TPM Customers", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong><span>Business Value</span></strong></strong></p>\n<p><span><span>Redundant index data, to determine relevant documents, was removed. This leads to a significantly r<span><span>educed database footprint</span></span>.<br/>Additional information can be found in note 2200691 and the documentation/notes for business function LOG_SD_REBATE_INDEX.</span></span></p>\n<p><strong>Description</strong></p>\n<p>Customers with an active CRM TPM or Trade Management license can continue using SD Rebate Processing, even though a successor is already being provided by Settlement Management (cp. simplification item SD Rebate Processing replaced by Settlement Management).</p>\n<p><strong>Business Process-Related information</strong></p>\n<p>For more information, see note <a href=\"/notes/2226380\" target=\"_blank\">2226380</a>.</p>\n<p>For these customers, the existing SD Rebate functionality has been optimized with regard to the database footprint. For this purpose, from a technical viewpoint, the rebate index table VBOX is not available.</p>\n<p>Licensed customers can optionally enable the creation of rebate agreements directly in sales via removal of the blacklist entries for VB01 (Note <a href=\"/notes/2249880\" target=\"_blank\" title=\"https://launchpad.support.sap.com/#/notes/2249880\">2249880</a>)<em>.</em> However, for the TPM process this is not necessary at all, since for this business process, rebate agreements are created by CRM TPM.<em> </em></p>\n<p>Regardless of any TPM licences the usage right of SD Rebates in SAP S/4HANA will end with the end of standard or extended maintenance of SAP ERP/ECC.</p>\n<p>The SD Rebate solution is only provided in the current scope. Upcoming legal requirements and additional funcitionalities will <strong>only</strong> be covered in the Settlement Management solution.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>If the customer is using the extended SD rebate processing, a rebuild of the S469 content is required after the upgrade.</li>\n<li>The customer has to maintain new customizing settings.</li>\n</ul>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>This Transition Worklist Item is only relevant if the customer has an active Trade Promotion Management or Trade Management license.<br/></span></p>\n<p>The S/4HANA Readiness Check determines this Transition Worklist Item as relevant if SD Rebates have been used in the customer system (table KONA has entries with field ABTYP Equal to 'A'). <br/>But in fact only those customers are affected which have an active Trade Promotion or Trade Management license.</p>\n<p>The general changes for SD Rebates are described in note<br/><a href=\"/notes/2267377\" target=\"_blank\">2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management</a></p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: <a href=\"/notes/2200691\" target=\"_blank\">2200691</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 11}, {"note": "2226380", "noteTitle": "2226380 - S/4 HANA: Deprecation of SD Rebate Processing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are using an SAP ERP system and intend to perform the upgrade-like conversion to SAP S/4HANA.</p>\n<p>The custom code check shows customer objects that are affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to SAP Note 2226380.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In general, the functionality SD Rebate Processing is not supported in SAP S/4HANA. The successor of SD Rebate Processing is Settlement Management.</p>\n<p>However, there is one exception: because the integration between CRM Trade Promotion Management (TPM) and Settlement Management has only been added in CRM 7.0 EhP4 SP05 and S/4HANA 1610 FP01, customer with an active TPM or Trade Management license can currently still use SD Rebate Processing. Customers with an active TPM or Trade Management license can still generate rebate agreements using the condition generation feature of CRM TPM. Licensed customers can optionally enable the creation of rebate agreements directly in sales via removal of the blacklist entries for VB01 (Note <a href=\"/notes/2249880\" target=\"_blank\" title=\"https://launchpad.support.sap.com/#/notes/2249880\">2249880</a><em>).</em></p>\n<p>Regardless of any TPM licences the usage right of SD Rebates in SAP S/4HANA will end with the end of standard or extended maintenance of SAP ERP/ECC.</p>\n<p>For all other customers, the following is valid:</p>\n<p>SAP objects used by the customer objects are deprecated and shall not be used any more.</p>\n<p>Custom code does not comply with the scope and data structure of SAP S/4HANA.</p>\n<p>The usage of SD Rebate Processing for this case is limited to the maintenance period of the CRM TPM and Trade Management solutions.</p>\n<p>The SD Rebate solution is only provided in the current scope. Upcoming legal requirements and additional funcitionalities will <strong>only</strong> be covered in the Settlement Management solution.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>All usages of SAP objects in customer objects for which the custom code check refers to Note 2226380 will no longer work, and must be removed.</p>\n<p>For customers without an active TPM or Trade Management license, existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. New agreements can only be created based on condition contracts. Therefore, the following corresponding transaction codes for the creation of rebate agreements are only available for customers with an active TPM or Trade Management license:</p>\n<ul>\n<li>Create Rebate Agreement VBO1</li>\n<li>Extend Rebate Agreements VB(D</li>\n</ul>", "noteVersion": 7}]}, {"note": "2198647", "noteTitle": "2198647 - S/4 HANA: Data Model Changes in SD", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4 HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to note 2198647 which covers data model changes in the area SD (Sales and Distribution).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note contains detailed descriptions how to adopt customer objects to the data model changes in SD.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The attached documents describe in detail how adoption has to be done for the following data model changes:</p>\n<ol>\n<li>\n<div>Elimination of status tables VBUK and VBUP</div>\n</li>\n<li>\n<div>Change of the technical structure of document flow table VBFA</div>\n</li>\n<li>\n<div>Field length extension of field VBTYP (SD document category) and elimination of field VBTYP_EXT.</div>\n</li>\n<li>Elimination of index tables for SD documents by Material and by Customer.</li>\n</ol>\n<p>Note that VBFA-STUFE is supported again as of S/4HANA 1709 and equivalent Support packages in 1610 and 1511.<br/>The attached cookbook for VBFA also explains dependencies related to this change.<br/>Compare also related notes 2469315 and 2470721.</p>", "noteVersion": 9, "refer_note": [{"note": "1471153", "noteTitle": "1471153 - Composite note for profit center and FM reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note is a composite SAP Note. Under \"Related notes\", you will find all relevant SAP Notes for the profit center and Funds Management reorganization (reassignment with FI-GL (new) in Funds Management). We have used the following nomenclature in the short text for the purpose of differentiation:</p>\n<ul>\n<li>PRCTR: Profit Center reorganization</li>\n</ul>\n<ul>\n<li>FM: Funds Management reorganization</li>\n</ul>\n<ul>\n<li>No entry: Both areas</li>\n</ul>\n<ul>\n<li>SEG: Some notes get this additional prefix if they are also relevant for Segment reorganization.</li>\n</ul>\n<p><br/>Important information:<br/>Contact your SAP Account Executive to find out whether there are additional license fees for the use of profit center reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorganization, profit center, profit center reorganization, Funds Management, reassignment with FI-GL (new)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have activated the business function FIN_GL_REORG_1 or PSM_FM_REASSIGN and you use profit center or Funds Management reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Check the related notes.</p>\n<p>For problems with the implementation of SAP Notes, check whether the following SAP Note of SAP Basis exists in your system:</p>\n<p>1668882 - Note Assistant: Important notes for SAP_BASIS 730,731,740,750</p>\n<p>If you require support for the implementation, open an incident under the component BC-UPG-NA.</p>\n<p> </p></div>", "noteVersion": 7}]}, {"note": "2270544", "noteTitle": "2270544 - S4TWL - Credit Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA and you are using Credit Management (FI-AR-CR) on SAP ERP. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the following we distinguish between the three possible constellations in your system:</p>\n<ol>\n<li>You are only using FI-AR-CR Credit Management in your source release</li>\n<li>You are only using SAP Credit Management (FIN-FSCM-CR) in your source release</li>\n<li>You are using both Credit Management solutions (FI-AR-CR &amp; FIN-FSCM-CR) in your source release</li>\n<li>You are not using any of the Credit Management solutions (Then this note is not relevant for you)</li>\n</ol>\n<p>(with the prefixes A, B, C, D we mark the explanations as relevant for these scenarios in the following chapters)</p>\n<p><strong>Description</strong><strong>:</strong></p>\n<p>Credit Management (FI-AR-CR) is not available as part of SAP S/4HANA. The functional equivalent in SAP S/4HANA is SAP Credit Management (FIN-FSCM-CR).</p>\n<ul>\n<li>Risk category -&gt; Risk class maintained for the whole business partner (not per customer &amp; credit control area), as the probability of a credit default of your customer does depend on the characteristics of your customer and not your internal organizational structures.</li>\n<li>Maintenance of customer master data in business partner transaction</li>\n<li>The credit checks carried out for a customer depend on the assigned check rule in the business partner transaction. They do not depend on the risk class (formerly risk category).</li>\n<li>Releasing blocked documents via Case Management (documents the blocking reason)</li>\n</ul>\n<p><strong>How to Determine Relevancy:</strong></p>\n<p>A: Indicators for the usage of FI-AR-CR Credit Management are:</p>\n<ul>\n<li>You are using transaction FD31 or FD32 to maintain credit account master data (this data is stored in table KNKK &amp; KNKA)</li>\n<li>You are using transactions VKM1-4 to process sales documents (e.g. releasing blocked sales orders)</li>\n<li>Function module UKM_IS_ACTIVE returns space for flag E_ACTIVE and E_ERP2005</li>\n<li>In general: You are using parts of the obsolete transactions and reports listed in chapter “Business Process related Information”.</li>\n<li>Exposure data is stored and updated continuously in database tables S066 and S067.</li>\n</ul>\n<p>B: Indicators for the usage of SAP Credit Management (FIN-FSCM-CR) are:</p>\n<ul>\n<li>You are using transaction BP or UKM_BP to maintain credit account master data (this data is stored in table UKMBP_CMS_SGM &amp; UKMBP_CMS)</li>\n<li>You are using transaction UKM_CASE to process sales documents (e.g. releasing blocked sales orders)</li>\n<li>Function module UKM_IS_ACTIVE returns ‘X’ for parameter E_ACTIVE and E_ERP2005</li>\n</ul>\n<p>C: Indicators of A and B apply for you.</p>\n<p>D: None of the indicators of A and B apply for you.</p>\n<p><strong>Business Value:</strong></p>\n<ul>\n<li>Higher degree of automation, thanks to automatic calculation of credit scores, risk classes and credit limits</li>\n<li>Credit events help the credit analyst to focus on exceptions during his daily business</li>\n<li>Create a relationship from credit analysts to customers</li>\n<li>Process of credit limit requests</li>\n<li>Documented credit decisions</li>\n<li>Higher / lower account relationship of business partners (adds up exposure of lower accounts on higher accounts)</li>\n<li>Less redundant data (reading FI-data via HANA view)</li>\n</ul>\n<p><strong>Business Process related information</strong><strong>:</strong></p>\n<p>A &amp; C: You need to migrate FI-AR-CR to FIN-FSCM-CR. The migration from one-system landscapes is supported by tools that SAP provides in the IMG. (see details in chapter “Required and Recommended Action(s)”)</p>\n<p><span>Influence on Business Processes:</span></p>\n<p>A &amp; B &amp; C: There will be no change to your business processes because of the system conversion.</p>\n<p>A &amp; C: However, some transactions become obsolete and are replaced by new transactions.</p>\n<p>Here are some examples for such cases:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Examples for replaced transactions in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>For the maintenance of the credit account master data, transaction FD32 is replaced by transaction UKM_BP.</p>\n<p>For releasing credit-blocked sales orders, transaction VKM1 is replaced by transaction UKM_MY_DCDS.</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transactions not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>F.28 - Customers: Reset Credit Limit<br/>F.31 - Credit Management - Overview<br/>F.32 - Credit Management - Missing Data<br/>F.33 - Credit Management - Brief Overview<br/>F.34 - Credit Management - Mass Change<br/>FCV1 - Create A/R Summary<br/>FCV2 - Delete A/R Summary<br/>FCV3 - Early Warning List<br/>FD24 - Credit Limit Changes<br/>FD32 - Change Customer Credit Management  (but FD33 still available for Migration checks)<br/>FDK43 - Credit Management - Master Data List<br/>S_ALR_87012215 - Display Changes to Credit Management<br/>S_ALR_87012218 - Credit Master Sheet<br/>VKM1 - Blocked SD Documents<br/>VKM2 - Released SD Documents<br/>VKM3 - Sales Documents <br/>VKM4 - SD Documents<br/>VKM5 - Deliveries</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Reports not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>RFARI020 - FI-ARI: Extract from credit master data<br/>RFARI030 - FI-ARI: Import credit master data<br/>RFDFILZE - Credit Management: Branch/Head Office Reconciliation Program<br/>RFDKLI*NACC Reports<br/>RFDKLxx Reports</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The following settings in Customizing are affected by the migration:</p>\n<ul>\n<li>All IMG activities for SAP Credit Management are listed in Customizing under the following path: <em>Financial Supply Chain Management</em> &gt;<em> Credit Management.</em></li>\n<li>The credit limit check for sales document types can only be specified as type D in SAP Credit Management or can be left empty (no credit limit check). The credit limit check types A, B, and C are no longer available.</li>\n<li>The credit check when changing critical fields has been removed from the SD side.</li>\n<li>The payment guarantee-related Letter of Credit has been removed from the SD side.</li>\n<li>A credit check on the basis of the maximum document value is not supported in SAP S/4HANA 1511 SP00 and SP01.<br/>It is supported as of SAP S/4HANA 1511 FPS2. </li>\n</ul>\n<p><strong>Required and Recommended </strong><strong>Action(s):</strong></p>\n<p>B: Migrating Credit Management is not needed. Review of BadIs “UKM_R3_ACTIVATE” and “UKM_FILL” is advisable. Think of using the default implementation provided by SAP. (FI-data will then be read real-time with the default implementation of BadI “UKM_R3_ACTIVATE”. The implementation of BadI “UKM_FILL” is then not needed anymore.)</p>\n<p>D: Nothing to do, migration of Credit Management is not needed.</p>\n<p>A &amp; C: You need to migrate FI-AR-CR to FIN-FSCM-CR completely. This migration comprises several elements:</p>\n<ul>\n<li>Configuration data</li>\n<li>Master data</li>\n<li>Credit exposure data</li>\n<li>Credit decision data</li>\n</ul>\n<p>The migration from one-system landscapes is supported by tools that SAP provides.</p>\n<p>Reviewing the migrated Customizing is advisable afterwards: For example, risk classes are now maintained once for a customer (for all segments) instead of maintaining them per customer’s credit control area.</p>\n<p>1. Prerequisites for the migration to SAP Credit Management:</p>\n<ul>\n<li>You have completed all documents related to payment guarantee Letter of Credit.</li>\n<li>You have completed the migration for Accounting. </li>\n<li>If you are running on your own code, you have eliminated the usage of SAP objects in your own code. For details on how to adapt user-defined customer code that relies on the changed objects, see the following SAP Notes:</li>\n<ul>\n<li>2227014 (Financials)</li>\n<li>2227014 (SD)</li>\n</ul>\n</ul>\n<p>2. Perform the recommended actions in the Task List PDF attached to this Note (only available in English). This list is only valid for the conversion of one-system landscapes.</p>\n<p>C (additionally): Review of BadIs “UKM_R3_ACTIVATE” and “UKM_FILL” is advisable. Think of using the default implementation provided by SAP.</p>\n<p> </p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information (SD)</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2217124</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information (Financials)</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2227014</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 12, "refer_note": [{"note": "2217124", "noteTitle": "2217124 - S/4 HANA: Credit Management Changes in SD", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4 HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to note 2217124 which covers credit management changes in the area SD (Sales and Distribution).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<div class=\"WordSection1\">\n<p>S4_TRANSFORMATION</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note contains detailed descriptions how to adopt customer objects to the data model changes in SD.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You must eliminate the indicated usages from your customer objects for:</p>\n<p>R3TR TABL S066<br/>R3TR TABL S067<br/>R3TR PROG SD_VKMLOG_SHOW<br/>R3TR PROG VAKCR_REBUILD<br/>R3TR PROG RVKRED03<br/>R3TR PROG RVKRED04<br/>R3TR PROG RVKRED05<br/>R3TR TABL VKMI<br/>R3TR TABL VAKCR<br/>R3TR TRAN VKM2<br/>R3TR TRAN VKM3<br/>R3TR TRAN VKM5<br/>R3TR CLAS CL_CRED_VAL_LOG</p>\n<p> </p>", "noteVersion": 6}, {"note": "2227014", "noteTitle": "2227014 - S/4 HANA: Credit Management Changes in FI", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4 HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note contains detailed descriptions how to adopt customer objects to the data model changes in FI.</p>\n<p>The changes of credit management in the area of sales and distribution can be checked via note 2217124.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You must eliminate the indicated usages from your customer objects for:</p>\n<p>Program R3TR PROG MF01AO00<br/>Program R3TR PROG MF02CO00<br/>Program R3TR PROG RFCMCRCV<br/>Program R3TR PROG RFCMDECV<br/>Program R3TR PROG RFDKLI10<br/>Program R3TR PROG RFDKLI20<br/>Program R3TR PROG RFDKLI20_NACC<br/>Program R3TR PROG RFDKLI30<br/>Program R3TR PROG RFDKLI40<br/>Program R3TR PROG RFDKLI40_NACC<br/>Program R3TR PROG RFDKLI41<br/>Program R3TR PROG RFDKLI41_NACC<br/>Program R3TR PROG RFDKLI42<br/>Program R3TR PROG RFDKLI43<br/>Program R3TR PROG RFDKLI50<br/>Program R3TR PROG RFDKLIAB<br/>Program R3TR PROG RFDKLIAB_NACC<br/>Program R3TR PROG RFDKVZ00_NACC<br/>Table R3TR TABL T024B<br/>Table R3TR TABL T691B<br/>Transaction R3TR TRAN OB02<br/>Transaction R3TR TRAN S_ER9_11000074<br/>View R3TR VIEW V_T024B<br/>Table R3TR TABL KNKK<br/>Table R3TR TABL KNKA</p>", "noteVersion": 2}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}