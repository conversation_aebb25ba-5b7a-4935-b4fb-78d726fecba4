{"guid": "0050569455E21ED5B3E176783915409E", "sitemId": "SI4: Logistics_PP", "sitemTitle": "S4TWL - Graphical Planning Table", "note": 2268050, "noteTitle": "2268050 - S4TWL - Graphical Planning Table", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Gantt-Chart; Graphical Planning Board; Capacity Levelling; Transactions CM21, CM22, CM23, CM25, CM29, MF50; REO; DMC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The classic Gantt-chart control only works in SAP-Gui, not in Fiori-UIs</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Graphical planning tables (also known as graphical planning board or Gantt chart) can be used for capacity load levelling and sequence planning. SAP offered basic capacity planning by means of transactions CM21, CM22, etc. and advanced capacity planning in PP/DS. This note is relevant for basic capacity planning by means of CM* transactions.</p>\n<p>Transactions CM21, CM22, CM23, CM25, etc with graphical planning table are still available in SAP S/4HANA on-premise edition, but not considered as the target architecture. Graphical Planning Table is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, Graphical Planning Table  can be found under the ID 451.</p>\n<p>Transactions CM* will be replaced by apps like \"Manage Work Center Capacity\", \"Capacity Scheduling Table\", and \"Capacity Scheduling Board\" in SAP S/4HANA. The use-cases covered and the features provided by the new apps are similar to the classic CM* transactions. The new apps are available from release SAP S/4HANA on-premise edition 1909 and in cloud from 1908.</p>\n<p>The Planning Table for Repetitive Manufacturing (Transaction MF50) uses the graphical planning table as well. The transaction MF50 will continue to be available, but the graphical planning table can only be used as part of the SAP S/4HANA compatibility scope until end of expiry date. There is no alternative planned for a graphical visualization in MF50.</p>\n<p>Alternatively consider SAP Digital Manufacturing Cloud DMC component Resource Orchestration REO. REO works on more granular data defined in Digital Manufacturing Cloud. In a job-shop environment for example work-centers could be defined in SAP S/4HANA and the individual machines only in DMC. Dispatching from the work-center level to the individual machine level is the domain of REO. Further information can be found on http://help.sap.com/dmc.</p>\n<p>Use embedded PP/DS for complex scheduling problems, if for example planned and production orders of different BOM levels need to be synchronized, or if material availability needs to be considered in scheduling, or if multiple bottleneck resources must be aligned. PP/DS is embedded into S/4HANA. It is available on-premise. For details refer to note 2666947.</p>\n<p><strong>Business Process related information</strong></p>\n<p>It will not be possible to translate customizing and personalization of the classic graphical planning table into a UI configuration of the new graphical planning table.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Consider to migrate to the new apps \"Manage Work Center Capacity\", \"Capacity Scheduling Table\", and \"Capacity Scheduling Board\"</p>\n<p>or evaluate REO</p>\n<p>or evaluate (embedded) PP/DS.</p>", "noteVersion": 13, "refer_note": [{"note": "2666947", "noteTitle": "2666947 - Restrictions and Implementation Recommendations for Production Planning and Detailed Scheduling for SAP S/4HANA 1809", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides information about restrictions and recommendations for Production Planning and Detailed Scheduling for SAP S/4HANA 1809.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PP/DS</p>\n<p>Production Planning and Detailed Scheduling</p>\n<p>PPDS</p>\n<p>PP/DS for SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With Production Planning and Detailed Scheduling for SAP S/4HANA 1809, some restrictions apply to the productive use of certain functions in the area of Production Planning and Detailed Scheduling (PP/DS). SAP wishes to inform customers of these restrictions and provide some recommendations here.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p><strong>Production Planning and Detailed Scheduling for SAP S/4HANA</strong></p>\n<p>Production Planning and Detailed Scheduling (PP/DS) for SAP S/4HANA is not a legal successor of the PP/DS component from the SAP SCM Server. Data migration and customer code migration from PP/DS component from the SAP SCM Server is not supported by the standard solution.</p>\n<p><strong>Basic manual settings needed to set up PP/DS</strong></p>\n<ul>\n<li>Set up integrated liveCache and the relevant operational jobs by following the SAP S/4HANA installation and operations guide.  Also refer SAP note 2407589 (Manual installation of liveCache for S/4HANA).</li>\n<li>Run the mandatory reports SLCA_INIT_FOLLOW_UP, /SAPAPO/OM_CREATE_LC_TABLES. You can now use transaction LCA03 to check the functionality of the system in an SAP-liveCache-relevant business client.</li>\n<li>Activate the flag for Advanced Planning in customizing. To activate PP/DS for SAP S/4HANA in customizing, select <em>Advanced Planning &gt; Basic Settings &gt; Activate Advanced Planning and Scheduling</em></li>\n<li>Set up the Model '000' and Version '000' manually and check whether they exist. The PP/DS Horizon can only be maintained at the level of planning versions in SAP S/4HANA.</li>\n<li>Set the application indicator for ND-APO (New Dimension Plug-In APO) to active. (Transaction BF11). Verify if ND-APO and NDI (New Dimension Integration) is checked.</li>\n<li>Set up the CIF customizing in customizing by choosing Basic Settings for Creating the System Landscape, and by including SAP S/4HANA with system type SAP_APO and release 713 so that an Integration model pointing to itself (own system and client) can be created and logical system and queue types could be maintained. Set up CIF user parameters. Register the CIF queue (Transaction SMQR). There is no need for maintaining distribution definition as the PP/DS is part of SAP S/4HANA. SAP recommends using CIF Post-Processing as the error-handling option to avoid queue blocks.</li>\n<li>Verify Configuration Schema in Customizing for whether it matches your business need. Select <em>Advanced Planning &gt; Basic Settings &gt; Define Configuration Schema (CDP or Variant Configuration)</em></li>\n<li>Enable number range in PP/DS for SAP S/4HANA in PP/DS global parameters and values (Transaction /SAPAPO/RRPCUST1).  In addition, maintain number ranges for production campaigns and interchangeability groups depending on the usage of these functions.</li>\n<li>Check if pre-delivered ATP categories exist. Refer S/4HANA Administration Guide for Implementation and SAP Note 2272406 for more information. Activate BC set /SAPAPO/APO_ON_ERP for enabling pre-delivered customizing of Category Groups.</li>\n</ul>\n<p><strong>Setup and activation of Locations</strong></p>\n<ul>\n<li>Location Type, Business Partner (1021), replaces Customer (1010) and Vendor (1011)</li>\n<li>Execute reports mentioned below create and update locations</li>\n<ul>\n<li>Create – /SAPAPO/CREATE_LOCATION for creating Business Partner location, Plant, MRP Area and Shipping Point.</li>\n<li>Update – /SAPAPO/UPD_LOC_SP_PL for updating Plant, MRP Area and Shipping Point</li>\n</ul>\n<li>Use following BAdIs in case adaption is needed.</li>\n<ul>\n<li>Create Business Partner locations on saving a Business Partner – /SAPAPO/LOC_CREATE</li>\n<li>Change the location details – /SAPAPO/LOC_DETAILS</li>\n</ul>\n<li>During upgrade process BAdI SMOD_APOCF001 should be adapted with one of the BAdI’s mentioned above.</li>\n<li>Freely defined attributes of location are not supported.</li>\n<li>Post upgarade, the variants saved for creating Integration model for involved objects Customers, Vendors and Plants cannot be used as this is obselete.</li>\n</ul>\n<p><strong>Setup and activation of Materials</strong></p>\n<p>SCM Product Master is integrated into the SAP S/4HANA Product Master with CDS Redirects on material master.  For more information, refer SAP note 2342043.  The SCM Product master is read-only for active version and can be used for maintaining simulative parameters in simulation versions.</p>\n<ul>\n<li>Local material creation in PP/DS is not supported.</li>\n<li>Segmentation is also not supported in PP/DS.</li>\n<li>Activate PP/DS Materials by selecting the “Advanced Planning” checkbox in the \"Advanced Planning\" tab of the material master.</li>\n<li>Freely defined attribute of Product Master is not supported.</li>\n<li>SCM Extensibility Feature for SCM Related Fields in Product Master and data mapping, refer SAP Note 2590603 for further information. It is not possible to customize the standard mapping provided between S/4HANA Material master and PP/DS for S/4HANA Product master.</li>\n</ul>\n<p>Materials that were created in SAP S/4HANA releases prior to 1610 have to be enabled for usage in PP/DS in SAP S/4HANA. To do so run report PRD_SCM_GUID_CONVERSION. For more information, check the report documentation.﻿</p>\n<p>PP/DS product master (<em>Transaction /SAPAPO/MAT1</em>) is read-only for active planning versions, but can be used for maintaining simulation dependent parameters for inactive planning versions.</p>\n<p>If Stock in transfer, Blocked Stock and Restricted-use Stock are changed in customizing (Production -&gt; Material Requirement Planning -&gt; Planning -&gt; MRP Calculation -&gt; Stocks -&gt; Define Availability of Stock in Transfer/Blocked/Restricted Stock), it may lead to inconsistencies in Pegging area with liveCache. This inconsistency can be detected and resolved with Consistency Match liveCache - APO Database report (<em>Transaction /SAPAPO/OM17</em>).</p>\n<p>It is recommended to use MRP type with following MRP Procedure “X Without MRP, with BOM explosion” for materials relevant to advanced planning.</p>\n<p>Creation of Transportation Lanes automatically with the usage of Special Procurement Key is supported only in the following scenarios:</p>\n<ul>\n<li>During Material Creation when a material which is 'relevant' for Advanced Planning is created and Special Procurement Key is maintained at MRP Area</li>\n<li>During Material Change when a material is changed from 'not relevant' for Advanced Planning to 'relevant' for Advanced Planning and Special Procurement Key is maintained at MRP Area at once before save</li>\n</ul>\n<p><strong>Setup and activation of Resources</strong></p>\n<ul>\n<li>Local resource creation in PP/DS is not supported.</li>\n<li>Enable Target-System independent Settings in CIF (Transaction CFC9) to enable \"APO Resources\" in Work Center Capacity: header tab. This is required to send to PP/DS different resource types and resource categories. This would be required to transfer handling unit, calendar resources etc.</li>\n<li>Change transfer mode “No Change Transfer” is not supported in PP/DS for SAP S/4HANA in Target-System independent Settings in CIF (Transaction CFC9). In case “No Change Transfer”  is maintained, the value gets defaulted to ‘N’ (Change Transfer for Pl.-Vers-Independent Resource and PV 000)</li>\n<li>Setup the Capacity Versions for integrating external capacities from ERP in customizing by selecting <em>Advanced Planning &gt; Master Data &gt; Resource &gt; Specify Capacity Variants</em>. We recommend that you use external capacities as much as possible.</li>\n<li>Activate PP/DS work centers by selecting the “Advanced planning” checkbox</li>\n<li>To create a Handling Resource for Storage Location and subcontracting MRP areas, the report /SAPAPO/COPY_RESOURCE_HU can be used</li>\n<li>Capacity Variants, Shifts and Definitions are disabled in SAP GUI when External Capacity is selected for Resources, to prevent data inconsistencies when you are also using Monitor Capacity Utilizaton - Object Page. In case this app is not used and maintenance needs to be enabled, refer SAP note 2598760.</li>\n</ul>\n<p><strong>Setup and activation of Source of Supply</strong></p>\n<p>In-house production:</p>\n<ul>\n<li>Use transaction CURTOADV_CREATE, CURTOADV_CREATE_FOCUS (in the SAP Easy Access Menu, select <em>Logistics &gt; Master Data &gt; PDS for Advanced Planning</em>) to enable PDS transfer to PP/DS. The transactions CURTO_CREATE, CURTO_CREATE_BOM, CURTO_CREATE_FOCUS cannot be used for integrating in-house production source of supply.</li>\n<li>BOM for phantom assemblies mandatorily need production versions and need to be transferred to PP/DS using CURTOADV_CREATE.</li>\n<li>For automatic change transfer of PDS on change implement BAdI CUSLNTRTO_ADDIN.</li>\n</ul>\n<p>External Procurement:</p>\n<ul>\n<li>Create the integration model for scheduling agreements, contracts, and purchasing info records which needs to be integrated to PP/DS should be used using integration model maintenance and activate it</li>\n</ul>\n<p><strong>Activation of Orders and Batches</strong></p>\n<p>By default, all material dependent orders and batches are integrated into liveCache for all PP/DS-relevant materials.  The settings are controlled in customizing by selecting <em>Advanced Planning &gt; Basic Settings &gt; Settings for Data Transfer</em></p>\n<p>In case of Sales Scheduling Agreement, if delivery schedules are managed in SAP S/4HANA, and not in PP/DS, you need to unflag check box SD Sched. Agmt. Refer SAP note 2570941 for details.</p>\n<p><strong>Re-transfer of Orders</strong></p>\n<p>Complete retransfer of material dependent orders can be achieved with the report /SAPAPO/PPDS_DELTA_ORD_TRANS. We recommend running this report during a timeframe where orders are not changed in the system. The orders changed while running the report need to be reintegrated using CIF Delta Reconciliation Report.</p>\n<p><strong>Automatic Creation of Hierarchies for Planning Product</strong></p>\n<p>Automatic Creation of Hierarchies for Planning Product is not supported. The assignment of final products to a planning product using hierarchies should be done manually.</p>\n<p><strong>Forecast Consumption</strong></p>\n<ul>\n<li>Consumption period is calculated in calendar days in PP/DS for SAP S/4HANA and in working days in SAP S/4HANA. A calendar can be assigned to respect working days by using BAdI /SAPAPO/BADI_FC_CSP_CLNDR. Refer SAP Note 2619345 for details.</li>\n<li>Maintain the Category Group in customizing by selecting Advanced Planning &gt; Maintain Category Groups and add relevant ATP categories. Category group defines which categories (of customer requirement) consume PIR.</li>\n<li>Check Mode is not displayed in PP/DS product master and cannot be changed. The corresponding strategy group of the requirement class has to be defined in ERP material master. In PP/DS, the Requirement Class has to be defined as Check Mode (same identifier).  The Assignment mode maintained in Check mode and the allocation indicator maintained in the Requirement Class should be the same for consistency in the system’s behavior. Maintain requirement class in customizing by selecting Cross-Application Components -&gt; Advanced Available-to-Promise -&gt; Configuration Activities for specific document types -&gt; Sales Order and deliveries -&gt; Configure Requirement Classes and maintain Check mode by selecting Advanced Planning &gt; Maintain Check Mode.</li>\n<li>Trigger the Forecast consumption using Report /SAPAPO/CSP_CORRECT_FCST in case in the Product view the Elements tab result is different from that of the Forecast tab</li>\n<li>The following consumption modes are supported by PP/DS for SAP S/4HANA</li>\n<ul>\n<li>1 - Backward Consumption Only</li>\n<li>2 - Backward/Forward Consumption</li>\n<li>3 - Forward Consumption Only</li>\n<li>5 - Period-Based Consumption</li>\n</ul>\n<li>The requirement strategies must respect note 1241642.</li>\n</ul>\n<p><strong>MRP Live</strong></p>\n<p>All downstream materials should be Advanced Planning-relevant if planned together with MRP Live. This means the data modelling should be such that once a material at one BOM level is PP/DS-relevant (Advanced Planning-relevant), then the materials above that BOM level should also PP/DS-relevant (Advanced Planning-relevant). This is to ensure that depended demands are also in liveCache or PP/DS.</p>\n<p>Low-level code calculation happens for all materials, whether those are relevant to Advanced Planning or not, in SAP S/4HANA through MRP Live. Therefore, any specific process that uses a different low-level code calculation needs to be planned independently, such as Planning with Planning Package, Interchangeability, Planning with Co-Products, Planning with Planning Product .</p>\n<p>Creation of planning file entries and BOM components determination happen while integrating planning documents from PP/DS to SAP S/4HANA. Due to this reason, MRP Live is not supported for non-regenerative planning process or for planning BOM components only in SAP S/4HANA, if planning documents are not integrated to SAP S/4HANA.</p>\n<p>Locations corresponding to Storage location MRP areas (location type 1007) and subcontracting MRP areas (location type 1050) are planned before planning the corresponding plant locations MRP Live will not support planning with stock transfer between MRP areas.</p>\n<p>MRP Live does not take under consideration for planning, the Products that have Cross Plant Material Status and Plant-Specific Material Status for 'Material is used in MRP' as 'Error Message', deletion indicator for Plant and Material, and MRP Procedure as 'No MRP' (for both Plant and MRP Area).But in case you are using product heuristics from other PP/DS applications, the above statuses are not respected and the product will be planned.</p>\n<p>If MRP Procedure is specified as 'No MRP' in Plant, then the corresponding MRP areas will not get planned.</p>\n<p>In case you are using Rapid Planning Matrix (RPM) for PP/DS automotive cases it is recommended to use MRP type with following planning methods</p>\n<ul>\n<li>6 - Import Dependent Requirements from Advanced Planning: Should be used for components which need to read dependent requirements from PP/DS and are to be planned in standard MRP. MRP Live adheres to this as well.</li>\n</ul>\n<p><strong>Availability to Promise Integration</strong></p>\n<p>PP/DS for SAP S/4HANA is integrated to SAP S/4HANA ATP for planned and production/process orders for component check. In addition, integration is also available during planned order conversion. In the above cases, an SAP S/4HANA ATP check is triggered for the components of planned/production/process orders for the corresponding integrated PP order.</p>\n<p>PP/DS planned orders that are present only in liveCache and PP/DS planned orders that have a mismatch in the components (which are selected for advanced planning) between the liveCache and the corresponding integrated PP orders are not supported. For example, business processes like Interchangeability/simple discontinuation, planning with characteristics depended planning, planning for long products.</p>\n<p>For non-supported features, your customizing setting for order conversion should ensure that no ATP check is triggered (appropriate ATP conversion rule)</p>\n<p>A characteristics-based ATP check is not supported.</p>\n<p>ATP Check will not consider fix pegging relationships.</p>\n<p>ATP check for stock transfer requisitions/orders, purchase requisitions/orders with components (in case of subcontracting) is not supported.</p>\n<p>ATP Check for Planned Orders for Rapid Planning Matrix (RPM) Products is supported for component check during order conversion for PP/DS for SAP S/4HANA without integration to PP orders, if used along with advanced-ATP. For this case, it is recommended to use MRP type with following planning method 5 - Planned by Advanced Planning (RPM/iPPE only).  This planning method should be used for the header material (KMAT) so that ATP check works.</p>\n<p><strong>Production Scheduling Board (Fiori App)</strong></p>\n<p>Campaign planning and block planning are not supported.</p>\n<p>Editing Resource master data together with the same sim-session as that of the Production Scheduling Board is not supported.</p>\n<p>Creation of Downtimes and Fixing intervals from Production Scheduling Board is not supported.</p>\n<p>Time Profile setting options ‘Segments’ and ‘Hide Non-WT’ are not supported.</p>\n<p>Create order independently without referencing an operation is not supported.</p>\n<p>Optimization feature from Production Scheduling Board is not supported.</p>\n<p>Selection of multiple orders by one cursor movement is not supported.</p>\n<p>Find function (from the menu: More - Edit - Find) is not supported.</p>\n<p>The following standard heuristics are supported while working with Production Scheduling Board</p>\n<ul>\n<li>SAP001 - Schedule Sequence</li>\n<li>SAP002 - Remove Backlog           </li>\n<li>SAP003 - Schedule Sequence Manually (Fiori UI pop-up supported)</li>\n<li>SAP004 - Minimize Runtime       </li>\n<li>SAP005 - Schedule Operations  </li>\n<li>SAP_DS_01 - Stable Forward Scheduling              </li>\n<li>SAP_DS_02 - Enhanced Backward Scheduling</li>\n<li>SAP_DS_03 - Change Fixing/Planning Intervals</li>\n<li>SAP_DS_04 - Activate Seq.-Dependent Setup Activities</li>\n</ul>\n<p>Additional heuristics are also supported if the heuristics are configured to be executed in the background, without a user interface.</p>\n<p>Only production Resource types are supported.</p>\n<p>For a list of SAP notes to be implemented, refer corresponding release information note for the app.</p>\n<p><strong>Monitor Capacity Utilization (Fiori App)</strong></p>\n<p>Only production Resource types are supported.</p>\n<p>Shift maintenance is supported only for capacities of type external capacities.</p>\n<p><strong>Prerequisites</strong></p>\n<p>Maintain breaks, shifts and shift intervals with transaction OP4A.</p>\n<p>Workcenter/Resource maintenance: Check below pre-requisites updated in CR01/CR02/CRC1/CRC2:</p>\n<ul>\n<li>Check 1: Active Version is maintained as ‘01’.</li>\n<li>Check 2: Factory Calendar is maintained .</li>\n<li>Check 3: Advance Planning to be checked.</li>\n<li>Check 4: External capacity to be checked.</li>\n<li>Check 5: Grouping to be maintained and configure shift definition without overlap.</li>\n</ul>\n<p><strong>Limitations</strong></p>\n<ul>\n<li>Only Setup, Produce and Teardown activities are considered</li>\n<li>Evaluation Profile will influence the result</li>\n<li>Maximum horizon period is 26 weeks</li>\n<li>Shift table can not be maintained for Standard Capacity interval</li>\n<li>Only two types of Downtime possible (Planned Downtime, Resource is Inactive)</li>\n<li>Deletion of downtime is not possible.</li>\n<li>Maximum 9 shifts can be add as in CR02.</li>\n<li>WorkDay flag in interval pop is display-only field and supports</li>\n<ul>\n<li>Space : i.e Working days according to the factory calendar</li>\n<li>One  :  i.e Working days (overrides factory calendar)</li>\n</ul>\n<li>Utilization displayed maximum upto 999% both micro chart and overview chart.</li>\n</ul>\n</div>\n<div class=\"longtext\">\n<p><strong>Deletion of Advanced Planning-relevant Work Centers</strong></p>\n<p>Capacity deletion in advanced planning-relevant work centers functions like it does in the conventional integration model, without change transfer being switched on. Deletion of the resources in PP/DS has to be performed separately.</p>\n<p>Changing resource types and resource categories needs existing Resources to be deleted in PP/DS.</p>\n<p>In case you want to add capacity with the same category only for the purpose of SAP S/4HANA ERP, and not for PP/DS, then the Advance Planning flag must be removed, after which you can save this addition.</p>\n<p>If you try to create a new Advanced Planning-relevant work center without capacity, you get an error message.</p>\n<p><strong>Removal of Advanced Planning flag from Material Master</strong></p>\n<p>Removing Advanced Planning flag from Material Master will not delete the related orders or data from PP/DS. This has to be performed separately.</p>\n<p><strong>Usage with EWM as a part of S/4HANA</strong></p>\n<p>It is possible to use EWM and PP/DS which are part of S/4 HANA in the same client.</p>\n<p><strong>Usage of “Side-by-Side SCM-APO” as a part of S/4HANA</strong></p>\n<p>Certain versions (refer SAP Note 2376061) of a “Standalone SCM-APO” system running side-by-side with SAP S/4HANA are supported (including all functions integrating standalone SNP, DP and PP/DS).</p>\n<p>\"Standalone SCM-APO\" and PP/DS for SAP S/4HANA can be used together, but the data modelling should ensure that there is no data dependency.</p>\n<p>Integration of orders generated out of \"Standalone SCM-APO\" to PP/DS for SAP S/4HANA or vice versa is not supported.</p>\n<p><strong>Usage of Integrated Business Planning (IBP) as a part of S/4HANA</strong></p>\n<p>Integration of orders generated out of IBP to PP/DS for SAP S/4HANA or vice versa is not supported.</p>\n<p>IBP and PP/DS for SAP S/4HANA can be used together, but the data modelling should ensure that there is no data dependency.</p>\n<p>For more information refer note 2732174.</p>\n<p><strong>Special Procurement Types</strong></p>\n<p>The following special procurement types from SAP S/4HANA are supported in PP/DS for SAP S/4HANA:</p>\n<ul>\n<li>Subcontracting</li>\n<li>Stock transfer</li>\n<li>Phantom assembly</li>\n<li>Production in alternative plant (called \"production in alternative location\" in SAP APO)</li>\n</ul>\n<p><br/>The following special procurement types from SAP S/4HANA are <strong>not </strong>supported in PP/DS for SAP S/4HANA:</p>\n<ul>\n<li>Withdrawal from alternative plant</li>\n<li>Direct production/collective order</li>\n<li>Phantom in planning</li>\n</ul>\n<p><strong>Lot-Sizing Procedure Fixing and Splitting</strong></p>\n<p>The lot-sizing procedure Fixing and Splitting (FS) is not supported in PP/DS for SAP S/4HANA. Explanation and recommendation: If planning is performed in SAP S/4HANA, all partial lots created when using FS cover exactly one requirement and are scheduled in a sequence (in contrast to FX, where all partial lots are scheduled to start at the same time).</p>\n<p>Though the planning is executed infinitely, the FS procedure spreads the planned orders. This makes finite planning that might be done later in SAP S/4HANA easier, especially if there are no planned orders of other products using the same resource. In PP/DS for SAP S/4HANA, this lot-sizing procedure is not available because better options are available to create a finite schedule for planned orders.</p>\n<p><strong>Planning with Validity Recommendations of BOMs and Routings</strong></p>\n<p>Regarding planning with limited validities of BOMs and routings, see documentation note 385602.</p>\n<p><strong>Production Data Structure (PDS)</strong></p>\n<p>The supported functionality of PDS differs from that of the production process model (PPM). PPM is not supported in PP/DS for SAP S/4HANA.The differences are described in SAP Note 1079959. The limitations of the PDS functionality are described as follows:</p>\n<p><br/>Block Planning:</p>\n<ul>\n<li>Classification of operations with class type 018 is not possible for recipes. If recipes are used in SAP S/4HANA and block planning is used in PP/DS for SAP S/4HANA, a BAdI must be implemented for the classification of the activities.</li>\n</ul>\n<p><br/>Variant Configuration and Object Dependencies:</p>\n<ul>\n<li>To add characteristic requirements to an input node, it is necessary to implement the BAdI method CHANGE_EXPL_RESULT of the BAdI /SAPAPO/CULLRTOEXPL or to use an ABAP class that is maintained at the input node. In case of non-configurable BOM, it is possible to set default requirements by maintaining batch classification in the BOM.</li>\n</ul>\n<p>Note: When a Stock Transfer Requisition with characteristic requirement is converted in SAP S/4HANA or changed in SAP S/4HANA after the conversion is triggered from PP/DS for SAP S/4HANA, there might be inconsistencies in characteristic-requirement data of the converted/changed Stock Transfer Orders. It is not recommended to directly convert STR with characteristic-requirement in SAP S/4HANA or change the converted STO afterwards. Subsequent documents like delivery will be created without requirements in PP/DS for SAP S/4HANA which may cause broken pegging relationships.</p>\n<p><br/>PP-PI/Recipes:</p>\n<ul>\n<li>Relationships between phases of different recipes are not supported.</li>\n</ul>\n<ul>\n<li>No material quantity calculation.</li>\n</ul>\n<ul>\n<li>No product flow (container resource).</li>\n</ul>\n<p><br/>Comparison Report PDS and SAP S/4HANA Explosion:</p>\n<ul>\n<li>The comparison report (transaction /SAPAPO/RTO_ORD_COMP) does not compare the following:</li>\n</ul>\n<ul>\n<li>Sub-operations and secondary resources</li>\n<li>Setup groups and setup keys</li>\n<li>Configuration of the input and output products</li>\n<li>Durations of order internal relationships</li>\n<li>Continuous input/output must be set using a BAdI.</li>\n</ul>\n<p><br/>The PDS is NOT RELEASED for the following functionalities:</p>\n<ul>\n<li>Parameter efficiency</li>\n</ul>\n<ul>\n<li>Extended order generation (EOG)</li>\n</ul>\n<ul>\n<li>Multiple output planning (MOP)</li>\n</ul>\n<p>Since only PDS is possible in PP/DS for S/4HANA, the support for the above functionalities is NOT RELEASED. For EOG and MOP, BAdIs are available that can be used to implement specific use cases.</p>\n<p>Production version status \"Locked for automatic sourcing\" is not considered by PDS.</p>\n<p><br/>Planning with Phantoms:</p>\n<ul>\n<li>When using the PDS, it is not allowed to use the same phantom several times in the BOM of a finished product in case a component of this phantom is assigned to different operations in the routing.</li>\n<li>Production versions are mandatory for phantoms in S/4HANA.  Phantom BOM needs to be integrated to PP/DS for S/4HANA with transaction curtoadv_create</li>\n</ul>\n<p><strong>Editing Production Data Structure (PDS)</strong></p>\n<p>Engineering Change Management (ECM)-relevant changes for components cannot be simulated using the 'PDS Edit' functionality. However, ECM-relevant PDS, transferred from SAP S/4HANA, can be copied to inactive planning versions.</p>\n<p><strong>Fixed Pegging</strong></p>\n<p>Fixed pegging in PP/DS for SAP S/4HANA is not kept during all document type changes in SAP S/4HANA. SAP Note 698427 lists all supported document type changes. Note the following restrictions:</p>\n<ul>\n<li>The stock transfer orders have two nodes. Fixed pegging is applicable for the supply node at the target location, but not for the requirement node at the source location.</li>\n</ul>\n<ul>\n<li>Not supported at all are forecasts and forecast consumption, scheduling agreements (neither SD nor MM) and REM backflush, since these are not sensible from a process point-of-view.</li>\n</ul>\n<ul>\n<li>Collective orders are not supported in PP/DS for SAP S/4HANA, and consequently, not supported by fixed pegging either.</li>\n</ul>\n<ul>\n<li>Assembly processing is not supported (No integration of SAP S/4HANA assembly orders (planned orders)).</li>\n</ul>\n<ul>\n<li>Document type changes from customer inquiry to customer quotation and from customer quotation to sales order are not supported.</li>\n</ul>\n<ul>\n<li>Document type change from transport order to delivery is not supported.</li>\n</ul>\n<p><br/>The limitations of fixed pegging with respect to other functions and processes are described in SAP Note 704583. Some additional comments:</p>\n<ul>\n<li>Shelf Life: Fixed pegging does not take shelf life restrictions into account.</li>\n</ul>\n<ul>\n<li>Make-to-Order: Though it is technically possible to use fixed pegging in a make-to-order scenario as well, it does not make much sense because the assignment between demand and receipt is already given by the account planning segment.</li>\n</ul>\n<ul>\n<li>Prioritization: Though fixed pegging does not calculate any priorities by itself, it can propagate the priorities from the requirement.</li>\n</ul>\n<ul>\n<li>Batch Determination: Batch determination in SAP S/4HANA is not influenced in any way by the fixed pegging in PP/DS for SAP S/4HANA. The batch determination in SAP S/4HANA does not trigger creation of the corresponding fixed pegging arc in PP/DS for SAP S/4HANA. It is however possible to use the heuristic SAP_PP_019 to create fixed pegging in PP/DS for SAP S/4HANA based on the same batches, even though this is not an online process. A prerequisite for this is that no fixed pegging must have existed before.</li>\n</ul>\n<ul>\n<li>Quantity Propagation: Changes in the order quantity of a requirement do not lead to the adjustment of the order quantity of a fixed pegged receipt element and vice versa.</li>\n</ul>\n<ul>\n<li>Multiple scheduling lines in sales and purchase orders: Considerable information for the integration between SAP S/4HANA and PP/DS for SAP S/4HANA is based on the order item and not on the schedule line item. Therefore, during document type changes, the quantities for the fixed pegging are kept for the order item but not necessarily for the correct schedule line.</li>\n</ul>\n<ul>\n<li>The heuristic for creating fixed pegging can be called before or after a MRP-level-based Production Planning Run. During the planning of single products or between MRP levels in a Production Planning Run, it is not possible to include the SAP_PP_019 heuristic because there is no possibility in the Standard Heuristic Framework to integrate other heuristics in addition to the product heuristics. If you want to integrate logic similar to that provided by SAP_PP_019 within a product heuristic (for example, in SAP_PP_002 - Standard Lot Heuristic), the BAdI /SAPAPO/RRP_PLANNING must be used to create fixed pegging before the Net Requirements Calculation (Method PEGID_GET_IO)</li>\n</ul>\n<p><strong>Safety Stock in PP/DS</strong></p>\n<ul>\n<li>The Virtual Safety Stock Elements option that is used to cover unexpectedly high requirements is the only safety stock method supported in PP/DS for SAP S/4HANA. The option Real Safety Stock Requirements that is needed to resolve delays or problems in production is not supported.</li>\n</ul>\n<ul>\n<li>To consider dynamic safety stock (for example, safety days supply) in PP/DS for SAP S/4HANA, new alerts 'stock below safety stock,' 'stock below target stock,' and 'stock exceeds target stock' are provided. Characteristics, shelf-life and minimum-maximum pegging intervals are not considered for the new stock alert types.</li>\n</ul>\n<ul>\n<li>The SAP S/4HANA Customizing value for the percentage of safety stock availability will not be integrated with PP/DS for SAP S/4HANA.</li>\n</ul>\n<ul>\n<li>Product master data of PP/DS is mapped from the SAP S/4HANA material master to utilize the target stock level method functionality. The mapping is as follows</li>\n<ul>\n<li>'SM' if safety stock &amp; time is maintained</li>\n<li>'SB' if only safety stock is maintained</li>\n<li>'SZ' if only safety time is maintained</li>\n</ul>\n</ul>\n<ul>\n<li>Time-dependent target stock level methods are not supported by PP/DS for SAP S/4HANA. Consequently, target stock alerts are only generated when a static target stock level method is used.</li>\n</ul>\n<ul>\n<li>For planning, PP/DS for SAP S/4HANA does not take into account all possible values that can be maintained on the Lot-Size tab in the APO product master.</li>\n</ul>\n<p>PP/DS for SAP S/4HANA supports the following entries:</p>\n<ul>\n<li>Reorder point method: 2 (Reorder supply from location product master)</li>\n</ul>\n<ul>\n<li>Target stock level method: Target days' supply from product master</li>\n</ul>\n<ul>\n<li>Safety stock methods:</li>\n</ul>\n<p>                     No Safety Stock<br/>                     SB - Safety Stock from Location Product Master<br/>                     SZ - Safety Days' Supply from Location Product Master<br/>                     SM - Max. of Safety Stock and Safety Days' Supply from Location Product Master</p>\n<p><strong>Shelf Life</strong></p>\n<p>Regarding shelf life functionality, see SAP Notes 751392 and 563863.</p>\n<p><strong>Interchangeability</strong></p>\n<p>Interchangeability cannot be used along with ATP check. Due to this reason order conversion should ensure that there is no ATP check for the products using interchangeability by setting an appropriate ATP conversion rule.</p>\n<p>Supersession Chain is the only supported group type for interchangeability .</p>\n<p><strong>Reservation Planning</strong></p>\n<p>This functionality is not supported.</p>\n<p><strong>Order Combination for Mill Products</strong></p>\n<p>For more information on this functionality, refer SAP note 2501607</p>\n<p><strong>Planning Scenarios within Consideration of Length Allowance</strong></p>\n<p>Certain planning scenarios within consideration of length allowance are not supported. From the planning perspective, either the operation duration or the operation capacity requirement may be variable (but not both at the same time).</p>\n<p>The solution does not support scenarios where the operation duration is fixed and the operation varies according to operation quantity. Also not supported are setup durations that have been modified using an available BAdI. Setup times are fixed in the PDS.</p>\n<p><strong>Scheduling Mode 'Close Gaps'</strong></p>\n<p>Depending on the complexity of the scenario and the planning environment, the system is not always able to schedule the operation and close existing gaps even though the scheduling mode 'Insert and Close Gaps' is used.</p>\n<p><strong>Compact Planning</strong></p>\n<p>Compact scheduling concentrates on reduction of lead time of orders. To achieve this in a meaningful way, orders should contain no fixed activities. If they do, a compact schedule cannot be guaranteed. In such cases where orders do contain fixed activities (for example, partially confirmed orders), other optimization strategies such as Prioritization or partially confirmed orders (PCO) should be used instead.</p>\n<p><strong>PP/DS Optimizer</strong></p>\n<p>PP/DS Optimizer is an optional component in S/4 HANA. For the supported optimizer version, refer Product Availability Matrix (PAM) of relevant version of S/4 HANA.</p>\n<p>For information regarding installation of SCM Optimizers, refer SAP Note 1686826.</p>\n<p>See SAP Note 712066 for recommendations and restrictions for the PP/DS Optimizer.</p>\n<p>PP/DS Optimizer cannot create orders in PP/DS for S/4HANA,</p>\n<p><strong>Explanation Protocol for PP/DS Optimizer</strong></p>\n<p>The Explanation Protocol is to be used like a protocol; it is not a problem-solving tool. Customers are able to see the explanations but will be unable to jump from an explanation to the place where the problems could be fixed. The navigation inside the explanation GUI is restricted to functionality that is offered by the ALV grid. When the Explanation Protocol has to protocol a large amount of data, the lists inside the GUI are very long and the display could be slow. There is no solution as yet for this problem.</p>\n<p><strong>Transfer Planner from SAP S/4HANA to SAP APO</strong></p>\n<p>The planner in SAP APO does not correspond to the MRP controller in SAP S/4HANA and is not transferred to SAP APO. If it is desired to match the MRP controller to the SAP APO planner, it has to be considered that the field planner in SAP APO has three characters and is independent from a location (in contrast to SAP S/4HANA).<a href=\"file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/67OF1997/Implemetation_Note.docx\" target=\"_blank\"><br/></a></p>\n<p><strong>Capacity Reservation</strong></p>\n<p>This feature is not supported.</p>\n<p><strong>Rapid Planning Matrix (RPM)</strong></p>\n<p>The Rapid Planning Matrix can only be used in combination with the iPPE and with production line resources - and not in combination with shop floor resources (single, multi, single-mixed and multi-mixed). The transaction RPMBAL to display capacity utilization (capacities of resources and their utilization by activities coming from RPM LC matrices) is not released. Also see SAP Note 699773.</p>\n<p><strong>Subcontracting</strong></p>\n<ul>\n<li>The following sub-contracting scenarios are supported:</li>\n</ul>\n<ul>\n<li>Subcontracting Without Source Location in PP/DS</li>\n<li>Subcontracting with Source Location in PP/DS</li>\n<li>Subcontracting with Scheduling Agreements in PP/DS</li>\n<li>Subcontracting with Third-Party Provision of Components in PP/DS</li>\n</ul>\n<p>Source of supply for subcontracting is modelled with a subcontracting PDS. A subcontracting external procurement relationship is linked to this subcontracting PDS.</p>\n<p>Only rough-cut planning is possible at the subcontractor location</p>\n<p>For propagating the changes of a subcontracting info record or subcontracting scheduling agreement to the linked subcontracting PDS, an absolute transfer of a PDS is necessary. Automatic transfer for subcontracting PDS happens only for changes in the production version.</p>\n<p>Subcontracting is modelled with subcontractor location only for components. This needs creation of subcontracting MRP area in S/4HANA and a corresponding subcontractor location of type 1050 be created.</p>\n<p>Assign the subcontracting MRP area in S/4HANA to the components.</p>\n<p>If procurement type “E” is set at plant, the corresponding product-location at subcontractor MRP areas will be mapped with procurement type “X”, as for subcontractor location it do not make any sense to have procurement type is “E”.</p>\n<p> Default ATP categories are delivered for subcontracting purchase requisitions, subcontracting purchase orders and subcontracting scheduling agreements. However this can be changed in customizing -&gt; Advanced Planning -&gt;Global Settings -&gt; Maintain Global Parameters and Defaults ( /SAPAPO/RRPCUST1 )</p>\n<p>Additional output components (i.e. co- or by-products) are not considered by subcontracting processes.</p>\n<p>Below listed topics are not supported:-</p>\n<ul>\n<li>Additional output components (i.e. co- or by-products) are not considered by subcontracting processes.</li>\n<li>Changes in Scheduling Agreement cannot be transferred to PP/DS using Change Transfer. It can be transferred using Absolute transfer.</li>\n<li>Changes (Addition/Deletion) of production version to the subcontracting info record using Tcode - CMPDS1(Customize multiple PDS) cannot be transferred to PP/DS using Change Transfer. It can be transferred using Absolute transfer.</li>\n<li>Subcontracting Purchasing Contract is not supported.</li>\n<li>Product Interchangeability is not supported for the components to be provided to the subcontractor.</li>\n<li>Focus PDS for subcontracting is not supported.</li>\n<li>Multi-Level Subcontracting is not supported. Multistep or multilevel subcontracting allows the scenario when the subcontractor is using their own subcontractors in the production chain for the components.</li>\n<li>Automatic transfer of subcontracting PDS is not supported.</li>\n<li>Creating a subcontracting purchase requisition in S/4 without assigning an info record is not supported. Also creating a subcontracting purchase requisition in S/4 with issuing or receiving location as a storage location MRP area is not supported.</li>\n</ul>\n<p><strong>Serial Numbers</strong></p>\n<p>A material may have a serial number in SAP S/4HANA. However, there is no processing of serial numbers in PP/DS for SAP S/4HANA</p>\n<p><strong>Planning Without Final Assembly</strong></p>\n<p>The system only creates safety stock in the make-to-stock segment. Only demands in the make-to-stock segment can consume stock.</p>\n<p><strong>Keeping Activity Dates when Transferring Orders to PP/DS for SAP S/4HANA</strong></p>\n<p>The functionality to keep activity dates provides the possibility to avoid PP/DS for SAP S/4HANA having to do a rescheduling when orders are transferred from SAP S/4HANA to PP/DS for SAP S/4HANA. The operations of production orders must have the status DSEX in SAP S/4HANA to keep their activity dates in PP/DS for SAP S/4HANA. The activity dates of planned orders are generally retained, as long as the PDS is not re-exploded.</p>\n<p>Limitations:</p>\n<ul>\n<li>Goods receipt time processing activities are always scheduled. However, with correct and unchanged settings at the handling resource and with an unchanged goods receipt processing time, this should not have an impact on the dates of the goods receipt time processing activity.</li>\n</ul>\n<ul>\n<li>Not all finite strategies are supported with this functionality. For some finite strategies, the system switches to infinite scheduling in case the activity dates are kept. However, SAP in any case recommends infinite strategies in the strategy profile for CIF integration.</li>\n</ul>\n<ul>\n<li>Keeping activity dates overrules the settings for synchronization for a resource. If activity dates are kept in PP/DS for SAP S/4HANA, existing violations of other boundary conditions in PP/DS will not be removed; for example, violations of pegging relationships and relationships between operations.</li>\n</ul>\n<p><strong>PP/DS Horizon</strong></p>\n<p>The 'PP/DS Horizon' field is only available in Model and Version Management. It is not present in product master for each product.</p>\n<p><strong>Multiple Partial Confirmations on an Operation of a Manufacturing Order</strong></p>\n<p>Multiple partial confirmations on an operation of a manufacturing order do not reduce the remaining duration of the operation. An operation of a manufacturing order in SAP S/4HANA is partially confirmed. The corresponding SAP APO operation also has the remaining duration dates adjusted correctly. If a second partial confirmation is done on the same operation, you observe in PP/DS that the remaining duration and dates of the operation are not getting updated according to the new (just confirmed) values.</p>\n<p><strong>iPPE Workbench Express</strong></p>\n<p>The iPPE Workbench Express is not released at all for SAP S/4HANA integration and CDP.</p>\n<p><strong>Alternate Sequences of SAP S/4HANA Routing</strong></p>\n<p>Alternate sequences functioning as at present in SAP S/4HANA routings are not supported in integrated PP/DS for SAP S/4HANA orders (See SAP Note 437400).</p>\n<p><strong>Setup Information Integration</strong></p>\n<p>Setup information from SAP S/4HANA, if maintained (non-initial), is taken over in PP/DS for SAP S/4HANA. It has to be thus ensured that both systems have maintained consistent data. Prior to this, PP/DS setup information was retained.</p>\n<p><strong>MRP-based Detailed Scheduling (DS)</strong></p>\n<p>MRP-based DS functionality is not supported.</p>\n<p><strong>Fault-Tolerant Scheduling</strong></p>\n<p>Fault-tolerant scheduling is only used for scheduling of orders and cannot be used for their creation.</p>\n<ul>\n<li>No backtracking in scheduling: A scheduling action can affect more than operation. However, each operation is scheduled only once, independently of previous operations and in a given sequence.</li>\n</ul>\n<ul>\n<li>If a scheduling problem consisting of several operations is solved by an allowed violation of a constraint for the first operation, the scheduling action might still terminate without solution. This could happen because the problem only occurs at the second operation.</li>\n</ul>\n<ul>\n<li>If 'Schedule Infinitely' is chosen as the fault-tolerance option, the scheduling modes 'Insert Operation', 'Insert Operation and Close Gaps' and 'Squeeze In' can cause some operations to be pushed outside the planning horizon (the operation sequence is kept with these scheduling modes). Since infinite scheduling does not allow scheduling outside the planning horizon, the scheduling action can terminate without solution.</li>\n</ul>\n<p><strong>Reference Operation Sets</strong><br/><br/>Reference operation sets are only integrated for PDS and for orders.</p>\n<p><strong>Usage of Long Products Planning (LPP)</strong><br/><br/>For LPP solution to be used, you must consider SAP Note 1101409.</p>\n<p><strong>Model Mix Planning (MMP)</strong><br/><br/>For information on the limitations for Model Mix Planning, see SAP Note 627377. The functionality of the Genetic Algorithm for multilines is limited and should be used only after consultation with SAP. For example, only lines belonging to the planning segment and the restrictions assigned to those lines are taken into account during the optimization run. The Model Mix Planning algorithms do not take the priorities of the customer-independent requirements into account. MMP also does not consider the settings made in 'Model and Version Management'.</p>\n<p><strong>Multilevel Scheduling Framework</strong><br/><br/>The objective of the Multilevel Scheduling Framework is to provide a basis for heuristics such that they can tackle more complex scheduling problems by processing the activity network instead of single activities. The Multilevel Scheduling Framework is not yet released for customer developments as SAP might change the interface without compatibility.</p>\n<p>Order validity is considered such that orders that cannot be scheduled finitely on a resource are scheduled deallocated within the order validity. If it is not possible to schedule the orders deallocated within the validity interval, the orders remain in their original positions.</p>\n<p>The following conditions/parameters are not supported by this heuristic:</p>\n<ul>\n<li>Relationships with maximum length (order internal, or between activities of different orders - that is to say, pegging arcs with maximum length)</li>\n</ul>\n<ul>\n<li>Continuous I/O</li>\n</ul>\n<ul>\n<li>Shelf life</li>\n</ul>\n<ul>\n<li>Characteristics</li>\n</ul>\n<ul>\n<li>Container resource</li>\n</ul>\n<ul>\n<li>Production campaign constraints</li>\n</ul>\n<ul>\n<li>Block planning</li>\n</ul>\n<ul>\n<li>Synchronization on multi-resources</li>\n</ul>\n<p><strong>Operation Split</strong></p>\n<p>The functionality of operation split is not supported in PP/DS for SAP S/4HANA. Also see SAP Note 441777.</p>\n<p><strong>Classification System (CDP vs VC/iBase)</strong></p>\n<p>Usage of VC:</p>\n<p>When running in the one system, CIF transfer for classification data of class type 300 is not required. PP/DS for SAP S/4HANA directly reads class 300 related data from SAP S/4HANA.</p>\n<p>This leads to the following system behavior when using VC-planning in the one system approach:</p>\n<ul>\n<li>CIF transfer for classification data in class type 300 is not required</li>\n<li>APO reads from SAP S/4HANA classification which is assigned to MARA </li>\n<li>The APO product master data transaction /SAPAPO/MAT1 does not show the VC relevant class assignments, which can be viewed on the SAP S/4HANA material master.<br/>(SAP S/4HANA classification data is assigned via MARA whereas the APO product master display shows APO-assignments via /SAPAPO/MATKEY)</li>\n<li>Reference characteristics: <br/>unlike SCM Server, where CIF maps table fields for reference characteristics, PP/DS for SAP S/4HANA requires a mapping of reference characteristics procedure code via BADI /SAPAPO/CURTO_CREATE</li>\n</ul>\n<p><br/>Usage of CDP:</p>\n<p>Prerequisite: <br/>To use planning with CDP (and batches) data in the PP/DS for SAP S/4HANA please activate the content of BC-set /SAPAPO/APO_ON_ERP_CDP. This will introduce customizing for APO classes 230 and 400.</p>\n<p>When running in the one system approach, CIF transfer for classification data in class type 23/300 to type 230/400 is required. Integration models need to be maintained for relevant organization areas for these class types. PP/DS for SAP S/4HANA directly reads related data from the APO-only classes of type 230 and 400.</p>\n<p>This leads to the following system behavior when using CDP-planning in the PP/DS for SAP S/4HANA deployment:</p>\n<ul>\n<li>PP/DS for SAP S/4HANA reads from SAP S/4HANA classification which is assigned to /SAPAPO/MATKEY</li>\n<li>The product master data /SAPAPO/MAT1 shows the CDP relevant class assignments only, assignments to 23/300 can be viewed on the SAP S/4HANA material master.<br/>(SAP S/4HANA classification data is assigned via MARA whereas the APO product master display shows APO-assignments via /SAPAPO/MATKEY)</li>\n</ul>\n<p>Additional Info:</p>\n<p>If the components for subcontracting are planned with characteristic dependent planning (CDP), the characteristic values are not transferred to SAP S/4HANA. Classification is not used for components of in-house production orders. The iPPE side access does not support CDP classification. Reference characteristics: It is necessary to first transfer characteristics and then the BOM and routings. Therefore, it is not possible to read SAP APO master data information in the BAdI implementation.</p>\n<p><br/>The simultaneous use of different configuration schemes - CDP and variant configuration (VC) - within one client is not supported.<br/><br/>In the delta report, the classification is checked for:</p>\n<ul>\n<li>VC regarding the same configuration reference</li>\n</ul>\n<ul>\n<li>CDP regarding the existence of characteristics</li>\n</ul>\n<p>It is possible to perform an extended configuration check for the classification with the effect that the value of the characteristics is checked as well.<br/>Consistency Check in liveCache (Transaction OM17):</p>\n<ul>\n<li>Consistency of information for characteristics-based time series is not checked</li>\n</ul>\n<ul>\n<li>Some checks do not prove full consistency but only carry out important plausibility checks</li>\n</ul>\n<ul>\n<li>Checks for VC configuration only apply to active planning version (planning version 000)</li>\n</ul>\n<ul>\n<li>Correction of inconsistencies may require subsequent execution of CIF delta report. There is no link between this functionality and CIF delta report.</li>\n</ul>\n<p><strong>CDP and Block Planning</strong></p>\n<p>Concerning CDP, there are certain limitations regarding unspecified characteristics in planning and pegging; see SAP Note 526883.<br/><br/>Regarding block planning, see SAP Note 528189.</p>\n<p><strong>VC and Block Planning</strong></p>\n<p>If you want to use block planning in context of VC you have to consider that in the full integrated system the relevant class and characteristic data is not transferred automatically anymore to the relevant block planning tables like /SAPAPO/TMC01CL, /SAPAPO/TMC01CC.. This is a side effect of the missing CIF integration model for class and characteristic data, which is not relevant to maintain any longer. The direct integration can still be triggered in SAP S/4HANA 1610 when class and characteristic data was maintained in transaction CL01/CL02.</p>\n<p>To achieve this you must perform following steps:</p>\n<ul>\n<li>Run the transaction BF11 and create a new entry for the Application Indicator 'CACDP', set the flag 'Application Active' and enter 'Matching KlassensystemCACL/CDP' as the Text. Save the change.</li>\n<li>Run the transaction BF31 and create new entries as given below and save the changes:</li>\n</ul>\n<p>         Event / Application Indicator / Function Module           00004001 / CACDP / CLASS_CHARACT_BTE_4001<br/>         Event / Application Indicator / Function Module           00004002 / CACDP / CLASSIFICATION_BTE_4002</p>\n<p>The block planning class type 400 needs to be created directly/manually via a transaction CL01. An organizational area must be assigned and one block planning characteristic, with the same organizational area assigned.</p>\n<p><strong>Overlap and Continuous Input / Output</strong></p>\n<p><br/>Overlap due to continuous production (quantities are continuously handed over to the next processing step instead of only at the end) can be modeled in two ways:</p>\n<ul>\n<li>Within an order, by order internal relationships a start-start relationship can be defined</li>\n</ul>\n<ul>\n<li>Between orders, the functionality of continuous consumption can be used</li>\n</ul>\n<p>Other limitations:</p>\n<ul>\n<li>Note that continuous consumption is only considered in production planning heuristics and not in scheduling heuristics.</li>\n</ul>\n<ul>\n<li>Continuous consumption does not work with external procurement and sales orders.</li>\n</ul>\n<ul>\n<li>The heuristic SAP_PP_C001 must not be called by the heuristic SAP_PP_Q001.</li>\n</ul>\n<p>See further explanations in the Customizing where the heuristics are maintained and also SAP Note 448960.</p>\n<p><strong>Pegging with Delivery Tolerances and 'Use Total Supply'</strong></p>\n<p>This functionality should be used only after consultation with SAP. The functionalities \"delivery tolerances\", \"use entire receipt\" and \"use total stock\" (Under the product master, see 'Demand' tab) only lead to satisfying results in very simple cases. For example, the combination of partial delivery and delivery tolerance leads to incorrect results because the system cannot calculate the delivery tolerance correctly in case of a partial delivery. It is therefore necessary to contact SAP in order to evaluate if the mentioned functionalities can be used in the customer's scenario.</p>\n<p><strong>Accessibility</strong><br/><br/>The Resource Planning Table is not intended to handle or to be used for such business functions as:</p>\n<ul>\n<li>Planning of multiresources</li>\n</ul>\n<ul>\n<li>Planning of bucket resources</li>\n</ul>\n<ul>\n<li>Displaying network information (pegging, relationships)</li>\n</ul>\n<p><br/>The function can be called only from those transactions that call the screen 'Receipts View,' which is screen 1000 from the function group /SAPAPO/RRP_FRAMES. This includes the transactions:</p>\n<ul>\n<li>/SAPAPO/RRP4 - Receipts View</li>\n</ul>\n<ul>\n<li>/SAPAPO/RRP7 - Mass Conversion/Transfer of Planned Receipts in Active Planning Version</li>\n</ul>\n<ul>\n<li>/SAPAPO/CDPS0 - DPS Variable View and other related transactions</li>\n</ul>\n<ul>\n<li>/SAPAPO/PPT - Product Planning Table</li>\n</ul>\n<p><strong>Calculation of Capacity Requirements, Durations and Component Requirements</strong></p>\n<p>The calculation of capacity requirements, durations, and component requirements are calculated using the formula X = (Order quantity) * A + B. In contrast to SAP S/4HANA, other formulas are not supported in PP/DS for SAP S/4HANA.</p>\n<p><strong>Partial Delivery of Output Products</strong></p>\n<ul>\n<li>The full quantity of an in-house order is available at one discrete point in time, usually at the end of the order (with the exception of continuous input/output).</li>\n</ul>\n<ul>\n<li>A partial delivery of the output product is not supported in PP/DS for SAP S/4HANA.</li>\n</ul>\n<ul>\n<li>It is not recommended to split in-house orders in order to create a workaround for the partial delivery of the output products because this may lead to performance problems.</li>\n</ul>\n<p><strong>Characteristics-based Deployment in PP/DS </strong></p>\n<p>This feature is not supported.</p>\n<p><strong>Stretching and Shrinking of Operations (Process Industry Scenario)</strong></p>\n<p>Stretching and shrinking operations, either manual or using heuristic, is only applicable for process industry scenarios.</p>\n<p>When an operation is stretched or shrunk manually or using heuristic 'Fill gaps by changing operation duration' (SAP_PI_002) in the Detailed Scheduling Planning Board, operations are not scheduled automatically. To schedule the stretched or shrunk operations, the planner must subsequently execute the scheduling function.</p>\n<p><strong>Customer Consignment Stock</strong></p>\n<p>Customer consignment stocks are not planning-relevant. Planning assumes that these quantities are located at the customer and should therefore not be available for a requirement in the distributor's plant. Due to this reason, this is not relevant for PP/DS processes and is not available.</p>\n</div>\n<p><strong> </strong></p>", "noteVersion": 17, "refer_note": [{"note": "2732174", "noteTitle": "2732174 - SAP IBP OD 1905 - Release Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Restriction for using SAP Integrated Business Planning 1905.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IBP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note describes restrictions for the initial shipment of SAP IBP 1905 as well as for the support package stacks of SAP IBP 1905.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p class=\"longtext\">You have to consider those restrictions when using IBP 1905. This note will be updated regularly. Updated sections or bullet points are prefixed with an asterisk '*'.</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p class=\"longtext\"><strong>  </strong></p>\n</div>\n<p><strong>*IBP for Demand Driven Replenishment</strong></p>\n<ul>\n<li>*The ddmrp operators do not support effectivity/validity dates for sources of supply master data.</li>\n<li>*The ddmrp operators support ADU calculation for the customer-facing demand only. Then, they propagate and aggregate the customer-facing demand ADU to all sourcing locations. The current release does not support independent local ADU calculations at internal locations based on dependent demand.</li>\n<li>*The ddmrp operators do not support UOM conversions directly on the average usage input key figure.</li>\n</ul>\n<p>*Note: The following bullet points apply for the ECC Add-on for Demand Driven Replenishment.</p>\n<ul>\n<li>\n<p>*Only the planning segments for net and gross planning (PLAAB 02 and PLAAB 04) are supported by the DDMRP functionality. Other planning segments are planned by the standard MRP. The DDMRP heuristic does not support functionalities which are added due to the activation of business functions. The creation of specific order types is not supported for the planning with schedule lines.</p>\n</li>\n</ul>\n<ul>\n<li>*The DDMRP heuristic can only be executed for materials with MRP lot-sizing procedures with the following settings:</li>\n<ul>\n<li>The lot-sizing procedure in customizing is a static lot-sizing procedure</li>\n<li>The lot-size indicator is either “E” (lot-for-lot order quantity) or “F” (fixed lot size) </li>\n</ul>\n</ul>\n<ul>\n<li>\n<p>*The heuristic creates a pull element every time it is executed if the material is a decoupling point and an order recommendation is sent by IBP. When the heuristic is executed again before integration to IBP and back to ECC could have deleted a previous order recommendation in the Product Location table, the heuristic creates a new pull element even if a previously created pull order may already exist. This can happen in the following cases:</p>\n</li>\n</ul>\n</div>\n<div class=\"longtext\">\n<ul>\n<ul>\n<li>The previously created pull element is fixed manually or automatically (the setting for fixing pull elements is set to Fix elements in DLT).</li>\n<li>The previously created pull element is converted.</li>\n</ul>\n<li>*When executing DDMRP heuristic only with the Net Change flag, materials are not planned in the following cases:</li>\n<ul>\n<li>No Net Change flag from demand or master data is received.</li>\n<li>No planning file entry was written as demand elements did not change for the material.</li>\n</ul>\n</ul>\n</div>\n<div class=\"longtext\">\n<ul>\n<li>*The user exit EXIT_SAPLM61C_001 is only considered for non-DDMRP materials.</li>\n<li>*Customizing of scope of planning is not supported. You can define scope in the transaction /IBP/EDD_RUN_MRP.</li>\n<li>*Automated multi-level planning is currently not supported. All selected materials are planned on single-stage. You can define the scope of planning in the transaction /IBP/EDD_RUN_MRP.</li>\n<li>*Z-push/pull profiles are not preserved for planned orders in the following cases:</li>\n<ul>\n<li>When you change the planned orders with the change-order-BAPI.</li>\n<li>When you chance the planned orders with the transaction MD12.</li>\n<li>Furthermore, if there is any order-profile-specific customizing for scheduling, this is not considered for the newly created Z-push/pull orders.</li>\n</ul>\n<li>*Element list in the transaction /IBP/EDD_ELEM_LIST does not display safety time.</li>\n<li>*Purchase requisitions of type ZPLL must be converted to purchase orders before you can integrate data from SAP ECC to IBP.</li>\n</ul>\n<p class=\"longtext\"><strong>Analytics and Alerts</strong></p>\n</div>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<ul>\n<li>Analytics Advanced/Dashboards Advanced restrictions</li>\n<ul>\n<li>The Save As feature for charts can be triggered only on the main (top level) chart.</li>\n<li>Download to PDF/PNG/JPG isn’t available in the Analytics Advanced app.</li>\n<li>Repositioning of tiles on mobile devices is not supported in the Dashboards Advanced app.</li>\n<li>Group By as Series feature is supported with the following chart types: Bar, Column and Line, Combination, Dual Y-Axis Combination, Dual X-Axis Combination.</li>\n<li>When using the High Contrast Fiori themes, the available color palette is reduced.</li>\n</ul>\n<li>Custom Alerts restrictions</li>\n<ul>\n<li>Minimum Consecutive Period\" and “Period before and after” features work only exclusively in Define and Subscribe to Custom Alerts app.</li>\n<li>Custom alerts are not synonymous with key figure alerts defined in the IBP Excel Add-In and they are not merged together.</li>\n<li>When navigating to Excel from the Monitor Custom Alerts app, if multiple alerts are selected, all combinations of attributes between the alerts will be displayed within the Excel planning view.</li>\n</ul>\n<li>Manage cases restrictions</li>\n<ul>\n<li>Tasks cannot be created within Manage Cases app.</li>\n</ul>\n</ul>\n</div>\n<p class=\"longtext\"><strong>IBP Add-In for Microsoft Excel</strong></p>\n<div class=\"longtext\">\n<ul>\n<li>EPM Sheet Options and EPM Report Editor</li>\n<ul>\n<li>The EPM Sheet Option settings that are supported by the IBP add-in for Microsoft Excel can be found on the Edit View -&gt; Sheet Options dialog. Please also refer to the application help for more information <a href=\"https://help.sap.com/viewer/b28ffdd739bf45678ef36c44e64652d7/1711/en-US/9b35d663fd814544abd4e0caaa31537d.html\" target=\"_blank\">here</a></li>\n<li>The additional EPM settings that can be found under “Advanced à EPM Sheet Options” are generally not supported, except for the ones under “Protection” tab.</li>\n<li>Specifically, do not use the following “EPM Sheet Options” within the IBP add-in for Microsoft Excel:</li>\n<ul>\n<li>General -&gt; Member Recognition -&gt; “Activate Member Recognition”</li>\n<li>Refresh -&gt; Refresh Options -&gt; “Refresh Data in the Whole File When Opening it”</li>\n</ul>\n<li>The IBP add-in for Microsoft Excel has restricted EPM Report Editor functionality. In particular, do not use the “EPM Report Editor” functionality and settings except for performing tasks such as changing the position of the report in the grid, adjusting the shift, or creating local members:</li>\n<ul>\n<li>EPM Report Editor -&gt; Tab: Layout -&gt; changing the position of the report in the grid, adjusting the shift</li>\n<li>EPM Report Editor -&gt; Tab: Local Member -&gt; change and define local members</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Alert Key Figures</li>\n<ul>\n<li>Alert key figures that are defined to be calculated at the request level are not supported.</li>\n<li>Alert key figures do not work with the sheet option “Rows: Remove Empty and Zero Values” in dialog Edit View à Sheet Options.</li>\n<li>Sharing planning views as favorites with other users or user groups that include alert key figures does not result in alert notifications in the recipients' alerts dashboard in the IBP add-in for Microsoft Excel.</li>\n<li>The alert dashboard does not determine the number of alerts for planning views if the planning view has multiple time levels.</li>\n</ul>\n</ul>\n<ul>\n<li>SAP Jam Integration</li>\n<ul>\n<li>SAP Jam Feeds cannot be viewed within the IBP add-in for Microsoft Excel. For this, the Collaboration app on the SAP Fiori Launchpad for SAP Integrated Business Planning or SAP Jam itself needs to be used.</li>\n<li>SAP Jam Tasks that are in status “In Progress” cannot be reported as “Completed” from the IBP add-in for Microsoft Excel. Please also refer to the application help for more information <a href=\"https://help.sap.com/viewer/b28ffdd739bf45678ef36c44e64652d7/1711/en-US/5c480acdb086403a993b55d3116f637d.html\" target=\"_blank\">here</a></li>\n</ul>\n<li>Miscellaneous</li>\n<ul>\n<li>Changing the order of the columns and rows in the data and attribute area of IBP Excel Planning Views by using native Microsoft Excel capabilities like for example “Sorting from Largest to Smallest” or “Custom Sort” is not supported. The usage can lead to data inconsistencies. It is recommended to only use SAP EPM capabilities for these tasks.</li>\n<li>Permission filters are relevant for manual changes in the IBP add-in for Microsoft Excel only. The system disregards permission filters when the change is done by a planning operator, or version copy, and so on. This also applies to creating new planning objects either via the “Add New Planning Objects” or “Master Data Workbook” capabilities. Please also refer to the application help for more information: <a href=\"https://help.sap.com/viewer/b28ffdd739bf45678ef36c44e64652d7/1802/en-US/0efd417e860945e28103892e8f3f924b.html\" target=\"_blank\">here</a></li>\n<li>If you add a new planning object to a planning view that contains conversion attributes or conversion-enabled key figures, you must specify only one value for the conversion-from attribute, or include the conversion-from attribute with only one value in a filter. If you do not specify a value for the conversion-from attribute, or if you select multiple values, the application creates a new planning object for multiple conversion-from attribute values, which could lead to inconsistencies.</li>\n<li>A user's role needs to have read or write permission on at least one of the key figures of a scenario for that scenario to be visible in the IBP add-in for Microsoft Excel.</li>\n<li>Storage time profile level must be set to the lowest granularity level of the assigned time profile when a planning area is defined although the application would allow different settings.</li>\n<li>If you are using key figures with aggregation mode ”Custom”, constellations can occur where an offline report won’t work.</li>\n<li>The determination of the value of time-based totals (custom aggregates) does not work for key figures which are based on an L-code implementation or on a complex configuration and which have an aggregation mode other than Sum, Max, Min, or Avg.</li>\n<li>Flexible time axis &amp; attribute-based totals: Simulation capabilities are only supported within one time level<strong>. </strong>You cannot run simulation jobs for multiple time levels in parallel. <br/>Please also refer to the application help for more information<em>: </em><a href=\"https://help.sap.com/viewer/b28ffdd739bf45678ef36c44e64652d7/1711/en-US/e62621624be245bbb5279c7e0195d2c0.html\" target=\"_blank\">here</a><em> </em><em>&amp; </em><a href=\"https://help.sap.com/viewer/b28ffdd739bf45678ef36c44e64652d7/1711/en-US/ebfe7dfea7a24ae19ed943cdd2291d8c.html\" target=\"_blank\">here</a></li>\n<li>Attribute values must be loaded without trailing spaces via data integration. If loaded otherwise, changed key figure values could get reset to original values in the IBP add-in for Microsoft Excel, or error messages would appear.</li>\n<li>Implication using the Change-History-Based Calculations: Adding any of the following attributes to the IBP Excel Add.In planning view makes it a read-only planning view: Changed By (S_CHANGEDBY), Change ID (TSCHANGEIDFR), Change ID To (TSCHANGEIDTO), Changed in Period (S_CHINPERIODID), Changed in Period Level (S_CHINPERIODIDx), Data Sharing Plan (S_DSPID), Data Sharing Arrangement (S_DSAID, and Data Sharing Event (S_DSEVENTID). <br/>It’s not possible to edit cells, run simulations, and use scenarios in such a planning view. More information can be found <a href=\"https://help.sap.com/viewer/ff510e6429c542149a9d086d16c6601d/1802/en-US/de8b03675b53481298a23036c64e21c6.html\" target=\"_blank\">here</a>.</li>\n<li>Fixing of key figure values is not displayed in the ʽPlanning Objects with Key Figure Data’ view in master data workbooks. If you change fixed key figure values values, fixing is automatically deleted.</li>\n<li>Fixing changes are not tracked by change history.</li>\n<li>*It is not supported to create or display Planning Notes in a planning view that contain the attribute value (none) and a total for the same attribute.</li>\n<li>*When creating planning notes on those cells the key figure values saved for the planning note might be incorrect.</li>\n<li>*When displaying planning notes on those cells displayed Planning Notes might be incorrect.</li>\n</ul>\n</ul>\n</div>\n<p class=\"longtext\"><strong>Data Integration</strong></p>\n<div class=\"longtext\">\n<ul>\n<li>From the Web UI, you can load data one file at a time, but you cannot load multiple files as a single batch.</li>\n<li>While a data integration process is running, users or roles should not be maintained.</li>\n<li>Ensure that values for the attributes in IBP do not contain any of the following characters:\r\n<ul>\n<li>Single quotes/apostrophe (')</li>\n<li>Double quotes (\")</li>\n<li>Greater than symbol (&gt;)</li>\n<li>Less than symbol (&lt;)</li>\n<li>Carriage return (CR)</li>\n<li>Carriage return + line feed (CRLF)</li>\n<li>Empty line</li>\n<li>Tab</li>\n</ul>\n</li>\n</ul>\n</div>\n<p>Note: When data is loaded into IBP, it will allow quotes (“ “) in the Product Description when the description attribute is assigned to an attribute in Configuration.</p>\n<ul>\n<li>For order-based planning, periodic integration through OpenAPI is supported. The OpenAPI using SDI technology is released for usage with the SDI file adapter and ABAP adapter. Near-real time integration is not supported. Multiple sources can be integrated into individual planning areas. The integration of multiple sources into a common planning area is not supported.</li>\n<li>The following patches of the SDI Agent are not supported: HANA DP Agent 2.0 SP03 Patches 40 and 41.</li>\n<li>Key Figure data cannot be loaded for a planning level which has only time period attributes or no time period attribute.</li>\n<li>Attributes cannot have the same values spelled in different cases (upper, lower, title, mixed,etc). For example, the Product Family ‘Organic’ cannot be spelled as ‘ORGANIC’ for some product, ‘Organic’ for other products and ‘organic’ for yet another set of products. You should have one of the three values (‘Organic’,‘ORGANIC’ or ‘Organic’) consistently for all products that belong to the 'Organic' Product Family.</li>\n<li>When an entry is deleted from a master data type which has the following configuration\r\n<ul>\n<li>Master Data Type has multiple key fields,</li>\n<li>Each of the key field is a primary key of other master data type,</li>\n<li>An attribute of the master data type is marked as a mandatory field in the plan area then this entry in the characteristic value combination table for the plan area is not deleted.</li>\n</ul>\n</li>\n<li>In WEB UI downloading very large file reports (more than 60.000 records) is not possible</li>\n</ul>\n<p><strong>Configuration</strong></p>\n<ul>\n<li>Changing the type of an active master data type (simple, reference, compound, virtual, external) is not allowed.</li>\n<li>Decimal attributes cannot be assigned to a planning area. However, you can use decimal attributes as attributes as key figures.</li>\n<li>A decimal attribute cannot be key attribute in a compound master data type.</li>\n<li>Decimal attributes are not supported as time profile level attributes.</li>\n<li>For calculation expressions, the maximum number of supported input planning levels is 2. Use helper key figures to break down the calculation expressions if there are more than 2 input planning levels. For example, three inputs KF1(PL1,stored), KF2(PL2,stored), and KF3(PL3,stored) into a calculation are not supported, but inputs KF1(PL1,stored), KF2(PL1,calculated), and KF3(PL1,stored) are supported.</li>\n<li>If an attribute is assigned to a time profile level, it cannot be assigned to a planning area.</li>\n<li>Key figures in a disaggregation expressions must be stored key figures and must have the same base planning level as the key figure to be disaggregated.\r\n<ul>\n<li>If a key figure needs to be disaggregated based on a calculated key figure, use the Advance Simulation operator or the Copy Operator to copy the calculated values to a stored key figure that can be used in the disaggregation expression.</li>\n</ul>\n</li>\n<li>Concurrent model activation is not supported.</li>\n<li>The key figure display settings (decimals and display formats) are only applicable for analytics, while key figure data in the IBP Excel Add-In uses the formatting mechanisms of Excel and of the IBP Excel add-in</li>\n<li>Copy Replace for snapshots and attributes as key figures behaves same as Copy Merge. Users need to manually remove the unused configuration of snapshots and attributes as key figures from the target planning area .</li>\n<li>The calculation graph display for a key figure does not show a complete graph when one of the input key figures has an attribute transformation.</li>\n<li>Changing the definition of a snapshot key figure (changing the input key figures, the time period settings, or the number of snapshots) is not supported.</li>\n<li>A virtual master data type cannot be built on another virtual master data type.</li>\n<li>The length of an attribute</li>\n<ul>\n<li>cannot be made shortened once master data types or planning areas that use the attribue have been activated.</li>\n<li>must not be longer than 100 characters. Attributes which do not fit into this size have to be modelled as description attributes (e.g. product description as description attribute of product ID).</li>\n</ul>\n<li>When using scenarios, the assignment of TIMESTAMP attributes to a planning area might cause issues. It is recommended to assign NVARCHAR attributes to a planning area instead of TIMESTAMP attributes.</li>\n<li>Snapshots of key figure values won’t include time-independent key figures.</li>\n<li>Snapshots cannot be defined for a key figure that includes a helper key figure with attribute transformation.</li>\n<li>Analytics and disaggregation use the display setting for the number of decimal places for a key figure, which you make in the Configuration app. However, calculation scenarios do not necessarily return the value of a key figure rounded to the number of decimal places specified in the display settings for the key figure.</li>\n</ul>\n<p><strong>Change History</strong></p>\n<ul>\n<li>If the Change History, Planning Area Enabled checkbox is selected, and later the user decides to unselect it, the previously recorded change history of the planning area will be deleted upon the next activation of the planning area.</li>\n<li>Change History for Snapshot Key Figures is not supported.</li>\n<li>Change History for End User Scenarios is not supported.</li>\n<li>Change History is allowed for planning areas for IO operators. However, IO operators do not update the change history when run.</li>\n</ul>\n<ul></ul>\n<p><strong>IBP for inventory</strong></p>\n<ul>\n<li>The expected lost demand calculation does not support lead time variability, minimum and incremental batch sizes, or incoming backlog mean and standard deviation.</li>\n<li>IBP for inventory granularity is fixed to week. The time profile must include week.</li>\n<li>The inventory operators do not support effectivity/validity dates for sources of supply. </li>\n</ul>\n<p><strong>Supply Chain Network Visualization - deprecated</strong></p>\n<ul>\n<li><em>deprecated functionality: use <strong>Supply Chain Network Fiori Application</strong></em></li>\n<li>User is required and prompted to filter on at least one attribute (product id is preferred) to limit the size of the network displayed and to be meaningful.<br/>If selected filter is not a single product id, independent networks may overlap</li>\n<li>The network visualization graphical analytics does not support export files in any format.</li>\n<li>The following combinations of Group-By selection provides meaningful network representation and the user is prompted to select one of the combinations\r\n<ul>\n<li>Product, Source, Location, Component (Bill Of Material view)</li>\n<li>Product, Location, Ship From Location, Source, Customer Group  (Distribution view)</li>\n<li>Product, Ship-From Location, Location, Customer Group, Component, Source (Product network view)</li>\n</ul>\n</li>\n<li>User roles other than ALL_INCLUSIVE are currently not supported</li>\n<li>Because the Supply Chain Network Fiori app visualizes supply networks, it works only with planning areas used by the following modules:</li>\n<ul>\n<li>IBP for sale and operations</li>\n<li>IBP for supply</li>\n<li>IBP for inventory</li>\n<li>The Unified Planning Area is supported</li>\n</ul>\n</ul>\n<p><strong>Supply Chain Network Fiori Application<br/></strong></p>\n<ul>\n<li>Because the Supply Chain Network Fiori app visualizes supply networks, it works only with planning areas used by the following modules:</li>\n<li>IBP for sale and operations</li>\n<li>IBP for supply</li>\n<li>IBP for inventory</li>\n<li>IBP for response</li>\n<li>The Unified Planning Area is supported</li>\n</ul>\n<p><strong>IBP for Demand</strong></p>\n<ul>\n<li>Demand Sensing is only running on a fixed planning level with fixed attributes:<br/>Attributes to be used for Demand Sensing: PRDID, LOCID, CUSTID</li>\n<li>Demand Sensing can be run only on Baseline Version</li>\n<li>Demand Sensing does not consider phasing out of products.</li>\n<li>The data sources which are used in the APO-DP to IBP integration do not support a delta-load. Hence it is not possible to load changed data only. If a frequent data transfer of parts of the planning area data is required, we recommend to define separate HCI tasks with respective filter criteria.</li>\n<li>The APO-DP to IBP integration does not cover the deletion or realignment of characteristics value combinations. This means that if a characteristics value combination was transferred to IPB and is afterwards deleted in APO DP (either directly or by realignment) the respective planning object will not be automatically deleted in IBP</li>\n<li>Due to performance reasons it is not recommended to add more than 150 forecasting steps to a single forecast model.</li>\n</ul>\n<p><strong>Data Disaggregation</strong></p>\n<ul>\n<li>Rounding issues may occur in some cases in interactive disaggregation (simulation or interactive planning operator execution). In cases where rounding is supported, you cannot control the 'rest distribution'.</li>\n<li>When executing the IBPFORECAST or DISAGG planning operator in batch mode (mass disaggregation) with a key figure 'A' containing a unit of measure (UOM) conversion, all root attributes of the conversion key figure 'B', exept for the (UOM) conversion target attribute must be contained as attribute in the base planning level of key figure 'A'.</li>\n</ul>\n<p>Example:</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"211\">\n<p><strong>Attributes of Key Figure 'A'</strong></p>\n</td>\n<td valign=\"top\" width=\"282\">\n<p><strong>Root Attributes of Conversion Key Figure 'B'</strong></p>\n</td>\n<td valign=\"top\" width=\"87\">\n<p><strong>Supported</strong></p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"5\" valign=\"top\" width=\"211\">\n<p>PRDID, LOCID, Technical Week</p>\n</td>\n<td valign=\"top\" width=\"282\">\n<p>UOMTOID, PRDID, Technical Week</p>\n</td>\n<td valign=\"top\" width=\"87\">\n<p>yes</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"282\">\n<p>UOMTOID, PRDID</p>\n</td>\n<td valign=\"top\" width=\"87\">\n<p>yes</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"282\">\n<p>UOMTOID, PRDID, Day</p>\n</td>\n<td valign=\"top\" width=\"87\">\n<p>no</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"282\">\n<p>UOMTOID, PRDID, Calendar Week</p>\n</td>\n<td valign=\"top\" width=\"87\">\n<p>no</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"282\">\n<p>UOMTOID, PRDID, CUSTID</p>\n</td>\n<td valign=\"top\" width=\"87\">\n<p>no</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>If you use time disaggregation on key figure upload, dependent key figures have to be loaded sequentially in separate batches.</li>\n<li>Key figures used in a disaggregation expression must be stored on the same planning level. If the reference key figure is calculated and stored, the stored value will be used in the disaggregation expression.</li>\n<li>Key figures that have an external key figure quantity assigned to them (in the 'External Key Figure Qty' field in the Configuration app), can't be used in the DISAGG operator, neither as source key figure nor as target key figure. Such key figures can't be used in disaggregation expressions, either.</li>\n</ul>\n<p><strong>Time Disaggregation of Key Figure Values to Non-Editable Periods</strong></p>\n<ul>\n<li>In general, disaggregation does not consider key figure editability settings in configuration. This means that disaggregation may also change periods that are not editable.<strong><br/></strong></li>\n<li>If a key figure value needs to be disaggregated by time in the IBP Excel add-in and at least one of the time periods on a detailed level is not editable, the change will be prevented by the system. For example, if a key figure is defined as editable for current and future periods and its values are stored on the level of technical week, the system will allow changes on the monthly level only if the current date is in the first calendar week of the current month.</li>\n</ul>\n<p><strong>Order-based Planning</strong></p>\n<ul>\n<li>It is not supported to create and delete version specific master data for external master data types via the excel client</li>\n<li>The process integration with Production Planning and Detailed Scheduling (PP/DS) in SAP SCM and in SAP S/4HANA is not supported.</li>\n<li>Advanced process order functionality is not supported in order-based planning. Further details can be found in the SAP note <a href=\"/notes/2680725\" target=\"_blank\">2680725</a>.</li>\n<li>Multiple Planning Areas can be used in order-based planning, but only the operative Planning Area(s) are directly integrated</li>\n<li>*Detailed restrictions for the App Rules for Demand Prioritization be can be found in the SAP note <a href=\"/notes/2338641\" target=\"_blank\">2338641</a> - Rules for Demand Prioritization</li>\n</ul>\n<p>     Further details on restrictions can be can be found in the SAP note <a href=\"/notes/2640432\" target=\"_blank\">2640432</a> - Details on Restrictions for SAP IBP Order-Based Planning</p>\n<p><strong>Unified Planning Area</strong></p>\n<ul>\n<li>There is additional information on Partial Copy functionality. Refer to note <a href=\"/notes/2408579\" target=\"_blank\">2408579</a>.</li>\n<li>SAPIBP1 does not support Means of Transport, Demand Category and integration to SAP Ariba. For SAP Ariba, the unified planning process utilizes SAP7.</li>\n</ul>\n<p><strong>Manage Product Lifecycle application</strong></p>\n<ul type=\"disc\">\n<li>Product references are only taken into account by forecasting if the forecast level contains the product e.g. if forecast is done on product group level, no product references can be taken into account</li>\n</ul>\n<p class=\"longtext\"><strong>Time-series based Planning (IBP for Sales &amp; Operations Planning)</strong></p>\n<ul>\n<li>Time-series based supply planning heuristic: Usage of key figure Inventory Target and the lot sizes “minimum lot size” and “rounded value” lead in most cases to suboptimal results. The heuristic computes too much transport or production receipts and hence too high projected inventory. Existing stock on-hands are not taken into account appropriately. </li>\n</ul>\n<p> </p>\n</div>", "noteVersion": 2}]}, {"note": "2674488", "noteTitle": "2674488 - Release Information Note: Production Planning and Detailed Scheduling for SAP S/4HANA 1809", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional release information for Production Planning and Detailed Scheduling as part of:</p>\n<ul>\n<li>Product version SAP S/4HANA ON-PREMISE 1809</li>\n<li>Software component S4CORE - Release 103</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PP/DS</p>\n<p>Production Planning and Detailed Scheduling</p>\n<p>PPDS</p>\n<p>PP/DS for SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To be able to use Production Planning and Detailed Scheduling for SAP S/4HANA, you must activate <em>Advanced Planning and Scheduling</em> in the back-end system. In Customizing, choose<em> Advanced Planning -&gt; Basic Settings -&gt; Activate Advanced Planning and Scheduling.</em></p>\n<p>The features activated by the <em>Advanced Planning and Scheduling</em> switch are not part of the standard SAP S/4HANA Enterprise license. A dedicated license, <em>Manufacturing for Planning and Scheduling</em> is required if this switch is set to <em>Active</em><em>.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Customer Code Takeover:</p>\n<p>PP/DS customer code needs to be adapted to work with Production Planning and Detailed Scheduling for SAP S/4HANA.</p>\n<p>Simplification List:</p>\n<p>The Simplification List for Production Planning and Detailed Scheduling (PP/DS) for SAP S/4HANA consists of simplification items, with a focus on what has to be considered by customers in an implementation or migration project from the PP/DS component from SCM Server to PP/DS for SAP S/4HANA, on-premise edition 1809 can be found in note 2647972.</p>\n<p>The SAP S/4HANA Transition Worklist item 'S4TWL - Performance optimizations for publication of planning order'applies, if you are using Production Planning and Detailed Scheduling (PP/DS) during an upgrade from S/4HANA 1610 and S/4HANA 1709. For more details refer note 2643483.</p>\n<p>Fiori Apps and Visually Harmonized Transactions:</p>\n<p>The details of the Fiori apps for PP/DS can be found in SAP Note 2674523. The details for supported Fiori apps and Visually Harmonized transactions can be found in the SAP Fiori apps reference library.</p>\n<p>Restrictions and Implementation Recommendations:</p>\n<p>The Restrictions and Implementation recommendations that needs to be considered while implementing PP/DS for SAP S/4HANA, on-premise edition 1809 can be found in note 2666947.</p>\n<p>Intergrated LiveCache:</p>\n<p>Information on installation and version on live cache can be found in following notes</p>\n<p>2074788 - Patch strategy for SAP HANA-integrated liveCaches</p>\n<p>2074843 - Version Matrix for HANA integrated liveCaches</p>\n<p>2037585 - Upgrade of SAP-HANA-integrated liveCaches</p>\n<p><strong>List of important notes released after 1809 FPS0 </strong></p>\n<div class=\"myNNFV2-container\">\n<div class=\"myNNFV2-table-responsive\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"myNNFV2-table\">\n<tbody>\n<tr>\n<td>Note</td>\n<td>Description</td>\n</tr>\n<tr>\n<td>2670338</td>\n<td>PP/DSonS/4HANA: Manufacturing Order not getting reconciled</td>\n</tr>\n<tr>\n<td>2620835</td>\n<td>PP/DS ON s/4 HANA: Materials with error are empty in MRP Live</td>\n</tr>\n<tr>\n<td>2609363</td>\n<td>PP/DS on S/4 HANA: BAdi methods for order creation / updation in Simplified Publication</td>\n</tr>\n<tr>\n<td>2530660</td>\n<td>PPDSonS/4: Syntax errors in Intial order transfer report</td>\n</tr>\n<tr>\n<td>2519859</td>\n<td>PP/DS on S/4 HANA: Syntax changes for MRP Live</td>\n</tr>\n<tr>\n<td>2513875</td>\n<td>PP/DSforS/4HANA: MRP PERFORMANCE IMPROVEMENT</td>\n</tr>\n<tr>\n<td>2392486</td>\n<td>Buffer concept for reading the advanced planning flag for material, plant combinations</td>\n</tr>\n<tr>\n<td>2773936</td>\n<td>Purchase requisition is not transported from S/4 to PP/DS</td>\n</tr>\n<tr>\n<td>2718631</td>\n<td>Planned order deletion correction and memory consumption improvement during MRP live</td>\n</tr>\n<tr>\n<td>2728245</td>\n<td>PPDS on S4; /SAPAPO/C5: In case of error all objects of one block not updated at S4</td>\n</tr>\n</tbody>\n</table></div>\n</div>\n</div>\n<p><strong> </strong></p>", "noteVersion": 2}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Review available alternatives for Graphical Planning Table and decide about future strategy."}, {"Activity": "Fiori Implementation", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "If decided, implement Fiori apps as replacement of existing transactions."}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "Train new applications."}, {"Activity": "Implementation project required", "Phase": "After conversion project", "Condition": "Optional", "Additional_Information": "If decided, implement either embedded PPDS or REO as alternative."}]}