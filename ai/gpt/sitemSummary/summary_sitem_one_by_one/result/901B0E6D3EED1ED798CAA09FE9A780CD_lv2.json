{"guid": "901B0E6D3EED1ED798CAA09FE9A780CD", "sitemId": "SI19: CT_MRSS", "sitemTitle": "S4TWL - MRS Enhancements for Material Master", "note": 2493452, "noteTitle": "2493452 - MRS enhancements to Material master", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP Multiresource Scheduling 1.0 for SAP S/4HANA does not support the material master enhancement for requirements profiles.</p>\n<p>If you have used this functionality in previous SAP Multiresource Scheduling releases and would like to store the data before they might be lost during SAP Multiresource Scheduling 1.0 for SAP S/4HANA installation, please execute the steps describe in the manual activities attached to this note.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Master Check, S/4 System Conversion, Material Master, MRS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Not supported functionality in SAP Multiresource Scheduling 1.0 for SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Follow the steps describe in the manual activity.</p>", "noteVersion": 1, "refer_note": [{"note": "2488679", "noteTitle": "2488679 - S4TC MRSS Master Check for S/4 System Conversion Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Implementation of MRSS check class for S/4 transformation checks as described in SAP note 2182725.</p>\n<p>Implementation of MRSS check class for S/4 transformation checks as described in SAP note 2399707.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, Master Check, S/4 System Conversion, MRSS, CA-MRS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion checks  for S/4 transformation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\"> Implement the technical prerequisites for the S/4 transformation checks via this note.</p>", "noteVersion": 2, "refer_note": [{"note": "2493452", "noteTitle": "2493452 - MRS enhancements to Material master", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP Multiresource Scheduling 1.0 for SAP S/4HANA does not support the material master enhancement for requirements profiles.</p>\n<p>If you have used this functionality in previous SAP Multiresource Scheduling releases and would like to store the data before they might be lost during SAP Multiresource Scheduling 1.0 for SAP S/4HANA installation, please execute the steps describe in the manual activities attached to this note.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Master Check, S/4 System Conversion, Material Master, MRS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Not supported functionality in SAP Multiresource Scheduling 1.0 for SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Follow the steps describe in the manual activity.</p>", "noteVersion": 1}]}, {"note": "1138507", "noteTitle": "1138507 - Integrate MRS Qualifications with PM/CS, MM and Equipments", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Integrate the MRS qualifications and profiles with PM/CS, MM and Equipment master modules</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MRSS, Qualifications, Profiles, PM, CS, Material master, Equipment master</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Integrate the MRS qualifications and profiles with PM/CS, MM and Equipment master modules.</p>\n<p><strong>If you are in EHP8 SP11 version or if you have implemented SAP Note 2637577:</strong></p>\n<p>You should also implement SAP Note 2745163. Otherwise you may get a syntax error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please download the latest version of this note before proceeding.<br/><br/>Follow the below mentioned manual steps and then apply the automatic corrections.<br/><br/><br/><br/><strong>1. Enhancements to PM/CS</strong><br/><br/><strong>Steps 1.1 and 1.2 are not valid for MRS 8.0 upwards</strong><br/><br/>1.1 Create a customer include CI_AUFK (if not existing already) with a dummy field DUMMY_AUFK and append structure /MRSS/T_ORDER_H to it.<br/><br/>1.2 Create a customer include CI_AFVU (if not existing already) with a dummy field DUMMY_AFVU and append structure /MRSS/T_OPERATION_H to it.<br/><br/>1.3 The screen sequence for program SAPLCOIH has to be customized. Launch the transaction VFBS. Enter program name as SAPLCOIH.<br/><br/><span>a) Function Codes</span><br/><br/>Select the radio button \"Function codes for T185F'. Go to change mode and add the following entries:<br/>-&gt; Maintain 4 new entries with FCode : ZUS1, ZUS2, ZUS3, ZUS4.<br/>Refer to the screen shot 1 (in the attached word document) to see    the example entries.<br/><br/><span>b) Paths between process locations</span><br/><br/>Select the radio button \"Paths between process locations for T185'. Go to change mode and add the following entries:<br/>-&gt; Maintain 4 new entries with FCode : ZUS1, ZUS2, ZUS3, ZUS4.<br/>Maintain the values using the following parameter values: Fcode = 'ENT1' and Source of Parameters = 'A'.<br/>Refer to the screen shot 2 (in the attached word document) to see    the example entries.<br/><br/>1.4. For customers who are in EHP5 or above (only), kindly implement the following manual steps before implementing the automatic corrections.</p>\n<ul>\n<li>Run the transaction SE80</li>\n</ul>\n<ul>\n<li>Enter the program name as SAPLCOIH</li>\n</ul>\n<ul>\n<li>Expand the 'Screens' object on the left and select the screen 1280</li>\n</ul>\n<ul>\n<li>Go to change mode</li>\n</ul>\n<ul>\n<li>Go to the Attributes tab of the screen</li>\n</ul>\n<ul>\n<li>Here, under the 'Other attributes' change the lines/columns maintained to 30 (lines) and 166 (columns)</li>\n</ul>\n<ul>\n<li>Save the entry</li>\n</ul>\n<ul>\n<li>Now click on the 'Layout' option</li>\n</ul>\n<ul>\n<li>The screen painter will now open</li>\n</ul>\n<ul>\n<li>Go to the change mode</li>\n</ul>\n<ul>\n<li>Select the subscreen area EAML_LFE (the big subscreen on the right) by double clicking on it or using the select block button</li>\n</ul>\n<ul>\n<li>The selected block will now be highlighted</li>\n</ul>\n<ul>\n<li>Place the cursor on the first cell available on the 9th line of the same block</li>\n</ul>\n<ul>\n<li>From the menu, select the option Edit -&gt; Move Subscreen</li>\n</ul>\n<ul>\n<li>Or directly move this subscreen down such that it starts from the 9th line of the same block</li>\n</ul>\n<ul>\n<li>A EAML_LFE subscreen will be moved 10 lines below from its original position</li>\n</ul>\n<ul>\n<li>Save and activate the changes</li>\n</ul>\n<p>1.5. Now apply the automatic corrections.</p>\n<p><strong>2. Check</strong><br/>Run SE80<br/>Navigate to function group \"COIH \"                                     Double click on COIH, the pop-up 'Display Function Group' is triggered. Navigate to Main program and search for 'INCLUDE LCOIHFO4.'.<br/>Now check if the statment 'INCLUDE /MRSS/RAC_MOD_PROFILES.' has been inserted below the above mentioned code. If it is not found, manually insert the following code:<br/>*                                   *<br/>* MRS 610: Note 1138507<br/>INCLUDE /MRSS/RAC_MOD_PROFILES.<br/>*                                   *</p>\n<p><br/><strong>3. Enhancements to Material master</strong></p>\n<p><strong>Steps 3 is not valid for MRS 1.0 for SAP S/4HANA</strong></p>\n<p><strong>Note: SAP Multiresource Scheduling 1.0 for SAP S/4HANA does not support the material master enhancement for requirements profiles. If you have used this functionality in previous SAP Multiresource Scheduling releases, you must copy the values for fields in the /MRSS/* namespace from table MARC to a custom table. You must do so before the conversion to SAP Multiresource Scheduling 1.0 for SAP S/4HANA, otherwise your data might be lost.</strong><br/><br/>To show the created customer subscreens in the transactions<br/>MM01 / MM02 / MM03, there is some customizing necessary:<br/><br/><span>a) Creation of screen sequences</span><br/><br/>Follow the path in SPRO transaction:<br/>Logistics - General - Material Master - Configuring the Material        Master - Define Structure of Data Screens for Each Screen Sequence<br/>(Transaction OMT3B).<br/>Copy screen sequence e.g. 21 .. Z1<br/>Refer to the screen shots 3-5 (in the attached word document) to see    the example entries.<br/>Here in the Fig. 5 of the attached document, enter the Program name     for MRS as /MRSS/SAPLRAC_INTERFACE.<br/><br/><span>b) Customizing of screen order</span><br/><br/>Follow the path in SPRO transaction:<br/>Logistics - General - Material Master - Configuring the Material        Master - Maintain Order of Main and Additional Screens.<br/>(Transaction OMT3R)<br/>-&gt; the new will be automatic the last one<br/>Refer to the screen shots 6-7 (in the attached word document) to see    the example entries.<br/><br/><span>c) Assign screen sequences to user/material type/transaction/industry sector. </span><br/><br/>Use the following path :<br/>Logistics - General - Material Master - Configuring the Material        Master - Assign Screen Sequences to User / Material / Type /            Transaction / Industry Sector<br/>(Transaction OMT3E)<br/>- e.g. 21-&gt;Z1<br/>Refer to screen shot 8 (in the attached word document) to see the example entries.<br/><br/><span>d) Creating entries in table V_T133D.</span><br/><br/>Run the transaction SM30.<br/>Enter the view name as V_T133D and click on the 'maintain' button<br/><br/>Create 3 entries with code PB97, PB98 and PB99 respectively where:<br/><br/>Screen sequence number (SSq) is same as that maintained in transaction OMT3B for the MRS Enhancements for all 3 entries.<br/><br/>Logical screen (SScrn) is again same as the logical data screen maintained for the MRS Enhancements sequence number in OMT3B transaction for all 3 entries.<br/><br/>Processing Routine type (Typ) = I for all 3 entries and<br/><br/>Process Same screen indicator (P) is checked for all 3 entries<br/><br/>Save the table.<br/><br/>You may refer to the example screenshot present in the attachment 'V_T133D_entries'</p>\n<p><br/><strong>4. Enhancements to Equipment master</strong><br/><br/><strong>Steps 4.1 and 4.2 are not valid for 800 and above releases</strong><br/><br/>4.1 Create a customer include CI_EQUI (if not existing already) with a dummy field DUMMY_EQUI and append structure /MRSS/T_EQUI to it.<br/><br/>4.2 Create a customer include CI_EQUI_U (if not existing already) with a dummy field DUMMY_EQUI_U and append structure /MRSS/T_EQUI_U to it.<br/><br/>4.3 User exit enhancement ITOB0001<br/>From transaction CMOD create a new project ZMRSS_CS and use the enhancement ITOB0001.<br/><br/><span>a) Implement call of sub-screens ZMRSS_SUBSCREEN_PROF with Dynpro 1000</span><br/><br/>The screen type will be subscreen.<br/>From the graphical editor create a subscreen area and name it as ZMRSS_SUBSCREEN_PROF.<br/><br/>Copy the below logic into the Flow logic of screen.<br/><br/>PROCESS BEFORE OUTPUT.<br/>** MODULE STATUS_1000.<br/>** Begin MRSS extension:<br/>** call subscreen with user data<br/>  CALL SUBSCREEN zmrss_subscreen_prof<br/>       INCLUDING '/MRSS/SAPLRAC_INTERFACE' '2000'.<br/>** End MRSS extension<br/><br/>PROCESS AFTER INPUT.<br/>** MODULE USER_COMMAND_1000.<br/>** Begin MRSS extension:<br/>** call subscreen with user data<br/>  CALL SUBSCREEN zmrss_subscreen_prof.<br/>** End MRSS extension<br/><br/><span>b) Implement the function modules EXIT_SAPLITO0_001 and EXIT_SAPLITO0_002 via the include files</span><br/><br/>Function exit EXIT_SAPLITO0_001 is implemented via include ZXTOBU01<br/>** Begin MRSS extension:<br/>** call function module to transport data to subscreen<br/>  CALL FUNCTION '/MRSS/RAC_ACT_DISPLAY_DATA'<br/>       EXPORTING<br/>*           types of technical obj in PM: 02= Equipments<br/>            I_OBJECT_TYPE      = I_OBJECT_TYPE<br/>*           01=anlegen; 02=ändern; 03=anzeigen<br/>            I_ACTIVITY_TYPE    = I_ACTIVITY_TYPE<br/>            I_DELETION_FLAG    = I_DELETION_FLAG<br/>            I_ACTIVE_FCODE      = I_ACTIVE_FCODE<br/>            I_DATA_EQUI        = I_DATA_EQUI<br/>            I_DATA_EQKT        = I_DATA_EQKT<br/>            I_DATA_EQUZ        = I_DATA_EQUZ<br/>            I_DATA_ILOA        = I_DATA_ILOA<br/>            I_DATA_IFLO        = I_DATA_IFLO<br/>            I_DATA_FLEET        = I_DATA_FLEET<br/>      IMPORTING<br/>            E_SUBSCREEN_NUMBER  = E_SUBSCREEN_NUMBER.<br/>** End MRSS extension<br/><br/>Function exit EXIT_SAPLITO0_002 is implemented via include ZXTOBU02<br/>** Begin MRSS extension:<br/>** call function module to transport data to subscreen<br/>  CALL FUNCTION '/MRSS/RAC_ACT_CHANGE_DATA'<br/>       IMPORTING<br/>        E_UPDATE_DATA_EQ          = E_UPDATE_DATA_EQ<br/>        E_UPDATE_FLAGS_EQ         = E_UPDATE_FLAGS_EQ<br/>        E_UPDATE_DATA_EZ          = E_UPDATE_DATA_EZ<br/>        E_UPDATE_FLAGS_EZ         = E_UPDATE_FLAGS_EZ<br/>        E_UPDATE_DATA_IFLO        = E_UPDATE_DATA_IFLO<br/>        E_UPDATE_FLAGS_IFLO       = E_UPDATE_FLAGS_IFLO.<br/>** End MRSS extension<br/><br/>4.4 The new function codes in order to call the maintenance<br/>(create / change / display / automatic determination) of the requirement profile must have the name: ZZITO_xx with x = 01, 02, 03 or 04.<br/><br/>4.4.1 Use transaction VFBS in order to maintain the subsequent screen control.<br/><br/>Launch the transaction VFBS. Give program name as SAPMIEQ0 and select the radio button \"Function codes for T185F'. Go to change mode and add following entries.<br/>-&gt; Maintain 3 new entries with FCode : ZZITO_01, ZZITO_02, ZZITO_03.<br/>-&gt; For all 3 entries maintain AcCat = 'X'.<br/>-&gt; Leave all other values as default and save.<br/><br/>4.5 Via Customizing the (new) tabstrip has to be activated:<br/>Transaction SPRO : Plant Maintenance and Customer Service - Master Data in Plant Maintenance and Customer Service - Technical Objects -&gt; General Data - Set View Profiles for Technical Objects.<br/><br/>Here for the screen group H2(Screen group equipment data), navigate to \"Activity and Layout views\" and make sure that the tab No. 120<br/>( Other ) is maintained and active. This tab contains the MRS data.</p></div>", "noteVersion": 43, "refer_note": [{"note": "2637577", "noteTitle": "2637577 - Enable screens from different program for Technical Object View profile", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are not able to use a subscreen definition in the Set view profile for Technical Objects IMG activity, which is implemented outside the SAPLITO0 program. The subscreen number entered into maintenance view V_TITOBSUB is called in the context of SAPLITO0 and the given program name is not taken into consideration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Set View Profile for Technical Objects, SAPLITO0, V_TITOBSUB, V_TITOBSUB_EQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the attached automatic correction instructions</p>", "noteVersion": 2}, {"note": "2745163", "noteTitle": "2745163 - Run time error when MRS requirement profiles are maintained in equipment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In ECC EHP8 SPS11, if you are using requirements profile for equipment, you might get a 'type mismatch' error.</p>\n<p>This error is observed if you have implemented both notes 2637577 and 1138507.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>/MRSS/RAC_PAI_PROFILE_KEY, ITOB_DATA_EXPORT, CALL_FUNCTION_CONFLICT_TAB_TYP, Requirements Profile, ito0t_tabstrip_rec</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This error occurs only in SPs above SP11 of EHP8 as one of the structure is changed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement correction instructions.</p>\n<p>The SAP Note can be implemented for lower SPs of EHP8 as well.</p>", "noteVersion": 4}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "delete /MRSS/ appends in MARC before entering SUM realize phase"}]}