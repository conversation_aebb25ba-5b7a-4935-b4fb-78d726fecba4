{"guid": "6CAE8B3EA08B1ED6A5D5C60CCB3DE0C1", "sitemId": "SI35: Logistics_General", "sitemTitle": "S4TWL - Retail iViews", "note": 2370183, "noteTitle": "2370183 - S4TWL - Retail Buying iViews", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>An iView (integrated view) was a logical portal content building block representing a visual application or part thereof. There were some iViews to support retail buying.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, retail buying iViews are not available anymore. Transactions WRBDL, WRBDL2 are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In SAP S/4HANA there are alternatives to display the information.<br/>For stock information there is transaction RWBE or the Fiori apps for inventory management.<br/>For open purchase orders there are transaction ME2L, ME2M, ME2N or the purchase order Fiori app.</p>\n<p>In case you reuse ABAP objects of packet WRB or WRBA in your custome code, please see attached note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if iViews for retail buying are used. <br/>This is can be observed if functions modules WRB_RFC_GET_OPEN_PO_OVERVIEW, WRB_RFC_GET_VENDOR_OPEN_PO, WRB_RFC_GET_OOS_OVERVIEW, WRB_RFC_VENDOR_GET_OUTOFSTOCKS, WRB_RFC_VENDOR_GET_CONTACTDATA are called, or transactions WRBDL, WRBDL2, or report RWRBADSL is executed</p>", "noteVersion": 7, "refer_note": [{"note": "3038888", "noteTitle": "3038888 - Transactions WRBDL and WRBDL2 incorrectly available as tiles, and in catalogs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You cannot execute the mentioned transactions, and potentially get a short-dump.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>WRBDL, WRBDL2, Detailed Stock Data, Detailed Stock Data by Sites, displayDetailledStock, displayDetailledStockForSiteList</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Transactions WRBDL and WRBDL2 where used in iViews for Retail Buying. These iViews are not available in S/4HANA anymore, see note 2370183 - S4TWL - Retail Buying iViews.</p>\n<p>Application Descriptors, Tiles and the assignment to technical and business catalogs were created by mistake.</p>\n<p>Tile and app descriptor information:</p>\n<p>Transaction: WRBDL<br/>Semantic Object: InventoryReport<br/>Action: displayDetailledStock<br/>Title: Display Detailed Stock Data<br/>Tile Subtitle: Order Optimizing</p>\n<p>Transaction: WRBDL2<br/>Semantic Object: InventoryReport<br/>Action: displayDetailledStockForSiteList<br/>Title: Display Detailed Stock Data by Sites<br/>Tile Subtitle: Order Optimizing</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The error will be corrected in future releases, feature packs, and support packs.</p>", "noteVersion": 1, "refer_note": [{"note": "2370183", "noteTitle": "2370183 - S4TWL - Retail Buying iViews", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>An iView (integrated view) was a logical portal content building block representing a visual application or part thereof. There were some iViews to support retail buying.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, retail buying iViews are not available anymore. Transactions WRBDL, WRBDL2 are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In SAP S/4HANA there are alternatives to display the information.<br/>For stock information there is transaction RWBE or the Fiori apps for inventory management.<br/>For open purchase orders there are transaction ME2L, ME2M, ME2N or the purchase order Fiori app.</p>\n<p>In case you reuse ABAP objects of packet WRB or WRBA in your custome code, please see attached note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if iViews for retail buying are used. <br/>This is can be observed if functions modules WRB_RFC_GET_OPEN_PO_OVERVIEW, WRB_RFC_GET_VENDOR_OPEN_PO, WRB_RFC_GET_OOS_OVERVIEW, WRB_RFC_VENDOR_GET_OUTOFSTOCKS, WRB_RFC_VENDOR_GET_CONTACTDATA are called, or transactions WRBDL, WRBDL2, or report RWRBADSL is executed</p>", "noteVersion": 7}]}, {"note": "2383533", "noteTitle": "2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related business applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management some applications and the respective development objects are not available anymore. <br/>This might be relevant for customer specific coding. If customer specific coding re-used those development objects in ERP, the coding needs to be adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adjust customer specific coding accordingly.</p>", "noteVersion": 2}], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users must be trained to use the available alternatives like Fiori apps to display the requried informantion in S4/HANA."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "In case ABAP objects of packet WRB or WRBA are reused in custom code, custom code adaption will be requried."}]}