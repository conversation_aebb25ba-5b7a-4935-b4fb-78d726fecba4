{"guid": "0050569455E21ED5B3E176783915009E", "sitemId": "SI2: Logistics_PP-MRP", "sitemTitle": "S4TWL - Storage Location MRP", "note": 2268045, "noteTitle": "2268045 - S4TWL - Storage Location MRP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>Master data maintenance has been simplified and offers the following benefits:</p>\n<ul>\n<li>Reduction of modeling alternatives for similar function of the MRP planning on storage location level</li>\n<li>Support of a single modeling approach that provides the most sophisticated features and funtions for planning on storage location level</li>\n</ul>\n<p><strong>Description</strong></p>\n<p><span>Context</span></p>\n<p>In SAP ERP, storage locations can be excluded from MRP planning or they can be planned separately from other storage locations. In the latter case a reorder point procedure with maximum lot size is used which triggers transport reservations to fill up the storage location.</p>\n<p>MRP areas cover the very same business requirements. Materials with MRP-area-specific MRP type 'ND' (no MRP) can be used instead of materials with a storage location excluded from MRP. Materials with MRP-area-specific MRP type 'VB' (reorder point planning) can be used instead of materials with separately planned storage locations. The storage location functionality is a subset of the MRP areas capabilities.</p>\n<p><span>Description of Simplification</span></p>\n<p>The S/4HANA MRP only plans on plant and MRP area level. Planning on storage location level is not available in SAP S/4HANA, on-premise edition 1511.</p>\n<p><span>Reason</span></p>\n<p>The reason for this change are:</p>\n<ul>\n<li>The MRP logic is simplified. There is only one solution for planning on storage location level</li>\n<li>The MRP area logic is more advanced than the logic for planning on storage location level. All MRP types and lot-sizing procedures are possible on MRP area level</li>\n<li>The system creates planning file entries on plant and MRP area level. If planning is required on MRP area level, then the system only plans the affected MRP area. The system does not create planning file entries on storage location level and it did not do so in SAP ERP. The SAP ERP MRP had to plan all separately planned storage locations and the plant level every time inventory or an expected receipt was changed in a single separately planned storage location. Planning on MRP area level is more efficient</li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>Storage Location MRP is not available in SAP S/4HANA, on-premise edition 1511. Planning on MRP area level has to be used instead.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Conversion Pre-Checks raise an error if storage location MRP is used in the source ERP system. Run report MRP_AREA_STORAGE_LOC_MIGRATION if the pre-checks detect that storage location MRP is used. The report first checks some prerequisites like MRP types, lot-sizing procedures, and MRP areas in customizing. If the required customizing entries are missing, you have to create them manually. Follow the instructions provided by the report. If all prerequisites are fulfilled, then the report generates material master records for planning on MRP area level using the storage location material records. After the report was performed, planning is done on MRP area level, also if MRP is performed on the start release.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2216528</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2227579</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>See SAP Note 2466264 for S/4 conversion with storage location MRP for a Retail system.</p>", "noteVersion": 4, "refer_note": [{"note": "2227579", "noteTitle": "2227579 - SAP S/4HANA Simplification Item: Logistics_PP-MRP - Storage Location MRP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the system conversion to SAP S/4HANA.</p>\n<p>The custom code check shows those customer objects that are affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 simplification, PP, PP-MRP, Storage Location, piece list SI_PP2_STOR_LOC_MRP, MARD, DISKZ, MRP area, ...</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>In SAP ERP, storage locations can be excluded from MRP planning or they can be planned separately from other storage locations. In the latter case a reorder point procedure with maximum lot size is used which triggers transport reservations to fill up the storage location.</span></p>\n<p><span>MRP areas cover the very same business requirements. Materials with MRP-area-specific MRP type 'ND' (no MRP) can be used instead of materials with a storage location excluded from MRP. Materials with MRP-area-specific MRP type 'VB' (reorder point planning) can be used instead of materials with separately planned storage locations. The storage location functionality is a subset of the MRP areas capabilities.</span></p>\n<p>The S/4HANA MRP only plans on plant and MRP area level. Planning on storage location level is no longer supported.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You must remove or adapt the usages of SAP objects in your custom code, as indicated by the custom code check.</p>\n<p>Please check whether the usage of SAP objects in your custom code is still required even in a S/4HANA context. Check whether customer specific reports and evaluations can be replaced by new S4 apps like the MRP cockpit. If that is the case you should delete your development objects which are not necessary anymore.</p>\n<p>If the usage of SAP objects in your custom code is still required you have to adapt your coding ensuring that the changed development objects are used in a manner which is compatible with the business behavior in SAP S/4HANA.</p>\n<p>Based on the scope of this development topic, the number of concerned development objects and their possible usage in customer code it is unfortunately not possible to describe all possible impacts and scenarios to be checked regarding this issue. Therefore only some main impacts are described here together with necessary adjustments of your customer code.</p>\n<p><strong>Changes regarding the master data</strong></p>\n<p>As described in the Simplification Item storage locations to be excluded from MRP planning or to be planned separately are replaced by appropriate storage location MRP areas. Based on that appropriate material master settings to exclude storage location or to plan them separately (MRP4 view in material master) do not exist in S4HANA. Instead of this appropriate scenarios have to be modelled using MRP areas.</p>\n<p>If appropriate conditions have been checked in the old custom code, these checks have to be adjusted. Checks regarding the storage location indicator (MARD-DISKZ = '1' or '2') will provide no appropriate results in S/4HANA as these indicators are not set any longer. Instead of this it has to be checked whether a storage location MRP area exists for concerned material (entry in table MDMA for the MATNR, WERKS and BERID where BERID is the key of the MRP area --&gt; see customizing tables for storage location MRP areas MDLV and MDLG as well).</p>\n<p>Appropriate checks regarding existing MRP areas can be done for example using the function modules MD_MRP_AREA_GET, MD_MRP_AREA_GET_ALL or MD_GET_ALL_MATERIAL_DB.</p>\n<p><strong>Changes regarding the assignment of applicational data</strong></p>\n<p>In many customer-specific use cases planning data will be read and evaluated to select customer-specific information or determine additional KPIs to fulfill certain business need. This is usually done by using the function modules of function group M61X like AUFBAUEN_MDPSX_ANZEIGEN or MD_STOCK_REQUIREMENTS_LIST_API. Appropriate function modules can be used furthermore to select the required data but it might be necessary to adjust the calling program to get the required information.</p>\n<p>Regarding those adjustments the main aspects are explained here using the example of function module MD_STOCK_REQUIREMENTS_LIST_API:</p>\n<ul>\n<li>Generally all evaluations of the planning segments for storage location MRP will not work any longer (MDPSX-PLAAB = '06' or '07') --&gt; instead of this the planning situation for the MRP area has to be determined</li>\n<li>The determination of the planning situation for storage location MRP area requires that the function module will be called explicitly with reference to the key of storage location MRP area (BERID). Available MRP areas can be determined using one of the function modules mentioned above</li>\n<li>As MRP areas are always active in S4HANE it is generally recommended to call the function module with reference to the key of MRP area (BERID). This is valid even in case of plant MRP areas or subcontracting MRP areas. If the function module will be called without specifying the MRP area, it provides an overall planning result list that includes all available MRP areas within one list. This can be useful for certain use cases but the evaluation of single MRP areas like storage location or subcontracting MRP areas is more complicate</li>\n</ul>\n<p><strong>Testing of custom code</strong></p>\n<p>Generally it is strongly recommended to test the custom code which shall be used furtherly in S/4HANA in very detail even if no adjustments have to be done to the custom code. Sometimes changes in the behavior of the used standard objects only occur in very rare combination regarding master and transactional data.</p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 4, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}]}, {"note": "2216528", "noteTitle": "2216528 - Pre-upgrade check for storage location migration report", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Pre-upgrade check for PP-MRP functionality when preparing upgrade from Business Suite to S4H mentions this note as the one explaining the check results. The check determined that there are some materials in the system having field DISKZ set to 1 or 2 in table MARD. This means such materials had to be excluded from planning or planned separately by means of this setting. In S4H a different way is used to achieve the same goal: a material has to be assigned to a storage location MRP Area which is either excluded from planning, or is planned separately. Therefore to keep your scenarios running as before, a conversion of the data is needed.</p>\n<p>Missing storage location MRP Areas will have to be created manually. The report MRP_AREA_STORAGE_LOC_MIGRATION delivered with this note can give more details about missing storage loaction MRP Areas.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP_APPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Use report MRP_AREA_STORAGE_LOC_MIGRATION delivered with this note. It will perform all the necessary checks one more time and if all the customizing settings are correct, will generate the missing MRP area assignments to your materials.</p>\n<p>With version 18 of this note the migration report has been enhanced by an parameter 'TESTMODE'. Please add an approriate description for this parameter under 'Goto -&gt; Text Elements -&gt; Selection Texts':</p>\n<ul>\n<li>Name 'TESTMODE' - Text 'Test Mode - No DB Updates'</li>\n</ul>\n<p>This parameter allows to run the migration reports without processing data base updates immediately. It can be used to check the amount and consistency of data first before creating appropriate material assignments to storage location MRP areas (MDMA entries).</p>\n<p>Furthermore a result list will be provided which contains all material assignments to be created (testmode) or created by the migration report.</p>\n<p>For ECC Systems this parameter + result list is not available. It can be added with SAP note <span><strong>3421240</strong></span>.</p>\n<p> </p>\n<p> </p>", "noteVersion": 22}, {"note": "2466264", "noteTitle": "2466264 - SAP S/4HANA conversion for Retail with storage location MRP / MRP areas", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Master data maintenance and MRP options have been simplified with SAP S/4HANA by providing MRP areas as the only modeling option for excluding storage locations from MRP or for planning them separately. The similar function provided on SAP ERP via storage location MRP is not available with SAP S/4HANA. MRP areas cover the very same business requirements and provide more sophisticated features and functions. See SAP Note <a href=\"/notes/2268045\" target=\"_blank\">2268045</a> for more information.</p>\n<p>Existing storage location MRP data (MARD with storage location MRP indicator = '1' or '2') needs to be converted into MRP area data for SAP S/4HANA conversion. In case of a Retail system (business function ISR_RETAILSYSTEM is active) this conversion cannot be done before the actual SAP S/4HANA conversion, because MRP for MRP areas is not supported and cannot be activated on a SAP ERP Retail system (error message FSH_MG000 \"You can not activate function MRP area in SAP Retail system\"). On SAP S/4HANA, MRP for MRP areas is always active and can also be used with Retail.</p>\n<p>A particular conversion procedure applies for storage location MRP data to MRP area data during the SAP S/4HANA conversion for Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Storage Location MRP, Lagerortdisposition, Lagerortdispo, MRP Area, Dispobereich, Storage Location MRP Indicator, Dispositionskennzeichen Lagerort, DISKZ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using storage location MRP on SAP ERP with activated Retail business function and plan a conversion to SAP S/4HANA.</p>\n<p>SAP S/4HANA conversion pre-check for 1610 issues message: Check ID 'SAP_PP_MRP_MARD_CHECKS' (Application component PPMRP), return code '8'.<br/>Check Info: Action required for storage location migration see SAP Note 2216528</p>\n<p>SAP S/4HANA conversion pre-check (SIC) for 1709 and above issues message: \"Simplification item is relevant - Action required for storage location migration - see SAP Note 2466264\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>See the attached document for a detailed description of SAP S/4HANA conversion with storage location MRP for Retail.</p>\n<p>Note the special procedure described in the document for SAP S/4HANA OP 1610 FPS02 and below.</p>", "noteVersion": 7, "refer_note": [{"note": "2268045", "noteTitle": "2268045 - S4TWL - Storage Location MRP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>Master data maintenance has been simplified and offers the following benefits:</p>\n<ul>\n<li>Reduction of modeling alternatives for similar function of the MRP planning on storage location level</li>\n<li>Support of a single modeling approach that provides the most sophisticated features and funtions for planning on storage location level</li>\n</ul>\n<p><strong>Description</strong></p>\n<p><span>Context</span></p>\n<p>In SAP ERP, storage locations can be excluded from MRP planning or they can be planned separately from other storage locations. In the latter case a reorder point procedure with maximum lot size is used which triggers transport reservations to fill up the storage location.</p>\n<p>MRP areas cover the very same business requirements. Materials with MRP-area-specific MRP type 'ND' (no MRP) can be used instead of materials with a storage location excluded from MRP. Materials with MRP-area-specific MRP type 'VB' (reorder point planning) can be used instead of materials with separately planned storage locations. The storage location functionality is a subset of the MRP areas capabilities.</p>\n<p><span>Description of Simplification</span></p>\n<p>The S/4HANA MRP only plans on plant and MRP area level. Planning on storage location level is not available in SAP S/4HANA, on-premise edition 1511.</p>\n<p><span>Reason</span></p>\n<p>The reason for this change are:</p>\n<ul>\n<li>The MRP logic is simplified. There is only one solution for planning on storage location level</li>\n<li>The MRP area logic is more advanced than the logic for planning on storage location level. All MRP types and lot-sizing procedures are possible on MRP area level</li>\n<li>The system creates planning file entries on plant and MRP area level. If planning is required on MRP area level, then the system only plans the affected MRP area. The system does not create planning file entries on storage location level and it did not do so in SAP ERP. The SAP ERP MRP had to plan all separately planned storage locations and the plant level every time inventory or an expected receipt was changed in a single separately planned storage location. Planning on MRP area level is more efficient</li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>Storage Location MRP is not available in SAP S/4HANA, on-premise edition 1511. Planning on MRP area level has to be used instead.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Conversion Pre-Checks raise an error if storage location MRP is used in the source ERP system. Run report MRP_AREA_STORAGE_LOC_MIGRATION if the pre-checks detect that storage location MRP is used. The report first checks some prerequisites like MRP types, lot-sizing procedures, and MRP areas in customizing. If the required customizing entries are missing, you have to create them manually. Follow the instructions provided by the report. If all prerequisites are fulfilled, then the report generates material master records for planning on MRP area level using the storage location material records. After the report was performed, planning is done on MRP area level, also if MRP is performed on the start release.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2216528</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2227579</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>See SAP Note 2466264 for S/4 conversion with storage location MRP for a Retail system.</p>", "noteVersion": 4}]}], "activities": [{"Activity": "Data migration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Run report MRP_AREA_STORAGE_LOC_MIGRATION, Follow the instructions provided by the report. If all prerequisites are fulfilled, then the report generates material master records for planning on MRP area level using the storage location material records. SAP Note: 2216528"}, {"Activity": "Data cleanup / archiving", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If exist, Custom Code must be adjusted accordingly SAP Note: 2227579"}]}