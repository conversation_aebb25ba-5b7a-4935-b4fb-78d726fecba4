{"Request": {"Number": "2712569", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 461, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002252042018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002712569?language=E&token=578DEB43A8F86D9FF627B4732B3525CF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002712569", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002712569/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2712569"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "LO-RFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Retail & Fashion Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Retail & Fashion Management", "value": "LO-RFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-RFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2712569 - Retail Term Replacement in S/4 for 1709 ff"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The term replacement functionality for Industry Solution Retail is on the simplification list to be deprecated. Screens show standard terminology, for example, 'Material' instead of 'Article', 'Plant' instead of 'Site'.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RSBRAN03 SRT Short Text Replacement&#160;<em>Application Text Verticalization</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The term replacement functionality&#160;in ECC exchanged standard screen texts with Retail specific screen texts system wide.</p>\r\n<ul>\r\n<li>The term replacement tool supports one terminology system wide</li>\r\n<li>The term replacement tool does not support FIORI property files</li>\r\n<li>The term replacement tool is not&#160;compliant for operations in cloud</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>A&#160;successor solution is in place from S/4 HANA 2023 onwards. In case Retail terminology is required in earlier releases, the&#160;term replacement tool is offered with the following limitations:</p>\r\n<ul>\r\n<li>\r\n<div>The term replacement solution does not support FIORI applications</div>\r\n</li>\r\n<li>The Retail terminology is offered only for a limited set of languages. Currently these languages&#160;are:</li>\r\n<ul>\r\n<li>Supported languages in release 1709 on Premise: German, English</li>\r\n</ul>\r\n</ul>\r\n<p>Please execute the following steps:</p>\r\n<ol>\r\n<li>Make sure that the corrections of SAP Note&#160;2411388 are implemented</li>\r\n<li>Download the term replacement files for the corresponding release&#160;via the following link from the service market place and import them as described in SAP Note 897714. Before downloading a logon to the SAP help <a target=\"_blank\" href=\"https://help.sap.com/\">https://help.sap.com</a>&#160;portal is needed. Only logged on users have the authorization to download the files.</li>\r\n<ul>\r\n<li>The term replacement files for release S/4HANA 1709 are available <a target=\"_blank\" href=\"https://help.sap.com/doc/1f835bbbcb7f4fa8b3c566c6f11442a0/1709/en-US/sap_s_4hana_retail_str_op_1709_EN_DE_NL_ES_PL_FR.zip\">here</a>. Supported languages are English, German, Spanish, French, Polish and Dutch.</li>\r\n<li>The term replacement files for release S/4 HANA 1809 are available <a target=\"_blank\" href=\"https://help.sap.com/doc/4bb85cdd23a945029234bacc08e0394f/1809/en-US/sap_s_4hana_retail_str_op_1809_EN_DE_NL_ES_HU.zip\">here</a>.&#160;Supported languages are English, German, Spanish, Hungarian and Dutch.</li>\r\n<li>The term replacement files for release S/4 HANA 1909 are available&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/325a0b73200148478780b5f06e4e53b2/1909/en-US/sap_s_4hana_retail_str_op_1909_EN_DE_NL_ES.zip\">here</a>.&#160;Supported languages are English, German, Spanish, and Dutch.</li>\r\n<li>The term replacement files for release S/4 HANA 2020 are available&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/2bfc2d601bdb4f86abaae7277b51633f/2020/en-US/sap_s_4hana_retail_str_op_2020_EN_DE_NL_ES_HU.zip\">here</a>.&#160;Supported languages are English, German, Spanish, Hungarian and Dutch.</li>\r\n<li>The term replacement files for release S/4 HANA 2021 are available&#160;<a target=\"_blank\" href=\"https://help.sap.com/doc/1164408729e641f381c3f039e8ead838/2021/en-US/sap_s_4hana_retail_str_op_2021_DE_EN_HU_NL_ES.zip\">here</a>.&#160;Supported languages are English, German, Spanish, Hungarian and Dutch.</li>\r\n<li><strong>Newer replacement files are not available.&#160;</strong></li>\r\n<li>Please see notes 3361940 and 351551 for information on the successor solution&#160;<em>Application Text Verticalization.</em></li>\r\n</ul>\r\n<li>Test the business scenarios before bringing the solution to the productive system.</li>\r\n</ol>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D059843)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D059843)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002712569/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002712569/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "897714", "RefComponent": "IS-R-STR", "RefTitle": "Activating Retail Short Text Replacement", "RefUrl": "/notes/897714"}, {"RefNumber": "2411388", "RefComponent": "BC-CTS-LAN", "RefTitle": "Runtime error DBSQL_ILLEGAL_CLIENT_SPECIFIED during language supplementation", "RefUrl": "/notes/2411388"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2776375", "RefComponent": "IS-R-STR", "RefTitle": "Note 897714 contains outdated URL for Retail Short Text Replacement files", "RefUrl": "/notes/2776375 "}, {"RefNumber": "2377816", "RefComponent": "LO-MD", "RefTitle": "S4TWL - Retail Short Text Replacement", "RefUrl": "/notes/2377816 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}