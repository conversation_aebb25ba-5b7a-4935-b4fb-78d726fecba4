{"Request": {"Number": "1958910", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 382, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000011564372017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001958910?language=E&token=21CAA9983CF1341BBFBF04E79FB80D44"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001958910", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001958910/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1958910"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER-EWA"}, "SAPComponentKeyText": {"_label": "Component", "value": "EarlyWatch Alert"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "EarlyWatch Alert", "value": "SV-SMG-SER-EWA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER-EWA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1958910 - <PERSON><PERSON><PERSON> Alert For HANA Database"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have a HANA system and&#160;want to schedule the EarlyWatch Alert (EWA) service for this system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>EWA for HANA, FRUN, Remote Database Connection</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>There are two different scenarios to set up the&#160;EWA for HANA: either as part of&#160;the EWA report of an ABAP backend or the EWA for the HANA system.&#160;When the HANA database is used by an ABAP system, the first scenario with the EarlyWatch Alert checks for HANA&#160;appearing in the EarlyWatch Alert report for this ABAP backend, is the natural choice. In all other cases the scenario with the EWA&#160;setup for the HANA system itself is the suitable scenario.</p>\r\n<p>The HANA related content of the EWA report does not depend on the&#160;scenario chosen.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Scenario 1: EWA for an ABAP System With HANA Checks</strong></span></p>\r\n<p>Basically, the HANA checks not only appear in the EWA for the ABAP system when the primary database is on HANA, but also when another (secondary) database is used by the ABAP system through a remote database connection (maintained in transaction DBCO, stored in table DBCON). To have the latest data collectors available, it is recommended to apply from time to time the latest version of&#160;<a target=\"_blank\" href=\"/notes/1985402\">SAP Note 1985402</a>.</p>\r\n<p>On the Solution Manager checks for SAP HANA require ST-SER 701_2010_1 SP 06 (see <a target=\"_blank\" href=\"/notes/1529948\">SAP Note 1529948</a>).<br /><br /></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Scenario 2: EWA for an HANA System</strong></span></p>\r\n<ul>\r\n<li><span style=\"text-decoration: underline;\"><strong>Collection in the Solution Manager</strong></span></li>\r\n</ul>\r\n<p>The service data are collected using a remote database connection (aka&#160;<em>DBCON</em>), which can be created in the <em>Managed System Setup</em> or in transaction <em>DBACockpit</em>. The database connection is automatically determined with the help of the Extended System ID in the dbacockpit (the Extended System ID in DBACockpit has to be equal with the Extended System ID in the LMDB).&#160; Alternatively, the connection name can be maintained as LMDB attribute of the database. To do so, maintain in the <em>LMDB Editor</em> (transaction <em>/nLMDB</em>) the technical system of type HANADB.&#160;In the LMDB details maintain the name of the remote database connection&#160;as value of&#160;the attribute 'Remote Database Connection'.<br />To have the latest data collectors available, it is recommended to apply from time to time the latest version of&#160;<a target=\"_blank\" href=\"/notes/1985402\">SAP Note 1985402</a>.</p>\r\n<p>The EarlyWatch Alert for product <em>SAP HANA PLATFORM EDITION</em> requires</p>\r\n<ul>\r\n<li>Solution Manager <strong>ST 7.1 Service Pack 08</strong></li>\r\n<ul>\r\n<li>Below ST 7.1 Service Pack 12 apply SAP Note <a target=\"_blank\" href=\"/notes/1988691\">1988691</a>&#160;additionally.</li>\r\n</ul>\r\n<li>Service Tools <strong>ST-SER</strong> 701_2010_1 <strong>Service Pack 23</strong>.<strong><br /></strong>If you are not yet on SP23,&#160;alternatively to&#160;installing SP23 you can fulfill the requirements with:</li>\r\n<ul>\r\n<li>ST-SER 701_2010_1&#160;with <em>Service Content </em><em>Update </em>[SCU] enabled. (The correction was shipped with SCU early September 2014.) You find more on SCU in SAP Note <a target=\"_blank\" href=\"/notes/1143775\">1143775.</a></li>\r\n<li>Also apply the correction instruction of this SAP Note.</li>\r\n</ul>\r\n<li>SAP Solution Tools Plug-In <strong>ST-PI</strong> 2008_1_700 <strong>Service Pack 10</strong>.</li>\r\n</ul>\r\n<p>In&#160;Solution Manager 7.2, you activate the EWA for your HANA system using Work Center <em>Solution Manager Configuration -&gt; Application Operations -&gt; EarlyWatch Alert Management . </em>(Transaction <em>solman_ewa_admin </em>may also be used.)</p>\r\n<p>In&#160;Solution Manager 7.1,&#160;define a product system for product <em>SAP HANA PLATFORM EDIT</em> including the technical system of type HANADB. Include the product system&#160;in a logical component and include the logical component in a solution. If these requirements are met you can activate the EarlyWatch&#160;Alert for the HANA product system. (To check this configuration for your HANA system you may use Work Center <em>Solution Manager Configuration -&gt; EarlyWatch Alert Management </em>[on ST 7.1 SP12 or higher].)</p>\r\n<ul>\r\n<li><strong>Collection in Focused RUN (FRUN)</strong></li>\r\n</ul>\r\n<p>In FRUN the Download for a standalone HANA Database can also be collected for certain scenarios. <br /> The collection is done via the CSA (Configuration Security Analysis) and the System Analysis framework. The prerequisites are mentioned in <a target=\"_blank\" href=\"/notes/2711993\">SAP Note 2711993</a> - EarlyWatch Alert for HANA in FRUN Environments</p>\r\n<p>In the FRUN we have the following scenarios:</p>\r\n<ul>\r\n<li>ABAP on top of a HANA Database<br /> In this case no Standalone HANA Database should be scheduled</li>\r\n<li>JAVA on top of a HANA Database<br /> In this case the download collection should be issued by the JAVA System, there should be now separate HANA Download scheduled in FRUN.</li>\r\n<li>System Replication<br /> We distinguish between &#8216;Virtual&#8217; and &#8216;Physical&#8217; Databases. The &#8216;Virtual&#8217; Database is like clamb organizing in FRUN and Solution Manager the Physical System Replication Sites in FRUN. The download should always be scheduled for the &#8216;Virtual Database&#8217; &#8211; which can be seen in transaction sdccn with the Long SID.<br /> If you see here download collections for the &#8216;Physical Database&#8217; this might have the following reasons:\r\n<ul>\r\n<li>During Takeovers in system replication this &#8216;clamb&#8217; of the Virtual Database is reorganized (triggered by the SLD Data Supplier). After this reorganization the Simple System Integration is triggered again, with a correct setup of the System Monitoring and Configuation, Security Administration.<br /> Therefore also the EWA should be scheduled again with the Virtual Database.</li>\r\n<li>This behavior was not taken into account in FRUN (for FRUN 1 the EWA was scheduled for Virtual and Physical Databases). Partially this might have to be corrected manually .</li>\r\n<li>SystemDB<br /> The SystemDB is always scheduled as a Standalone HANA Database and therefore the above mentioned points are also valid the the SystemDB.<br /> Please be aware that scheduling the EWA for the SystemDB is also important. The following contents differs from the EWA of a Tenant Database:\r\n<ul>\r\n<li>Overview over all Tenant Databases</li>\r\n<li>Parameter Recommendation</li>\r\n</ul>\r\n</li>\r\n</ul>\r\n</li>\r\n</ul>\r\n<p>Starting with FRUN 4 FP 1 the report &#8216;FRUN_EWA_ADJUST_ACTIVATION&#8217; will be published which deactivated wrong running download collections in the following scenarios:</p>\r\n<ul>\r\n<li>Deactivating Download Collection for HANA Database with ABAP Systems on TOP</li>\r\n<li>Deactivating Download Scheduling for HANA Databases with JAVA on TOP</li>\r\n<li>Deactivating Download Scheduling for &#8216;Physical Databases&#8217; in a Replication Scenario</li>\r\n</ul>\r\n<p>This action can also be 'simulated' when the 'DISPLAY' field is marked (which is the default setting).</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Remarks</span></strong></p>\r\n<p><em><strong>Remark</strong></em>:&#160;If you are already on ST-SER 701_2010_1 SP23 the correction instruction of this SAP Note is no more relevant and you do not need to apply it. The correction instruction for SP23 was only required during a short period in August 2014 while <em>Service Content </em><em>Update</em> did not yet include SP23: in the&#160;special situation that&#160;SP23 was already applied to the Solution Manager and&#160;<em>Service Content </em><em>Update&#160;</em>was used&#160;also the correction instruction was required.</p>\r\n<p><em><strong>Remark regarding EWA and SystemDB:</strong></em> An EarlyWatch Alert of the SystemDB is helpful, because other System Settings / Configurations are possible to collect. In addition (currently only on the Solution Manager) an overview about all Tenant Databases is visualized.&#160;<br />Please pay attention: the connection to the SystemDB is different than that to the Tenant Database. The connection to the SystemDB is: &lt;hostname&gt;:3&lt;InstanceNo&gt;13.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Troubleshooting</strong></span></p>\r\n<ul>\r\n<li>If you want to run the EWA for an S/4 HANA On Premise system and the corresponding entry is&#160;missing from the Solution&#160;Directory, please use&#160;&#160;<a target=\"_blank\" href=\"/notes/1603103\">SAP Note <strong><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: #1f497d; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: DE; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;\">1603103</span></strong></a>&#160;to import the missing SAP product version.</li>\r\n<li><sub></sub>In case the HANA system does not appear in the EarlyWatch Alert administration screen, where you would need to activate the EWA:<br />Please check if SAP Note <a target=\"_blank\" href=\"/notes/0001984327\" title=\"1984327\">1984327</a> applies.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-SER (SAP Support Services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D028075)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001958910/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001958910/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1984327", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP HANA standalone not appearing in SMSY", "RefUrl": "/notes/1984327"}, {"RefNumber": "1988691", "RefComponent": "SV-SMG-OP", "RefTitle": "SDCCN does not collect download data for HANADB", "RefUrl": "/notes/1988691"}, {"RefNumber": "1985402", "RefComponent": "SV-SMG-SER", "RefTitle": "Requirements and Corrections For Collecting HANA Service Data", "RefUrl": "/notes/1985402"}, {"RefNumber": "1603103", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: The product definition for one or several SAP product versions is missing", "RefUrl": "/notes/1603103"}, {"RefNumber": "1543278", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Service Sessions for HANA: Preparing the SAP Back End", "RefUrl": "/notes/1543278"}, {"RefNumber": "1542651", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch alert for HANA: Service data collection", "RefUrl": "/notes/1542651"}, {"RefNumber": "1529948", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "EarlyWatch Alert: SAP HANA in EWA Services", "RefUrl": "/notes/1529948"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "696478", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Security Optimization: Preparation, additions", "RefUrl": "/notes/696478 "}, {"RefNumber": "2378962", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 2.0 Revision and Maintenance Strategy", "RefUrl": "/notes/2378962 "}, {"RefNumber": "2283516", "RefComponent": "SV-SMG-OP", "RefTitle": "HANA EWA Session is missing HANA Database information Part II for ST 710", "RefUrl": "/notes/2283516 "}, {"RefNumber": "2281878", "RefComponent": "SV-SMG-SDD", "RefTitle": "HANA EWA Session is missing HANA Database information Part I for ST-PI", "RefUrl": "/notes/2281878 "}, {"RefNumber": "1985402", "RefComponent": "SV-SMG-SER", "RefTitle": "Requirements and Corrections For Collecting HANA Service Data", "RefUrl": "/notes/1985402 "}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308 "}, {"RefNumber": "1892593", "RefComponent": "SV-SMG-SER", "RefTitle": "Preparing Support Services for SAP HANA Scenarios", "RefUrl": "/notes/1892593 "}, {"RefNumber": "2021789", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1.0 Revision and Maintenance Strategy", "RefUrl": "/notes/2021789 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-SER", "From": "701_2010_1", "To": "701_2010_1", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST-SER 701_2010_1", "SupportPackage": "SAPKITLOSP", "URL": "/supportpackage/SAPKITLOSP"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-SER", "NumberOfCorrin": 2, "URL": "/corrins/0001958910/401"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}