{"Request": {"Number": "2267863", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 305, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018243892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002267863?language=E&token=7502FC1D220B42AD6AF73F08131DDB8C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002267863", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002267863/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2267863"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "PLM-RM-REC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Recipe"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Product Lifecycle Management", "value": "PLM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PLM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Recipe Management", "value": "PLM-RM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PLM-RM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Recipe", "value": "PLM-RM-REC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PLM-RM-REC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2267863 - S4TWL - Recipe Management"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>PLM, Recipe Management, Simplification</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>PLM Recipe Management related transactions are disabled</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong></p>\r\n<p>SAP PLM Recipe Management (RM) is not available in SAP S/4HANA. This also affects PLM RM sub-functions such as Labelling, Trial Management and Handover to Manufacturing.&#160;The appropriate business requirements are covered within successor functionality&#160;SAP S/4HANA PLM Recipe Development (excluding Trial Management.)</p>\r\n<p>The takeover of recipes from&#160;SAP Business Suite into SAP S/4HANA is supported, so the recipes are available and can be displayed after the system conversion to SAP S/4HANA. To use and further develop them, they need to be migrated via&#160;the&#160;migration function integrated into recipes in SAP S/4HANA PLM Recipe Development.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>Functions related to PLM Recipe Development (RM),&#160;such as the Recipe Workbench,&#160;Recipe, Labelling and Trial Management, are not available within SAP S/4HANA. The functional equivalent is SAP&#160;PLM Recipe Development (RD).</p>\r\n<p>The customer needs to implement development processes based on SAP PLM Recipe Development which will change the user interaction with the software and might include minor process changes. It also allows users&#160;to leverage new capabilities of PLM Recipe Development to increase&#160;the software based support for the product developer.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"182\">\r\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<p>RMWB&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Start Workbench<br />BD_GEN_GRCP&#160;&#160;&#160;&#160;&#160;&#160;General Recipe Distribution<br />BD_GEN_SRCP&#160;&#160;&#160;&#160;&#160;&#160;Replicate Site Recipe<br />FRML02&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Edit Recipe Formula<br />FRML03&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Display Recipe Formula<br />FRML04&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Formula Information System<br />MRTRS_START&#160;&#160;&#160;&#160;&#160;&#160;Start MR Transformation <br />MRTRSC01 &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; RFC Destination of GR<br />MRTRSC02&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;Master Recipe Generation<br />RMSL02&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Change Label Set<br />RMSL03&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Display Label Set<br />RMSTAT&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Mass Status Change</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"182\">\r\n<p>Customizing Transaction not available in SAP S/4HANA on-premise edition 1511</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<p>ORCP*, RCP*, RMS*, RMX*</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>Projects&#160;need to migrate their processes from PLM Recipe Management functional capabilities to PLM Recipe Development functional capabilities.</p>\r\n<p>Data Migration from RM recipes to RD recipes is an integrated part of the tool and can be executed after the system conversion SAP S/4HANA. For the detailed steps, follow the guides for the migration from PLM RM to PLM RD.</p>\r\n<p>The specification object is not impacted by the switch over to PLM RD.</p>\r\n<p><strong>Related SAP Notes</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"302\">\r\n<p>Conversion Pre-Checks</p>\r\n</td>\r\n<td valign=\"top\" width=\"302\">\r\n<ul>\r\n<li>Does the customer use TRIAL MANAGEMENT</li>\r\n<li>Check which recipes, formulas and label objects need to be migrated</li>\r\n<li>Migration transactions - executable before or after system conversion &#160;\r\n<ul>\r\n<li>/PLMI/RCP_MIGRATION&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Recipe Migration</li>\r\n<li>/PLMI/RMSL_MIGRATION&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Migration of Label Sets</li>\r\n</ul>\r\n</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>How to Determine Relevancy</strong></p>\r\n<ul>\r\n<li>The PLM Recipe Management is used if entries exist in DB Table RMSAT_HDR or RMXTT_TRIAL_HD (check with SE16)</li>\r\n<li>Use ST03N (on the production system, if possible) to check transaction profile of the complete last month, whether the following transactions have been used: <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone80\" style=\"text-align: left;\">RMWB, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone81\" style=\"text-align: left;\">FRML02, </span><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone81\" style=\"text-align: left;\">FRML03, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone81\" style=\"text-align: left;\">FRML04, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone86\" style=\"text-align: left;\">RMSL02, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone86\" style=\"text-align: left;\">RMSL03</span></span></span></span></span></li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D065718)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D022084)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002267863/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267863/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}