{"Request": {"Number": "1371956", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 367, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016847152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001371956?language=E&token=684C301FD881C572A799EDD9FAF455EE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001371956", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1371956"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.09.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-ISR-RSL"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Retail Stock Ledger"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail and Consumer Products", "value": "BW-BCT-ISR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail Stock Ledger", "value": "BW-BCT-ISR-RSL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR-RSL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1371956 - RMA: Recommended settings for good performance"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns various settings in the BW system that improve the performance of the various steps of the Retail Method of Accounting (RMA) processes with reference to the following symptoms:<br />1) During processing of the task for sending to BW ('0001' or '0004' in standard Customizing) in the POS Inbound Processing Engine (PIPE) dispatcher, a large number of logical units of work (LUWs) are created in the delta queue of the DataSource 0RT_PA_TRAN_CONTROL. This has a negative effect on the performance.<br />2) The update of a data target using a data transfer process (DTP) is not carried out in parallel, even though parallel processing is set as the processing mode in the DTP.<br />3) Due to a large number of data packages, there are very long runtimes in the BW monitor during the delta upload of the DataSource 2LIS_03_BF to the Persistent Staging Area (PSA).<br />4) Due to a large number of data packages, there are very long runtimes in the BW monitor during the delta upload from the PSA to the DataStore object (DSO) 0RSL_DS51. Problems may occur during the subsequent activation of the request in the DSO.<br />5) The DataSource 2LIS_03_BF partially delivers data packages for which the package size exceeds the parameter MAXSIZE in the table ROIDOCPRMS in the source system by a considerable amount.<br />6) The runtime for POS inbound from the POS Inbound Processing Engine (PIPE) in SAP ERP is too long.<br />7) The runtime for the RMA Engine (data transfer process (DTP) 0RT_RMA_02 -&gt; 0RMA_DS01) is quite long, especially in the case of complex RMA calculation schemas.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Retail method of accounting (RMA), stock ledger<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are using BI_CONT 7.04 or later.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1) The required package size of a logical unit of work (LUW) must be specified when the task is defined in the Implementation Guide (IMG) for point of sale data management (POS DM) and under<br />POS Inbound Processing -&gt; Tasks -&gt; One-Step Processing -&gt; Tasks -&gt; Detail view for task code 0001' or '0004' -&gt; Number of Items (under Processing/Performance). The package size defined here overrides the parameter MAXSIZE in the table ROIDOCPRMS.<br />2) The error handling must be set to '0' (deactivated) so that the update is carried out in parallel.<br />3) The parameter MAXDPAKS for the table ROIDOCPRMS in the source system is used to restrict the number of data packages for each request. For more information, see Note 892513.<br />4) Use the parameter \"Get All New Data Request By Request\" on the \"Extraction\" tab page in the data transfer process (DTP) definition. With this parameter, a DTP request is created for each request in the data source, and therefore, the size of each individual DTP request is limited.<br />5) According to Note 1364412, depending on Customizing settings, additional data packages must be added to a data package in the DataSource 2LIS_03_BF for the RMA-specific valuation of stock transfers. You must define a different package size in the InfoPackage for this DataSource, to avoid problems with the size of the main memory when you load data. The optimal size depends on the settings in the Customizing view V_WRMA_TRANSF_C in ERP.<br />6) If buffering is not adequate for the number ranges in ERP, this creates particular problems. This applies to the number range objects MATBELEG, RV_BELEG and RF_BELEG. Note 213546 contains information on these and further notes about improving performance for POS inbound. Furthermore, for ERP POS inbound, we recommend that you limit the number of segments for the IDocs that are created. To do this, you can use the parameter for the package size of the relevant PIPE task. Under \"POS Inbound Processing -&gt; Tasks -&gt; One-Step Processing -&gt; Tasks -&gt; Detail view for task '0014' (for example) -&gt; Number of Items\", you can limit the number of segments in an IDoc. The number of segments also depends on the aggregation method selected for the task.<br />7) The generic processing of an RMA calculation schema causes the runtime to grow proportionally with the number of lines in the schema. In particular, the system also processes lines for which the system attempts to read conditions that are not created during the raw data update. Therefore, we recommend that you copy a suitable calculation schema from delivery Customizing, and delete any lines that refer to buckets that are not used from the copy. For example, the lines for markdown cancellations, markup cancellations, and advance discounts can be deleted from the schema if these buckets are not used and therefore are not filled.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019835)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037986)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001371956/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001371956/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1383219", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Performance Issues RMA engine", "RefUrl": "/notes/1383219"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "1383219", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Performance Issues RMA engine", "RefUrl": "/notes/1383219 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}