{"Request": {"Number": "1560383", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 338, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017182122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001560383?language=E&token=2CD4685D53F5AEA7EDF4F76246D5A6AA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001560383", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1560383"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released Internally"}, "ReleasedOn": {"_label": "Released On", "value": "02.09.2020"}, "SAPComponentKey": {"_label": "Component", "value": "GRC-ACP"}, "SAPComponentKeyText": {"_label": "Component", "value": "GRC Access Control Plug-In"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Governance, Risk and Compliance", "value": "GRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "GRC Access Control Plug-In", "value": "GRC-ACP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC-ACP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1560383 - Cannot install GRC 10.0 plug-in due to low BASIS, ABA SP lvl"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are unable to install GRC 10.0 plug-in because the existing SAP_BASIS &amp; SAP_ABA Support Package levels are lower that the required import conditions for the Plug-In</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>GRCPINW 1000_46C , GRC 10.0 Netweaver Plug-in</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The Support Package levels of your system is lower than the import conditions required by the GRC Plug-In</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Upgrade your system to the following Support Package levels as needed by the import conditions<br /><br />SAP_ABA 46C SAPKA46C55<br />SAP_APO 30A SAPKY30A08<br />SAP_BASIS 46C SAPKB46C55<br /><br />or<br /><br />SAP_ABA 46C SAPKA46C55<br />SAP_APPL 46C SAPKH46C50<br />SAP_BASIS 46C SAPKB46C55<br /><br /><br />If an upgrade is not possible, upload the Archive ACP_GRCPINW_V1000_46C.SAR attached to this note. This ACP will change the import attributes of the OCS package as follows<br /><br />SAP_ABA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKA46C55<br />SAP_APO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;30A&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKY30A08<br />SAP_BASIS&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKB46C55<br /><br />OR<br /><br />SAP_ABA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKA46C48<br />SAP_APPL&#x00A0;&#x00A0;&#x00A0;&#x00A0; 46C&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKH46C48<br />SAP_BASIS&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAPKB46C48<br /><br />Then proceed as follows:</p> <UL><LI>At least SPAM/SAINT Version 0047 is a prerequisite for the processing of ACPs in release 46C.</LI></UL> <UL><LI>Copy the ACP-Archive ACP_GRCPINW_V1000_46C.SAR to your transport directory, e.g. /usr/sap/trans for UNIX. Unpack the Archive with the command<br />'SAPCAR -xvf ACP_GRCPINW_V1000_46C.SAR'</LI></UL> <UL><LI>Afterwards, the file M6D0120007986_0003015.PAT should be available in the EPS-Inbox (directory /usr/sap/trans/EPS/in for UNIX).</LI></UL> <UL><LI>No other ACP GRCPINW===V1000_46C than the one with number M6D0120007986_0003015.PAT should be available in this directory, please check this in your system via transaction SEPS -&gt; Goto -&gt; Inbox.</LI></UL> <UL><LI>Download the installation package for GRCINW V1000_46C from the Service Marketplace.</LI></UL> <UL><LI>Load the contents of the file GRCPINW===V1000_46C into the EPS inbox (SAINT &gt; Installation Package &gt; Load Packages &gt; From Front End).</LI></UL> <UL><LI>Then you can use SAINT to define the required queue.</LI></UL> <p></p> <b>Note - IMPORTANT</b><br /> <p>After implementing this OSS-note PRIOR to implementing GRC 10.0 plug-in please implement OSS-note 1560308!<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "GRC-PCP (GRC Process Control Plug-in)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I811204)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> <PERSON><PERSON><PERSON> (I048557)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001560383/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001560383/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ACP_GRCPINW_V1000_46C.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000165382011&iv_version=0002&iv_guid=44D38EB4D98EB6459B4CFDE12383E0A9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1560308", "RefComponent": "GRC-ACP", "RefTitle": "Cannot install GRC 10 plug-in because of missing ACH node", "RefUrl": "/notes/1560308"}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1560308", "RefComponent": "GRC-ACP", "RefTitle": "Cannot install GRC 10 plug-in because of missing ACH node", "RefUrl": "/notes/1560308 "}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "GRCPINW", "From": "V1000_46C", "To": "V1000_46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}