{"Request": {"Number": "968222", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 642, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016137232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000968222?language=E&token=C2AB9B3782FE9F345058C7E6F9AB7605"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000968222", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000968222/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "968222"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-SEM-CPM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Corporate Performance Monitor"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Enterprise Management", "value": "FIN-SEM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Corporate Performance Monitor", "value": "FIN-SEM-CPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-SEM-CPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "968222 - SEM 3.5 Front end patch 8 (July 2006)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SEM 3.1A/B, SEM 3.2, SEM 3.5: SEM 4.0 there is an error in SEM Chart.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Patch, GUI, SAP GUI, front end, front, graphic, installation, SAPClient, SEM, frontend patch, SEMOCX, Balanced Scorecard, BSC</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This involves at least 46D GUI Compilation 3. Downwardly compatible for SEM 2.0B, SEM 3.0A, SEM 3.1A, SEM 3.1B, SEM 3.2, SEM 3.5, SEM 4.0</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of Release 4.6D Compilation 3, the SAP GUI installation provides a new tool for importing patches. Instead of manually unpacking the patches on the installation server, importing the patch now occurs automatically. Here, the patch files are copied and the installation database is updated on the installation server. Clients on which SapSetup is started are able to recognize that the new patch is installed on the server and can update themselves. As of the following patch levels, patches can only be installed using this tool:</p> <UL><LI>sem31a:&#x00A0;&#x00A0;&#x00A0;&#x00A0; 8</LI></UL> <UL><LI>setup46D:&#x00A0;&#x00A0; 5</LI></UL> <p>Manual unpacking of these and subsequent patches is NO LONGER possible. (See Note 361222).</p> <OL>1. Prepare an installation server by starting setup.exe and select \"Administrative set-up\".</OL> <OL>2. Download the archive of SapservX and the files into the installation directory of the copy server procedure:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;a) Download archive&#x00A0;&#x00A0;&#x00A0;&#x00A0;sem31a.exe&#x00A0;&#x00A0; - SEM 3.5 SP8 patch ftp://sapservX/general/frontend/patches/rel610/Windows/Win32/ <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b) Download locally onto the GUI server. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;c) On the installation server, start the SapSetup -&gt; Configuration -&gt; SapAdmin program from the \\netinst directory. Note that the access must occur via an UNC path: \\\\&lt;Servername&gt;\\&lt;ShareName&gt;\\netinst\\sapadmin.exe <OL>3. Select the menu option 'Tools --&gt; Apply Patch'.</OL> <OL>4. Specify the patch file you would like to install.</OL> <OL>5. Choose \"Install\" on the clients:</OL> <OL>6. Start the installation as usual with Netsetup, select SEM Addon and install. It is imperative that you do not register OCX files manually. Otherwise, a clean deinstallation of Netinstall is not ensured.</OL> <p>No installation server is available for a standalone computer, nevertheless there is an option to install the new patch. For that, you need the setup update: localpat46D_Y.exe (Y indicates the current patch level) that is available at: sapservx/general/frontend/patches/rel46D/Windows/Win32. (See Note 361222) Both for Balanced Scorecard, as well as for Management Cockpit, a SAP Chart&#x00A0;&#x00A0;OCX is required for the display of graphic components. You can also find the latest version of this component on sapservX. Note 318196 describes how you can obtain and install this patch. We recommend you always import the latest patch here. The SEM components require the basis/chart components. Install these with the most current front-end patch. This is an example of the i nstallation of the most current front-end patch of sapserv3: for 610: ftp://sapserv3/general/frontend/patches/rel610/Windows/Win32/<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I023479)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000968222/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000968222/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885"}, {"RefNumber": "811071", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 Frontend Patch 6 (January 2005)", "RefUrl": "/notes/811071"}, {"RefNumber": "656975", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 1 (Oct.2003)", "RefUrl": "/notes/656975"}, {"RefNumber": "598364", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Error when calling graphic OCX in SEM-CPM", "RefUrl": "/notes/598364"}, {"RefNumber": "571477", "RefComponent": "FIN-SEM", "RefTitle": "Installation of SAP SEM Release 3.2", "RefUrl": "/notes/571477"}, {"RefNumber": "566094", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 3.2", "RefUrl": "/notes/566094"}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415"}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "314973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/314973"}, {"RefNumber": "311212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311212"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "454415", "RefComponent": "FIN-SEM", "RefTitle": "Short dump CNTL_ERROR for transactions in SEM", "RefUrl": "/notes/454415 "}, {"RefNumber": "656975", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 frontend patch 1 (Oct.2003)", "RefUrl": "/notes/656975 "}, {"RefNumber": "811071", "RefComponent": "FIN-SEM-CPM", "RefTitle": "SEM 3.5 Frontend Patch 6 (January 2005)", "RefUrl": "/notes/811071 "}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}, {"RefNumber": "571477", "RefComponent": "FIN-SEM", "RefTitle": "Installation of SAP SEM Release 3.2", "RefUrl": "/notes/571477 "}, {"RefNumber": "598364", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Error when calling graphic OCX in SEM-CPM", "RefUrl": "/notes/598364 "}, {"RefNumber": "566094", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 3.2", "RefUrl": "/notes/566094 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP150", "SupportPackagePatch": "000150", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 32-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007105&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EX2 32-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009585&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EX2 64-BIT", "SupportPackage": "SP2381", "SupportPackagePatch": "002381", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009589&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP224", "SupportPackagePatch": "000224", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP224", "SupportPackagePatch": "000224", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}