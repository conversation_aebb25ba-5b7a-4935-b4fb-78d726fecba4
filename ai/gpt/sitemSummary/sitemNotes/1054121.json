{"Request": {"Number": "1054121", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 294, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016283702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001054121?language=E&token=36BB5F6712441ED55CF3B19FB19F736D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001054121", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001054121/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1054121"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2020"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-SAPSMP-CON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Contents"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Portal", "value": "XX-SER-SAPSMP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SAPSMP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Contents", "value": "XX-SER-SAPSMP-CON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SAPSMP-CON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1054121 - The SAP Ecosystem in a Nutshell"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You need information on:</p>\r\n<ul>\r\n<li>How to get assistance from&#160;SAP,&#160;SAP Partners and the SAP Community</li>\r\n<li>Services offered by SAP: consulting,&#160;remote services, real time support channels</li>\r\n<li>Fundamental and most popular training courses offered by SAP</li>\r\n<li>Detailed information on running SAP software and solutions</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Ecosystem, SAP Solutions, SAP Services, Consulting, Support,&#160;Community, &#160;Contact SAP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>You require information on the SAP ecosystem and resources available to assist with training, education or consulting needs.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Information regarding the fundamental and most popular training courses offered by SAP can be found via the following link:<br /><br /><a target=\"_blank\" href=\"https://training.sap.com/\">https://training.sap.com/</a><br /><br />In addition, SAP has released the document: \"The SAP Ecosystem in a Nutshell\", which contains information of SAPs various resources and can be found via the following link:<br /><br /><a target=\"_blank\" href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/incidents/sap-ecosystem.pdf\">https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/incidents/sap-ecosystem.pdf</a><br /><br />The document will be updated on a quarterly basis or as required.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I822835)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I859306)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001054121/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001054121/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "87655", "RefComponent": "XX-RC", "RefTitle": "SAP Austria consulting request", "RefUrl": "/notes/87655"}, {"RefNumber": "49440", "RefComponent": "XX-RC", "RefTitle": "Consulting Issue - SAP Switzerland", "RefUrl": "/notes/49440"}, {"RefNumber": "423778", "RefComponent": "XX-RC", "RefTitle": "SAP Germany consulting request", "RefUrl": "/notes/423778"}, {"RefNumber": "1559276", "RefComponent": "XX-SER-SELF", "RefTitle": "What are the SAP Mentors, what is Mentoring ?", "RefUrl": "/notes/1559276"}, {"RefNumber": "1271005", "RefComponent": "CO-OM", "RefTitle": "SV142:The Overhead Percentages are missing in the Target Sys", "RefUrl": "/notes/1271005"}, {"RefNumber": "1243085", "RefComponent": "GRC-SAC-ARA", "RefTitle": "10.0 and 5.3 Help Guides deleted from SAP HELP Portal:  Available Documentation for GRC Access Control", "RefUrl": "/notes/1243085"}, {"RefNumber": "1170377", "RefComponent": "SRM-EBP-SHP", "RefTitle": "FAQ Shopping cart: Initial problem analysis on messages.", "RefUrl": "/notes/1170377"}, {"RefNumber": "1094761", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1094761"}, {"RefNumber": "1026369", "RefComponent": "XX-RC-SRM", "RefTitle": "Sources of info for questions about non-product defect", "RefUrl": "/notes/1026369"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3141239", "RefComponent": "FI-LOC-MM-BR", "RefTitle": "Scenarios not supported with Materials Management (MM) in Brazilian Localization", "RefUrl": "/notes/3141239 "}, {"RefNumber": "1770578", "RefComponent": "MM-IM-GI-DET", "RefTitle": "Error M7021 in VL02N when posting a goods issue", "RefUrl": "/notes/1770578 "}, {"RefNumber": "3147580", "RefComponent": "FI-LOC-MM-BR", "RefTitle": "Mixed scenario of Future Delivery Purchase Order with Planned Freight is not supported in Brazilian Localization", "RefUrl": "/notes/3147580 "}, {"RefNumber": "2463319", "RefComponent": "FI-LOC-FI-AR-REP", "RefTitle": "Argentina Reporting Troubleshooting Guide", "RefUrl": "/notes/2463319 "}, {"RefNumber": "3009695", "RefComponent": "BC-SRV-GBT-PPF", "RefTitle": "How-to define a default sender address when using Smartforms", "RefUrl": "/notes/3009695 "}, {"RefNumber": "2997230", "RefComponent": "XX-PART-ISW", "RefTitle": "SAP Products by insightsoftware (previously known as Magnitude)", "RefUrl": "/notes/2997230 "}, {"RefNumber": "2982102", "RefComponent": "BC-JAS-SEC-UME", "RefTitle": "AS Java UME user is being locked frequently - Best Practices to find out the reason", "RefUrl": "/notes/2982102 "}, {"RefNumber": "2476151", "RefComponent": "CA-FE-FLP-EU", "RefTitle": "Custom UI5 app fails to open after upgrading from SAPUI5 1.28 to 1.44 - SAP Fiori Launchpad", "RefUrl": "/notes/2476151 "}, {"RefNumber": "2902113", "RefComponent": "FI-LOC-LO-MX", "RefTitle": "Mexico edocument solution CFDi and SAP Process Integration", "RefUrl": "/notes/2902113 "}, {"RefNumber": "2878698", "RefComponent": "KM-SEN", "RefTitle": "How do I submit a product enhancement or feature request for SAP Enable Now?", "RefUrl": "/notes/2878698 "}, {"RefNumber": "2838274", "RefComponent": "FI-LOC-FI-CL-REP", "RefTitle": "Sales Ledger (RFCLLIB03) Report:End of Support", "RefUrl": "/notes/2838274 "}, {"RefNumber": "2699960", "RefComponent": "CEC-PRO-GIY", "RefTitle": "Best Practice: Exporting User Data from Customer Data Cloud", "RefUrl": "/notes/2699960 "}, {"RefNumber": "2767572", "RefComponent": "FI-LOC-MM-BR", "RefTitle": "Tax values of accounting document x its cancellation do not match", "RefUrl": "/notes/2767572 "}, {"RefNumber": "2703314", "RefComponent": "CEC-PRO", "RefTitle": "How can I perform a bulk data update of user records in the Gigya database?", "RefUrl": "/notes/2703314 "}, {"RefNumber": "2730608", "RefComponent": "EPM-BPC-MS", "RefTitle": "How to automate the EPM Add-in installation for multiple machines", "RefUrl": "/notes/2730608 "}, {"RefNumber": "2541600", "RefComponent": "XX-CSC-CL-FI", "RefTitle": "Purchase Ledger Report - End of Support", "RefUrl": "/notes/2541600 "}, {"RefNumber": "2712751", "RefComponent": "FI-LOC-FI-RU", "RefTitle": "SAP RU-FI: Sales/Purchase Ledger TIME_OUT dump", "RefUrl": "/notes/2712751 "}, {"RefNumber": "2354696", "RefComponent": "MOB-APP-BI-IOS", "RefTitle": "Steps and prerequisites to setup BI Mobile Kerberos SSO Best Practice KBA", "RefUrl": "/notes/2354696 "}, {"RefNumber": "1739330", "RefComponent": "MM-PUR-GF-RE", "RefTitle": "Reports run in background only contain the default layout", "RefUrl": "/notes/1739330 "}, {"RefNumber": "2622713", "RefComponent": "BI-BIP-ADM", "RefTitle": "SAP Scope of Support for QueryBuilder, AdminTools or other CMS Database Queries", "RefUrl": "/notes/2622713 "}, {"RefNumber": "2580764", "RefComponent": "XX-PART-OPT", "RefTitle": "How to request an OpenText Knowledge Center Login", "RefUrl": "/notes/2580764 "}, {"RefNumber": "2261982", "RefComponent": "CRM-BF-DCN-CLN", "RefTitle": "The new value for the status field is not allowed, error when saving a task with CRM Desktop Connection.", "RefUrl": "/notes/2261982 "}, {"RefNumber": "2501040", "RefComponent": "SV-SMG-SUP", "RefTitle": "Custom field in Time Reporting Assignment Block for Incidents in SM_CRM does not read or write data", "RefUrl": "/notes/2501040 "}, {"RefNumber": "2528599", "RefComponent": "IS-R-BD-ART", "RefTitle": "MM41: tax Indicator & Control Code fields are missing", "RefUrl": "/notes/2528599 "}, {"RefNumber": "2519546", "RefComponent": "MM-IV-LIV-CRE", "RefTitle": "Different Number Range required for Credit Memo in case of Purchase Return", "RefUrl": "/notes/2519546 "}, {"RefNumber": "2515015", "RefComponent": "SCM-APO-MD", "RefTitle": "SCM APO Consulting incidents - Important links and channels", "RefUrl": "/notes/2515015 "}, {"RefNumber": "2510926", "RefComponent": "PSM-GPR", "RefTitle": "Confirmation and Invoice are not possible in PPS", "RefUrl": "/notes/2510926 "}, {"RefNumber": "2492089", "RefComponent": "IS-R-BD-ART", "RefTitle": "RETAIL: Change range number of variant article", "RefUrl": "/notes/2492089 "}, {"RefNumber": "2468113", "RefComponent": "AC-INT", "RefTitle": "Error F5A192 occurs when posting cross company posting with BAPI", "RefUrl": "/notes/2468113 "}, {"RefNumber": "2444390", "RefComponent": "BC-SYB-SQA-ML", "RefTitle": "How to limit the size of the Mobilink upload stream to overcome memory constraints on the Mobilink server machine", "RefUrl": "/notes/2444390 "}, {"RefNumber": "2443699", "RefComponent": "MOB-FC", "RefTitle": "How to configure SSO on Fiori Client of mobile devices ?", "RefUrl": "/notes/2443699 "}, {"RefNumber": "3117294", "RefComponent": "HAN-DB-CLI", "RefTitle": "How-To Troubleshoot SQL Error -10807 'Connection down' in a SAP HANA Landscape", "RefUrl": "/notes/3117294 "}, {"RefNumber": "3059060", "RefComponent": "HAN-DB-CLI", "RefTitle": "How To Troubleshoot SQL Error: -10709 'Connection failed' in a SAP HANA Landscape", "RefUrl": "/notes/3059060 "}, {"RefNumber": "2182103", "RefComponent": "HAN-AS-RPO", "RefTitle": "Troubleshoot HTTP(S) Error 503, 404, 403, 401 or a Blank Page when Accessing a SAP HANA Database via XS Classic", "RefUrl": "/notes/2182103 "}, {"RefNumber": "2375304", "RefComponent": "SCM-EWM-DLP", "RefTitle": "Deleted queues from SMQ2", "RefUrl": "/notes/2375304 "}, {"RefNumber": "826037", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache support", "RefUrl": "/notes/826037 "}, {"RefNumber": "1642530", "RefComponent": "XX-RC", "RefTitle": "Remote consulting - availability", "RefUrl": "/notes/1642530 "}, {"RefNumber": "49440", "RefComponent": "XX-RC", "RefTitle": "Consulting Issue - SAP Switzerland", "RefUrl": "/notes/49440 "}, {"RefNumber": "1243085", "RefComponent": "GRC-SAC-ARA", "RefTitle": "10.0 and 5.3 Help Guides deleted from SAP HELP Portal:  Available Documentation for GRC Access Control", "RefUrl": "/notes/1243085 "}, {"RefNumber": "423778", "RefComponent": "XX-RC", "RefTitle": "SAP Germany consulting request", "RefUrl": "/notes/423778 "}, {"RefNumber": "87655", "RefComponent": "XX-RC", "RefTitle": "SAP Austria consulting request", "RefUrl": "/notes/87655 "}, {"RefNumber": "1559276", "RefComponent": "XX-SER-SELF", "RefTitle": "What are the SAP Mentors, what is Mentoring ?", "RefUrl": "/notes/1559276 "}, {"RefNumber": "1026369", "RefComponent": "XX-RC-SRM", "RefTitle": "Sources of info for questions about non-product defect", "RefUrl": "/notes/1026369 "}, {"RefNumber": "1271005", "RefComponent": "CO-OM", "RefTitle": "SV142:The Overhead Percentages are missing in the Target Sys", "RefUrl": "/notes/1271005 "}, {"RefNumber": "1170377", "RefComponent": "SRM-EBP-SHP", "RefTitle": "FAQ Shopping cart: Initial problem analysis on messages.", "RefUrl": "/notes/1170377 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}