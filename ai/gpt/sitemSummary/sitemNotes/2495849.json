{"Request": {"Number": "2495849", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 548, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019675302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002495849?language=E&token=3A633964994270F3F2D59134183702AC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002495849", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002495849/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2495849"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.08.2019"}, "SAPComponentKey": {"_label": "Component", "value": "CA-MDG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Master Data Governance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data Governance", "value": "CA-MDG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2495849 - SAP S/4HANA Master Data Governance 1709: Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note&#160;contains important information for customers using SAP S/4HANA Master Data Governance 1709.<br />This note may be subject to changes. Make sure that you always use the latest version of the note. Changes that have been added after the first release of the note are contained in section: \"Changes after general availability of SAP S/4HANA Master Data Governance 1709\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Master Data Governance, MDG_APPL 802, MDG_UX 802, MDG_FND 802,</p>\r\n<p><PERSON>ori for Master Data Governenace, UIMDG001 100, UIMDG001 200, UIMDC001 100, UIMDG001 200</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You implement SAP S/4HANA Master Data Governance 1709.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In case of a new installation of please read note 2527783.</p>\r\n<p>Please note that certain master data fields as well as values for master data fields have been disabled in SAP S/4HANA On Premise. Fields and values which have been disabled&#160;can neither be maintained in nor distributed from SAP Master Data Governance when implemented on SAP S/4HANA On Premise. For details on disabled fields and changes to allowed values in SAP S/4HANA please see : <a target=\"_blank\" href=\"https://help.sap.com/doc/4698ca4ad85a4a24994b2016f366cc77/1709%20000/en-US/SIMPL_OP1709.pdf\">Simplification List for SAP S/4HANA, on-premise edition 1709.</a></p>\r\n<p>In case of software component MDG_MDC was installed before:&#160;As of S/4HANA 1610 the MDG_MDC functionality is part of software component S4CORE 101 - software component entry MDG_MDC is removed from table CVERS and set to UPDSTATUS=\"-\" in table AVERS.</p>\r\n<p>Customers using Master Data Governance with MDG 9.1 with S/4 HANA On Premise 1709 should read the following notes and implement them after importing the support package.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"><strong>Netweaver</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2507293</td>\r\n<td>BC-EIM-ESH</td>\r\n<td>3</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"><strong>Master Data Governance, Foundation</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2515706</td>\r\n<td>CA-MDG-AF</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2506783</td>\r\n<td>CA-MDG-AF</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2504210</td>\r\n<td>CA-MDG-AF</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2496655</td>\r\n<td>CA-MDG-RIF</td>\r\n<td>2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"><strong>Master Data Governance, Business Partner, Supplier, Customer</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2489305</td>\r\n<td>AP-MD-BP</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2440498</td>\r\n<td>CA-MDG-APP-BP</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2533258</td>\r\n<td>CA-MDG-APP-BP</td>\r\n<td>3</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"><strong>Master Data Governance for Financials</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2508685</td>\r\n<td>CA-MDG-APP-FIN</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2495162</td>\r\n<td>CA-MDG-APP-FIN</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2405590</td>\r\n<td>CA-MDG-APP-FIN</td>\r\n<td>2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"><strong>Master Data Governance for Material</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2511440</td>\r\n<td>CA-MDG-APP-MM</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2508799</td>\r\n<td>CA-MDG-APP-CLF</td>\r\n<td>2</td>\r\n</tr>\r\n<tr>\r\n<td>2501067</td>\r\n<td>CA-MDG-APP-MM</td>\r\n<td>2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"3\"><strong>MDG Fiori Apps</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2491944</td>\r\n<td>CA-MDG-UI5</td>\r\n<td>2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>For details concerning the Master Data Governance Fiori frontend installation please do read note 2327935.</p>\r\n<p><strong>IMPORTANT INFORMATION for customers with UIMDC001 100 software component </strong><span style=\"text-decoration: underline;\"><strong>previously installed:</strong></span></p>\r\n<p>UIMDC001&#160;has been retrofitted into the UIS4HOP1 300 component of the Fiori Frontend product version SAP FIORI FOR SAP S/4HANA 1709 which is&#160;needed for all SAP S/4HANA 1709 customers.&#160; Therefore you&#160;must first proceed with uninstallation of the UIMDC001 100 component (Note 2230429) BEFORE&#160;proceeding with installation of Product version SAP FIORI FOR SAP S/4HANA 1709.&#160; This will not result in any loss of functionality once SAP FIORI FOR SAP S/4HANA 1709 product is installed.&#160;Deinstallation of the higher version UIMDC001 200 is not mandatory, but possible after manual migration of the configuration from UIMDC001 to UIS4HOP1.</p>\r\n<p>If you plan to have both backend ERP installations (classical and S/4HANA)&#160;<strong>AND</strong>&#160;a central gateway Hub for Fiori deployment, then you must ensure that you have two separate clients on your gateway system for&#160;UI for MDG Consolidation and Mass Processing.&#160; Using the same client is not possible as the delivered apps use the same sematic object.</p>\r\n<p>&#160;</p>\r\n<p><strong><strong>Changes after general availability of SAP S/4HANA Master Data Governance 1709</strong></strong></p>\r\n<p>The following note is relevant from Netweaver:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Priority</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2800701</td>\r\n<td>BC-CUS-TOL-BCD</td>\r\n<td>3</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041268)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019437)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002495849/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495849/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2527783", "RefComponent": "CA-MDG", "RefTitle": "SAP S/4HANA Master Data Governance 1709 FPS0 missing translation", "RefUrl": "/notes/2527783"}, {"RefNumber": "2482453", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Release Information Note", "RefUrl": "/notes/2482453"}, {"RefNumber": "2472845", "RefComponent": "CA-MDG-APP-CUS", "RefTitle": "Functional restrictions in MDG for Business Partner / Customer / Supplier with SAP Master Data Governance 9.1", "RefUrl": "/notes/2472845"}, {"RefNumber": "2461516", "RefComponent": "CA-MDG-APP-MM", "RefTitle": "Functional Restrictions in MDG for Material with SAP Master Data Governance 9.1", "RefUrl": "/notes/2461516"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2392135", "RefComponent": "CA-MDG", "RefTitle": "SAP Master Data Governance on SAP S/4HANA (central governance) in combination with certain business functions for industries", "RefUrl": "/notes/2392135 "}, {"RefNumber": "2590829", "RefComponent": "CA-MDG-CMP", "RefTitle": "Installation & Configuration for \"SAP Master Data Governance\"", "RefUrl": "/notes/2590829 "}, {"RefNumber": "2568323", "RefComponent": "XX-PART-UGI", "RefTitle": "SAP Master Data Governance, enterprise asset management extension by Utopia 9.1 for S/4HANA Release Information Note", "RefUrl": "/notes/2568323 "}, {"RefNumber": "2568331", "RefComponent": "XX-PART-UGI", "RefTitle": "SAP Asset Information Workbench by Utopia 2.0 for S/4HANA Release Information Note", "RefUrl": "/notes/2568331 "}, {"RefNumber": "1685823", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for SAP MASTER DATA GOVERNANCE", "RefUrl": "/notes/1685823 "}, {"RefNumber": "2491467", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Restriction Note", "RefUrl": "/notes/2491467 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "MDG_FND", "From": "802", "To": "802", "Subsequent": ""}, {"SoftwareComponent": "MDG_APPL", "From": "802", "To": "802", "Subsequent": ""}, {"SoftwareComponent": "MDG_UX", "From": "802", "To": "802", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}