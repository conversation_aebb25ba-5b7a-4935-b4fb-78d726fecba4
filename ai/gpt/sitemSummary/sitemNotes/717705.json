{"Request": {"Number": "717705", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 279, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015630122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000717705?language=E&token=FE1FB90E40A573C01B1E7CADFA88BF4D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000717705", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000717705/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "717705"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.03.2004"}, "SAPComponentKey": {"_label": "Component", "value": "PY-GB-GR-AP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Absence payments (SSP, SMP, SAP, SPP)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "United Kingdom", "value": "PY-GB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-GB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Gross", "value": "PY-GB-GR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-GB-GR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Absence payments (SSP, SMP, SAP, SPP)", "value": "PY-GB-GR-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-GB-GR-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "717705 - AWE calculations using the current period's gross"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The legislation concerning Average Weekly Earnings (AWE) for Statutory<br />Sick Pay (SSP) states that employers should include the regular<br />payments dates before the PIW started, back eight weeks. This implies<br />that if a PIW starts after the paydate of the current period (which will<br />occur occasionally even for month-end pay dates) then the current gross<br />payments for this period should be included in the AWE calculation.<br /><br />This can lead to problems.<br /><br />1) GBSXP is delivered during up to gross GT00<br /><br />&#x00A0;&#x00A0; At this stage many payments have not been \"part period factored\" due<br />&#x00A0;&#x00A0; to the sickness and so the values in table IT will be \"overstated\"<br />&#x00A0;&#x00A0; if they are included in the total AWE'able gross.<br /><br />&#x00A0;&#x00A0; There are two solutions to get round this problem;<br />&#x00A0;&#x00A0; a) move GBSXP to after part period factoring has occured.<br />&#x00A0;&#x00A0; b) ignore the current period in AWE calculation for SSP.<br /><br />2) SSP itself makes up part of the AWE for the current period.<br /><br />&#x00A0;&#x00A0; It is quite feasible that an employee could have run out of<br />&#x00A0;&#x00A0; Occupational Sick Pay (OSP) entitlements or could have SSP paid in<br />&#x00A0;&#x00A0; addition to reduced pay Occupational Sick Pay (i.e. Half Pay OSP).<br />&#x00A0;&#x00A0; In this case, the SSP paid in the period will make up part of the<br />&#x00A0;&#x00A0; employee's gross pay. But how can the payroll pay the employee SSP<br />&#x00A0;&#x00A0; if it needs to know how much SSP it has paid, before it can pay it!<br /><br />&#x00A0;&#x00A0; There is no suitable solution to this get round this \"Catch 22\"<br />&#x00A0;&#x00A0; situation. The solution cannot know whether the SSP paid will have<br />&#x00A0;&#x00A0; an effect on the AWE calculation - specially for low earners!<br /><br />&#x00A0;&#x00A0; The only possibility to get round this problem is to ignore the<br />&#x00A0;&#x00A0; current period in AWE calculation for SSP.<br /><br />3) AWE is made up from payments made on more than one contract of<br />&#x00A0;&#x00A0; employment and the other contracts have not been payrolled yet.<br /><br />&#x00A0;&#x00A0; This is only an issue for customers using the Multiple Employment<br />&#x00A0;&#x00A0; solution. The problem lies in that the AWE cannot be known until<br />&#x00A0;&#x00A0; payroll has actually been run. This is similar to problem 2 above.<br />&#x00A0;&#x00A0; In theory, an approximation would be possible, but then when the<br />&#x00A0;&#x00A0; payroll was actually run the approximation would be corrected and<br />&#x00A0;&#x00A0; a different result could be calculated. This problem could mean<br />&#x00A0;&#x00A0; the difference between exclusion due to low AWE or not - for low<br />&#x00A0;&#x00A0; earners.<br /><br />&#x00A0;&#x00A0; The only possibility to get round this problem is to ignore the<br />&#x00A0;&#x00A0; current period in AWE calculation for SSP.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>GBSXP AVERA T51AV_A</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legislation conflicts with practical payroll processing</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Change payroll customising<br /><br />Do not include the current period in the AWE calculation for SSP.<br /><br />Table T51AV_A: \"Untick\" the checkbox \"Current Period\" against the<br />Average Rule \"SAWE\".<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;**********<br /><br />This action will make the payroll ignore the current period if the<br />paydate should, in theory, be included in the relevant period. This<br />changes the AVERA solution for SSP to make it revert to the same way<br />that the \"old\" AWE solution (using operation MEANVG etc) used to work<br />before the implementation of AVERA as the \"old\" AWE solution used to<br />ignore the current period.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I019233)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I019889)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000717705/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364"}, {"RefNumber": "0", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/0"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2594970", "RefComponent": "PY-GB", "RefTitle": "PY-GB: GBSXP: AWE including or excluding the current period", "RefUrl": "/notes/2594970 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}