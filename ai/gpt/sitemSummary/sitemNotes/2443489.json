{"Request": {"Number": "2443489", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 268, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018757922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002443489?language=E&token=C9E98D9EFB4AEE98CCFE350B8D56EFC4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002443489", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002443489/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2443489"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.01.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BW-B4H-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Conversion to SAP BW/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP BW/4HANA Starter Add-On", "value": "BW-B4H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Conversion to SAP BW/4HANA", "value": "BW-B4H-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2443489 - BW4SL & BWbridgeSL - InfoCubes"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The SAP BW InfoCube is not available in SAP BW/4HANA and SAP Data Warehouse Cloud, SAP BW bridge. Basic InfoCubes can be converted to a DataStore Object (advanced) (ADSO) with option \"All characteristics are key, reporting on union of inbound and active table\" using the BW4 Transfer Tool.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CUBE, DCUB</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available in or can be converted to SAP BW/4HANA:</p>\r\n<p>See node: Supported by Transfer Tool --&gt; BW4 Transfer Tool (using transaction RSB4HTRF).</p>\r\n<p>For the in-place conversion to SAP BW/4HANA your system needs to be on SAP BW 7.5 SP 5, powered by SAP HANA or higher.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Under node Supported by Transfer Tool --&gt; BW4 Transfer Tool --&gt; TLOGO CUBE (InfoCube), the Basis InfoCubes are listed. They can be transferred using the Transfer Tool&#160;\"in-place\" or via \"remote conversion\" to DataStore objects (advanced).</p>\r\n<p>Transfer:</p>\r\n<ul>\r\n<li>The Transfer Tool will automatically replace process chain variant \"Compression of InfoCubes\"&#160;with process chain variant \"Clean Up Old Requests in DataStore objects (advanced)\".</li>\r\n<li>DataStore objects (advanced) expose navigation attributes for staging processes only. It is not possible to consume navigation attributes of an ADSO directly in the query. The Transfer Tool will therefore automatically generate a CompositeProvider that enables the navigation attributes of the ADSO and redirects the query to it. See also&#160;SAP Note <a target=\"_blank\" href=\"/notes/**********\">2185212</a>.</li>\r\n<li>For InfoCubes with non-cumulative key figures and near-line storage solution (NLS), see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2618005\">2618005</a>.</li>\r\n<li>InfoCubes with an audit dimension, i.e. InfoCubes for which the checkbox 'Auditable' flag has been checked, can be converted. But it is only a technical convesion, the audit InfoObject filed will be deleted.&#160;</li>\r\n</ul>\r\n<p>Restrictions:</p>\r\n<ul>\r\n<li>InfoCubes of type VirtualProvider or HybridProvider cannot be converted (see SAP Notes <a target=\"_blank\" href=\"/notes/2444913\">2444913</a> and <a target=\"_blank\" href=\"/notes/2442730\">2442730</a>)</li>\r\n<li>The DataStore object (advanced) support \"zero elimination\" starting with SAP BW/4 2.0 Sp04.&#160;</li>\r\n<li>Transfer of InfoCubes with key figure aggregation type NO2 is not supported. Manual redesign required before transfer.</li>\r\n<li>SID materialization: ADSOs are transferred from InfoCubes without generating materialized SID columns. This&#160;might result in impaired&#160;performance in some cases. SID materialization can be enabled for selected characteristics containing mass data after the transfer, which will cause a remodeling job.</li>\r\n<li>Partitioning: InfoCubes are distributed across the SAP HANA nodes round-robin; ADSOs are distributed using a hash function. The fields that will be hashed are determined automatically, but can be changed after the transfer.&#160;</li>\r\n<li>InfoCubes on SAP HANA that are not HANA-optimized cannot be converted. HANA-optimized these InfoCubes using transaction RSMIGRHANADB.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA-ICUB (InfoCubes)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041133)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D051264)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002443489/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002443489/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2744335", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Preparation for zero elimination during activation", "RefUrl": "/notes/2744335"}, {"RefNumber": "2618005", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving advanced Data Store Objects with non-cumulative key figures", "RefUrl": "/notes/2618005"}, {"RefNumber": "2444913", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - VirtualProviders", "RefUrl": "/notes/2444913"}, {"RefNumber": "2442730", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - HybridProviders", "RefUrl": "/notes/2442730"}, {"RefNumber": "2421930", "RefComponent": "BW-B4H-CNV", "RefTitle": "Simplification List for SAP BW/4HANA", "RefUrl": "/notes/2421930"}, {"RefNumber": "2185212", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "ADSO: Recommendations and restrictions regarding reporting", "RefUrl": "/notes/2185212"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}