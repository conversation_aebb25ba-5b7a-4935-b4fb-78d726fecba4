{"Request": {"Number": "2357837", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 310, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018387192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002357837?language=E&token=75A50BC1E48A4FC71E6465ADBE775486"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002357837", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2357837"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.09.2016"}, "SAPComponentKey": {"_label": "Component", "value": "IS-MP-MM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Enhancements Materials Management (MM)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mill Products", "value": "IS-MP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-MP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enhancements Materials Management (MM)", "value": "IS-MP-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-MP-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2357837 - SAP S/4HANA: Material type simplification for material master of Mill Products."}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In <a target=\"_blank\" href=\"https://www.sapbrandtools.com/naming-center/#/requests/584\">SAP ERP Central Component</a>, 7 material types (MCFE, MCRO, MCHA, MRM1, <PERSON>O1, MRM3, MCHF) were delivered as part of industry solution Mill Products (activated through business function DIMP_SDUD). In <a target=\"_blank\" href=\"https://www.sapbrandtools.com/naming-center/#/requests/5130\">SAP S/4HANA</a> 1610, these 7 material types are not available as part of simplification.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Cable, Reel, Material, Trading, Goods, Returnable, Semi, Finished, Raw, Product, <a target=\"_blank\" href=\"https://www.sapbrandtools.com/naming-center/#/requests/5130\">SAP S/4HANA</a>, Mill, MCFE, MCRO, MCHA, MRM1, MRO1, <PERSON>M3, <PERSON><PERSON></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Mill Products is now part of the <a target=\"_blank\" href=\"https://www.sapbrandtools.com/naming-center/#/requests/5130\">SAP S/4HANA</a> Enterprise Management. For simplification in SAP S/4HANA 1610 the Mill Products specific material types are not available in client 000 to avoid having many unused material types. This approach gives you the flexibility to use any material type of your requirement.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Customers who want to maintain additional cable and reel master data, for example, cable diameter or reel dimensions, can enhance material master with cable specific screens (optional). It&#8217;s mandatory for customers requiring the functionality &#8220;reel calculation&#8221; as some cable and reel fields are used. To enable the additional screens, you can define new material types, for example, MCFE (copy of FERT) or you enhance customizing of existing material types, for example, FERT.</p>\r\n<p><strong>Approach 1: Create new material type</strong></p>\r\n<p><strong>Approach 1.1:</strong></p>\r\n<p><strong>Use existing screen sequence</strong></p>\r\n<p>If you are a new SAP customer in cable industry and want to separate cable and non-cable materials (or cable relevant for &#8220;reel calculation&#8221;), then you have to create new material types in Customizing:</p>\r\n<p>1. Create a new material type in customizing:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Basic Settings -&gt; Material Types -&gt; Define Attributes of Material Types&#8221;</p>\r\n<p>Example: MCFE = Mill Cable Finished Prdts</p>\r\n<p>2. Create and assign new screen reference to material type:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Assign Screen Sequences to User/Material Type/Transaction/Industry Sector&#8221;</p>\r\n<p>Choose material type screen reference.</p>\r\n<p>Example: MCCA screen reference is assigned to MCFE</p>\r\n<p>3. Assign screen reference material type to the shipped screen sequence (MC, MR, and MW):</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Assign Screen Sequences to User/Material Type/Transaction/Industry Sector&#8221;</p>\r\n<p>Choose screen sequence control.</p>\r\n<p>MC = MillCa Cable</p>\r\n<p>MR = MillCa Reel</p>\r\n<p>Example: MCCA screen reference is assigned to MC.</p>\r\n<p>Result:</p>\r\n<p>While creating a new material master (transaction MM01) with material type MCFE, the user can select the &#8220;Cable data&#8221; view (enabled through MC screen sequence) to enter additional cable master data.</p>\r\n<p>The same procedure to be followed for setting up material types for the view &#8220;Reel Data&#8221; (enabled through MR screen sequence).</p>\r\n<p>&#160;</p>\r\n<p><strong>Approach 1.2:</strong></p>\r\n<p><strong>Create New Screen Sequence</strong></p>\r\n<p>If you have a specific requirement for a particular set of Logical (Data) Screens which are not shipped together in any screen sequence by default, you can customize your own screen sequence by copying relevant Logical (Data) Screens from various screen sequences.</p>\r\n<p>1. Create a new screen sequence:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Define Structure of Data Screens for Each Screen Sequence &#8220;</p>\r\n<p>Example:&#160; Create a copy of MC, name it ZMC and add additional Data screens to it from other screen sequences like 21, 23, 24 or remove Data screens according to the requirement.</p>\r\n<p>2. Create material types:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Basic Settings -&gt; Material Types -&gt; Define Attributes of Material Types&#8221;</p>\r\n<p>Example: MCFE = Mill Cable Finished Prdts</p>\r\n<p>3. Create and assign new screen reference to material type:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Assign Screen Sequences to User/Material Type/Transaction/Industry Sector&#8221;</p>\r\n<p>Choose material type screen reference.</p>\r\n<p>Example: MCCA screen reference is assigned to MCFE</p>\r\n<p>4. Assign screen reference material type to the new screen sequence in Customizing:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Assign Screen Sequences to User/Material Type/Transaction/Industry Sector&#8221;</p>\r\n<p>Choose screen sequence control.</p>\r\n<p>Example: MCCA screen reference is assigned to ZMC.</p>\r\n<p>Result:</p>\r\n<p>While creating a new material master (transaction MM01) with material type MCFE, the user can select the &#8220;Cable data&#8221; (enabled through ZMC screen sequence) view to enter additional cable dimension data.</p>\r\n<p>The same procedure to be followed for setting up material types for the view &#8220;Reel Data&#8221;.</p>\r\n<p>&#160;</p>\r\n<p><strong>Approach 2: Enhance Existing Material Type</strong></p>\r\n<p>1. Find your screen sequence in Customizing:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Assign Screen Sequences to User/Material Type/Transaction/Industry Sector&#8221;</p>\r\n<p>Choose material type screen reference. Note the &#8220;SRef: matl type&#8221; linked to the material type you are using.</p>\r\n<p>Example: FERT material type is linked to ROH &#8220;SRef: matl type&#8221;</p>\r\n<p>Now choose Screen sequence control. Note &#8220;SSq&#8221; linked to the &#8220;SRef: matl type&#8221;.</p>\r\n<p>Example: ROH is linked to 21 screen sequence</p>\r\n<p>2. Enhance screen sequence with relevant cable data or reel data logical screens:</p>\r\n<p>&#8220;Logistics &#8211; General -&gt; Material Master -&gt; Configuring the Material Master -&gt; Define Structure of Data Screens for Each Screen Sequence &#8220;</p>\r\n<p>Example: Add C1 Data screen (Cable data)/ R1 Data screen (Reel data) to the screen sequence 21 by copying from MC/MR screen sequence.</p>\r\n<p>Result:</p>\r\n<p>While creating a new material master (transaction MM01) with material type FERT, the user can select the &#8220;Cable data&#8221; or &#8220;Reel data&#8221; (enabled through 21 screen sequence) view to enter additional cable/reel dimension data.</p>\r\n<p><strong>Important for customers undergoing system conversion:&#160;</strong>Existing SAP ERP customers undergoing system conversion to SAP S/4HANA 1610 can still work with the old material types in the productive client.</p>\r\n<p>The mill material types and their linking to the screen sequence is not available in client 000. To have customizing synchronization between the client 000 and productive client you can re-create standard material types MCFE, MCRO, MCHA, MRM1, MRO1, MRM3, MCHF and link them to screen sequences like MC, MR, MW or your own screen sequences as mentioned in the approaches above in the client 000.</p>\r\n<p>For more information about the Material types, see:</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/s4hana_op_1610\">http://help.sap.com/s4hana_op_1610</a> -&gt; Enterprise Business Applications -&gt; Sourcing and Procurement -&gt; Materials Management (MM)</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I320873"}, {"Key": "Processor                                                                                           ", "Value": "Kanakaraj V A (I065014)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002357837/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2698659", "RefComponent": "IS-MP", "RefTitle": "S/4HANA for Cable industry - Solution and Implementation facts", "RefUrl": "/notes/2698659 "}, {"RefNumber": "2357870", "RefComponent": "IS-MP-NF", "RefTitle": "SAP S/4HANA: Settings to enable Non Ferrous Metals data in material master", "RefUrl": "/notes/2357870 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}