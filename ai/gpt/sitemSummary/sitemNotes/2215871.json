{"Request": {"Number": "2215871", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 662, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018163522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002215871?language=E&token=BA57B96D3840DD32C213E9ABB79207DC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002215871", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002215871/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2215871"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.09.2020"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLE-MAT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Field Lenght Extension for Material"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Application Field Lenght Extension", "value": "CA-FLE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Field Lenght Extension for Material", "value": "CA-FLE-MAT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE-MAT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2215871 - Material Number Field Length Extension - Data Migration"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The material&#160;number and related data types&#160;are extended. In certain cases this requires some actions for stored data<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: '<PERSON><PERSON><PERSON>',sans-serif; color: #1f497d; mso-fareast-font-family: PMingLiU; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: ZH-TW; mso-bidi-language: AR-SA;\">.</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Material Number Field Length Extension, MATNR, ATWRT, Classification, Material Number, Data Migration, XPRA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP S/4HANA, the material number field length was extended from 18 to 40 characters. This change was first implemented in SAP S/4HANA, on-premise edition 1511 and affects this release and higher releases.</p>\r\n<p>If you convert from a system in which the DIMP long material number functionality was switched on, you should additionally consult SAP note&#160;<a target=\"_blank\" href=\"/notes/2381633\">2381633</a> as the data migration in this case has to take into account that also the internally used value of the material number changes and therefore data migration is necessary in more cases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Native Material Number Fields</strong></p>\r\n<p>For native material number fields, no data migration is necessary. Although the maximum length of these fields will be extended&#160;by database means, how these fields are stored on the database&#160;remains unchanged.</p>\r\n<p>This is also true for purely numeric material numbers. With basic configuration (leading zeroes not significant, lexicographic flag not set), these numbers are displayed in the UI without leading zeroes, but are stored on the database with leading zeroes to ensure proper sorting. For material numbers, this filling is only done up to a length of 18 characters, not up to the full length of 40 characters. This limits the possible values of numeric material numbers, but has the advantage that the old database representation is still valid and no conversion is needed.</p>\r\n<p>Besides the material number itself, other fields also had to be extended.&#160;This had to be done if a material number is stored&#160;in a database field for some&#160;records, while in other records the field contains other values. These fields also do not require conversion because the database representation of the material number does not change.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number functionality, changes of the content that&#160;is stored on the database for the material number will be needed. See SAP note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> for further information.</p>\r\n<p><strong>Concatenated Field Content</strong></p>\r\n<p>In some tables, the material number&#160;is not stored as a single field by itself, but as one part of the content of a generic field. This is common for frameworks that provide a generic key field but are not aware of the specific object and its key content that is using the framework.</p>\r\n<p>Usually these concatenated keys are constructed by moving a structure to a single key field or by another method that depends on the technical length of the fields that are concatenated.&#160;If the material number&#160;is not at the end of such a structure, this results in content of&#160;fields \"behind\" the material number being shifted to the right&#160;by 22 characters.</p>\r\n<p>If such a field is stored&#160;in the database, it has to be converted to the new representation, because otherwise the content would be interpreted wrongly if the field is read from the database and parsed to the new representation. For the material number, this means that 22 spaces need to be added&#160;only at the old offset of the \"next\" field after the material number.</p>\r\n<p>Note: In most cases, the length of such a field also had to be extended because the field could not hold additional 22 spaces.</p>\r\n<p>If you store values in the similar way in your own tables, you have to take into account that a customer specific conversion is usually&#160;needed. If you store your own values in a framework that provides such a key, it&#160;may also be necessary to convert exactly this content because the conversion programs delivered by SAP&#160;usually only convert values stored&#160;by SAP standard programs.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, additionally the value of the material number within the concatenate has to be changed.&#160;See note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> for further information.</p>\r\n<p><strong>Framework Tables that store BOR Keys</strong></p>\r\n<p>In some generic framework, the&#160;key of a BOR object is stored. This key is a concatenated field containing the key fields of the BOR object one after another. In these frameworks always the latest version of the BOR key is stored (see also SAP note&#160;<a target=\"_blank\" href=\"/notes/2216654\">2216654</a> for versions of BOR keys). Usually the conversion is done in the same way as already described for concatenated fields. The offset at which 22 spaces have to be added depends on the BOR object type for which the key is stored in the database record.</p>\r\n<p>However, the length of the BOR key could not be extended. For some BOR objects the new length of the key exceeded the available 66 characters. In these cases the key of the BOR object has been replaced by a GUID &#160;(for details see SAP note <a target=\"_blank\" href=\"/notes/2216654\">2216654</a>). For records that store the key of such a BOR object the conversion will replace the old value with the correct new GUID for stored&#160;BOR object instance key.</p>\r\n<p>The following fields storing BOR keys are converted by SAP:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Table</strong></td>\r\n<td><strong>Field</strong></td>\r\n</tr>\r\n<tr>\r\n<td>/PLMB/RCP_RECREL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>/PLMB/RCP_RECREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>/SAPPSPRO/RMRL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>/SAPPSPRO/RMRL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>FDT_ACTN_6200</td>\r\n<td>VALUE</td>\r\n</tr>\r\n<tr>\r\n<td>FDT_ACTN_6200A</td>\r\n<td>VALUE</td>\r\n</tr>\r\n<tr>\r\n<td>FDT_ACTN_6200S</td>\r\n<td>VALUE</td>\r\n</tr>\r\n<tr>\r\n<td>FDT_ACTN_6200T</td>\r\n<td>VALUE</td>\r\n</tr>\r\n<tr>\r\n<td>PLMM_ACT_BREL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>PLMM_ACT_BREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>SFLIBREL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>SFLIBREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>SGOSHIST</td>\r\n<td>OBJKEY</td>\r\n</tr>\r\n<tr>\r\n<td>SKWG_BREL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>SKWG_BREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>SPROXMSGREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>SRBCSBREL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>SRBCSBREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>SRGBTBREL</td>\r\n<td>INSTID_A</td>\r\n</tr>\r\n<tr>\r\n<td>SRGBTBREL</td>\r\n<td>INSTID_B</td>\r\n</tr>\r\n<tr>\r\n<td>SRMALBOR</td>\r\n<td>OBJECTKEY</td>\r\n</tr>\r\n<tr>\r\n<td>SRMALPRBOR</td>\r\n<td>BO_ID</td>\r\n</tr>\r\n<tr>\r\n<td>SROBJINGRP</td>\r\n<td>OBJKEY</td>\r\n</tr>\r\n<tr>\r\n<td>SROBLROLB</td>\r\n<td>OBJKEY</td>\r\n</tr>\r\n<tr>\r\n<td>SRRELROLES</td>\r\n<td>OBJKEY</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>(see also table MFLE_BOR_MAPPER; this table also shows which BOR types are included in the conversion)</p>\r\n<p>If you&#160;store BOR keys of affected objects in own tables it&#160;may also be necessary to convert exactly this content (see also the section on customer tables below).</p>\r\n<p><strong>Pre-Checks Before Migration</strong></p>\r\n<p>Before the migration to SAP S/4HANA, there is one pre-check automatically executed to check that no active ALE pointers are in the system. All processes with ALE change pointers have to be finished manually. ALE change pointers also have a concatenated field for the key definition. Since those pointers should be executed before the upgrade, this data is not converted. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2216559\">2216559</a>.</p>\r\n<p>In addition, the&#160;Custom Code Chapter of the Readiness Check Service Report will give you two additional tasks before the upgrade:</p>\r\n<ul>\r\n<li>The material number extension can influence saved selection variants. These variants can be converted according to SAP Note <a target=\"_blank\" href=\"/notes/1696821\">1696821</a>, but in order to do so, the first step of the SAP Note needs to run in the old version. Please make sure to run those parts of the SAP Note in the old system.</li>\r\n<li>It is <strong>important and required</strong> to run a check program according to SAP Note <a target=\"_blank\" href=\"/notes/2216958\">2216958</a>&#160;in your existing SAP system before starting the migration. This program will not change any data in your system. It will check for customer-defined tables and customer-defined BOR types that need to be converted. Additionally, it will analyze your data to make sure that all framework data will be converted to the new structure. The findings are structured and classified; details are described in the SAP Note. This check is relevant for the extended material number handling.</li>\r\n</ul>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, further pre-checks will be triggered. For further information see note 2194272.</p>\r\n<p><strong>Long Texts</strong></p>\r\n<p>Tables STXH and STXL are converted to the described rules for concatenated fields. The corresponding&#160;conversion program&#160;will do the conversion based on the type of the long text for SAP delivered application object and text ids. Custom application objects or custom text ids on SAP application objects may need to be converted like described below for custom tables.</p>\r\n<p><strong>Generic Object Services (GOS) and Record Management</strong></p>\r\n<p>Those frameworks use the Business Object Repository (BOR) framework as a basis. For example, GOS can be used for an attachment to a BOR object. In this case, the GOS framework will store data which contains the BOR key and the BOR type. If the BOR type definition has a material number&#160;(or any other extended field)&#160;in the key, the stored BOR key in the GOS database will be converted.</p>\r\n<p>If you have own BOR objects stored in GOS for which the key is affected by the extension of the material number, it&#160;may also be necessary to convert exactly this content.</p>\r\n<p><strong>Workflows</strong></p>\r\n<p>Workflow also stores the corresponding BOR object and BOR key in the workflow tables. These key fields have concatenated fields and will therefore change after the migration.</p>\r\n<p>Additionally, data can be stored in any structure in workflow containers.&#160;Because of the material field length extension either fields in these structures might be extended or new fields could be added. Whenever workflow containers are read from the database (e.g. when an already started workflow is continued after the migration to S/4HANA), it has to&#160;be ensured that the data stored on the database is converted to the current format of the structure. Workflow offers&#160;with&#160;S/4HANA, on-premise edition 1511 FPS2&#160;a generic handling for&#160;simple cases, including&#160;the call of MOVE-CORRESPONDING.&#160;For more complex cases, e.g. format changes in one or more fields, a plugin mechanism was introduced in S/4HANA, on-premise edition 1511FPS2 as well.&#160;More information can be found in note <a target=\"_blank\" href=\"/notes/2310255\">2310255</a>.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, also workflows that store material numbers have to be converted. Besides structured parameter this affects especially also scalar parameters in the workflow. See note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> for further information.</p>\r\n<p><strong>BRFplus</strong></p>\r\n<p>In most cases, no conversion is needed with the BRFplus framework. Nevertheless, you need to check your rules and test the functionality.</p>\r\n<p>Tests are especially necessary for rules that use material number, concatenated fields, or BOR objects, such as workflow.</p>\r\n<p>The checks can be done with report FDT_DDIC_WHERE_USED, see attachment 2215871_BRFplus. With this program, you can find specific dictionary types (e.g. MATNR) as well as used BOR objects (&#8220;Action Type&#8221; is equal to &#8220;Start Workflow&#8221; or &#8220;Workflow Event&#8221;). On the result overview, you should check if the keys for the actions are properly used or constructed. This program can also be used to refresh the DDIC binding.</p>\r\n<p>Important:&#160;The content needs to be updated with Refresh DDIC Binding. This has to be done&#160;by the customer for their own content and for copied templates!</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, you should additionally&#160;check your BRFplus rules for the value used. You&#160;probably need to replace&#160;the value of the material number in the rule.&#160;See notes&#160;2270396 and 2270836&#160;and notes referenced therein&#160;for further information</p>\r\n<p><strong>Change Documents</strong></p>\r\n<p>Change documents are not converted, they will be handled&#160;differently.</p>\r\n<p>For a given object&#160;class, a new object class is either created in addition, so the old object&#160;class has the short material number, while the new object&#160;class only has the extended material number.</p>\r\n<p>Or, the&#160;date&#160;is used within the change document to define if the document is created with the short material number or the extended material number.</p>\r\n<p>Change documents are read by the wrapper function MFLE_CHANGEDOCUMENT_READ_ALL. This function reads old change documents with 18 character material numbers&#160;and new data with 40 character material numbers for a given selection criteria and merges them into the new format with 40 characters. MFLE_CHANGEDOCUMENT_READ_ALL has the same API for selection and for giving back result sets like CHANGEDOCUMENT_READ_ALL.</p>\r\n<p>In addition, a function MFLE_READ_PLANNED_CHANGES is also available for the planned change documents.</p>\r\n<p>In case change documents are read by customer coding, the customer has to check if a material number (or any other extended field)&#160;is included in the key field or in the OBJECTID and if the data is selected based on the material number.</p>\r\n<p>If this is the case, an adjustment is necessary.</p>\r\n<p>If the object class of the change document is included in table CDADD, then the read of the change documents can be adjusted to the new function module mentioned above. This table also shows which object classes were changed. Please keep in mind that this will also affect ALE change pointer which now point to the new object class. If you have defined own message types that are triggered by the old objectclass, you need to maintain that the message types are now triggered by the new object class.</p>\r\n<p>If the object class is not included in this table (the application did not use the function), the access of the change data has to be done twice: with the short material number and with the extended material number. In this case, the migration date can be used to separate the time range of both select statements.</p>\r\n<p>If you created your own change documents you may check both options which suits to your case. If you decide to create a new object class make sure that you also adapt follow up functionalities like ALE changepointers to the new change document.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, the value of the material number used for old documents has to be changed as well. If the change document is included in table CDADD, this will be done by the wrapper function. If you created your own change documents you will need to call the appropriate mapper to get the old material number.&#160;See note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> for further information.</p>\r\n<p><strong>Data Archiving</strong></p>\r\n<p>Archived data will not be converted. For normal material number this will not cause any problems.</p>\r\n<p>For applications that have concatenated fields, a BAdI needs to be implemented that allows the transformation of the concatenated value during read access of the archived data. See SAP Note 2382620 for further details.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, also normal material numbers&#160;may&#160;need to be converted during read access. See SAP Note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> for further information.</p>\r\n<p><strong>Customer&#160;</strong><strong>Extensions and Custom Customer Tables</strong></p>\r\n<p>During the migration, this data is not considered and not converted. If the customer has extension fields and/or customer-defined tables with concatenated fields, this data needs to be handled by the customer. Keep in mind that pure material number fields do not need to be converted.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, also normal material numbers&#160;need to be converted. SAP delivers a pre-check that can identify such material number fields and allows to add them to the conversion program delivered by SAP. See SAP Note&#160;2194272 for further information.</p>\r\n<p><strong>Customer-Defined BOR Objects</strong></p>\r\n<p>Data&#160;stored&#160;with BOR object keys of customer-defined BOR objects cannot be converted automatically, even if they are used in a standard framework such as GOS.</p>\r\n<p>This conversion must be triggered by the customer. However, in many cases it should be possible to add the BOR type to a copied version of table MFLE_BOR_MAPPER, and copy and adjust the existing XPRA program MFLE_XPRA_CONCAT_GOS. Nevertheless, this is an extension that needs to be tested.</p>\r\n<p><strong>ArchiveLink </strong></p>\r\n<p>In case ArchiveLink integration was customized for an affected BOR object the BOR keys in the link tables (TOA01/TOA02/TOA03 and custom link tables if applicable) are not converted by default. This can lead to existing attachments becoming inaccessible after the conversion. As the ArchiveLink document ID only supports 50 characters (in contrast to the BOR key which supports 70) additionally new documents can appear to be assigned to multiple instances of a business object due to truncation issues.</p>\r\n<p>A possibility to convert the keys on the fly for both SAP as well as customer-defined BOR objects is provided in SAP Note <a target=\"_blank\" href=\"/notes/2679398\">2679398</a>. This will overcome the truncation issues.</p>\r\n<p>For customer defined BOR objects the conversion of BOR keys can alternatively be performed using the approach outlined in the previous section by running a data conversion on table level. When taking this approach, the key length restriction mentioned before needs to be considered, i.e. the data conversion is only an option for specific business objects having key lengths of 50 characters and less after the field length extension. Furthermore, it needs to be considered that both solution approaches described in this section are mutually exclusive, i.e. per BOR object type only one of them must be used.</p>\r\n<p><strong>Classification (CA-CL)</strong></p>\r\n<p>Entries in the classification tables (e.g. AUSP, INOB, KSSK) are converted according to the described rules for concatenated fields. The corresponding&#160;conversion program&#160;will do the conversion for SAP delivered class types and object tables. For customer-defined class types based on customer tables, entries in the classification&#160;tables&#160;mentioned before, may need to be converted as described in the section on customer tables. The same applies for customer defined class types based on SAP object tables, at least as long as the material number is not the only key-field of the object table.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number, also normal material numbers&#160;may&#160;need to be converted during read access. See SAP Note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> for further information.</p>\r\n<p><strong>Document Management System (CA-DMS)</strong></p>\r\n<p>Entries in the DMS tables (ODATA_CV_ATTACH, CVSE_GUID, DMS_DOC_FOLDER3, DRAD, DRAD_LAST_CHANGE, VEERP_LINKS) are converted according to the described rules for concatenated fields for links to SAP delivered objects. For links to customer defined objects, the corresponding entries in the DMS tables&#160;may need to be converted as described in the section on customer tables.</p>\r\n<p><strong>ALV Layout Variants</strong></p>\r\n<p>Usually ALV layout variants do not require adjustment. In case certain DIMP business functions (e.g. DIMP_SDUD) have been active ALV layout variants in specific reports might refer to a field \"MATNR_EXT\", which no longer exists in S/4HANA, rather than the standard field \"MATNR\". This can cause ALV output to turn up with missing/invisible material number columns. Unfortunately there is no possibility for automatic adjustment of these layout variants, so they need to be adjusted manually as post-processing of the conversion.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LO-MD-M<PERSON> (Material Master)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D048317)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I037481)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002215871/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215871/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "2215871_BRFplus.docx", "FileSize": "405", "MimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001293962015&iv_version=0018&iv_guid=855483841723804E9B190E965E5EC2AA"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2679398", "RefComponent": "CA-FLE-MAT", "RefTitle": "Support for key-conversion in ArchiveLink and in GOS/ArchiveLink integration", "RefUrl": "/notes/2679398"}, {"RefNumber": "2381633", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: additional information for  DIMP systems with active long material number or MPN", "RefUrl": "/notes/2381633"}, {"RefNumber": "2216958", "RefComponent": "CA-FLE-MAT", "RefTitle": "Precheck Program for Migration to SAP S/4HANA with regards to Material Number Field Length Extension: Find BOR Usage and Material Number Usage", "RefUrl": "/notes/2216958"}, {"RefNumber": "2216654", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - BOR Objects", "RefUrl": "/notes/2216654"}, {"RefNumber": "2216559", "RefComponent": "CA-FLE-MAT", "RefTitle": "S4TC SAP_APPL Precheck for SAP S/4HANA System Conversion of ALE Change Pointers and MFLE", "RefUrl": "/notes/2216559"}, {"RefNumber": "2215424", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - General Information", "RefUrl": "/notes/2215424"}, {"RefNumber": "1696821", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Technical documentation: ASU variant restorer functionality", "RefUrl": "/notes/1696821"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2679398", "RefComponent": "CA-FLE-MAT", "RefTitle": "Support for key-conversion in ArchiveLink and in GOS/ArchiveLink integration", "RefUrl": "/notes/2679398 "}, {"RefNumber": "2305404", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Checks for transition of ERP system with ECC-DIMP (DIMP-LAMA activated) to S/4HANA", "RefUrl": "/notes/2305404 "}, {"RefNumber": "2381633", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: additional information for  DIMP systems with active long material number or MPN", "RefUrl": "/notes/2381633 "}, {"RefNumber": "2382620", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Field Length Extension: converting concatenated values read from an archive", "RefUrl": "/notes/2382620 "}, {"RefNumber": "2337941", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension - Unfinished Workflows from DIMP LAMA system", "RefUrl": "/notes/2337941 "}, {"RefNumber": "2215424", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - General Information", "RefUrl": "/notes/2215424 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "QRT_STAG", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "SAP_ABA", "From": "75A", "To": "75A", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}