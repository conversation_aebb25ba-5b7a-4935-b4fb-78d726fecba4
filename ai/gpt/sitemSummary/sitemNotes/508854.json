{"Request": {"Number": "508854", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 285, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015398382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000508854?language=E&token=A0D27D205D65132957378128BB2F8CA8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000508854", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000508854/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "508854"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 31}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2012-12-19"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "508854 - SAPGUI: How to use Unicode"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains the document<br /> <B>SAPGUI for Windows - I18N User's Guide</B><br />as an attachment.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This guide is intended for the users of SAPGUI who logon to Unicode<br />systems and those who logon to non-Unicode systems whose code-page is<br />not limited to Latin-1.<br /><br />It replaces the former contents of the notes below.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;508854 - SAPGUI: How to use Unicode<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;451694 - SAPG<PERSON>: How to use RTL layout<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;316903 - SAPGUI: How to use I18N mode<br /><br />For the better understanding we created the document in MHTML format,<br />which can have embedded pictures.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please extract \"I18N_Guide.mht\" from \"I18N_Guide.zip\" and read it with<br />Microsoft Internet Explorer.<br />If you want to print the document, please take<br />\"I18N_Guide_Printable.mht\" in \"I18N_Guide_Printable.zip\".<br />This document is based on Windows XP and SAPGUI 7.10, which is not the actual environment.<br /><br />Here is the index of the document.<br />------------------------------------------------------------------------<br />PART 1 - Basics<br /><br />1.1.&#x00A0;&#x00A0; Regional settings<br />1.2.&#x00A0;&#x00A0; Language of SAP Logon<br />1.3.&#x00A0;&#x00A0; SAPGUI code-page<br />1.4.&#x00A0;&#x00A0; I18N mode<br />1.5.&#x00A0;&#x00A0; Font character set<br />1.6.&#x00A0;&#x00A0; Font setting<br />1.7.&#x00A0;&#x00A0; F4 Help<br />1.8.&#x00A0;&#x00A0; File dialog<br />1.9.&#x00A0;&#x00A0; HTML encoding<br />1.10.&#x00A0;&#x00A0;Graphics<br /><br />PART 2 - GUI components<br /><br />2.1.&#x00A0;&#x00A0; Windows title<br />2.2.&#x00A0;&#x00A0; Grid control<br />2.3.&#x00A0;&#x00A0; Text Edit control<br />2.4.&#x00A0;&#x00A0; Tree control<br />2.5.&#x00A0;&#x00A0; Calendar control<br />2.6.&#x00A0;&#x00A0; Toolbar of controls<br />2.7.&#x00A0;&#x00A0; Graphical PC editor<br />2.8.&#x00A0;&#x00A0; Print preview<br />2.9.&#x00A0;&#x00A0; Option dialogs<br />2.10.&#x00A0;&#x00A0;Development tools<br />2.11.&#x00A0;&#x00A0;Message box<br />2.12.&#x00A0;&#x00A0;Context menu of Microsoft#s edit control<br /><br />PART 3 - Other restrictions<br /><br />3.1.&#x00A0;&#x00A0; Unicode-only characters<br />3.2.&#x00A0;&#x00A0; Remote logon through RFC<br />3.3.&#x00A0;&#x00A0; Missing features of SAPGUI 620<br /><br />PART 4 - RTL layout<br /><br />4.1.&#x00A0;&#x00A0; Software requirements<br />4.2.&#x00A0;&#x00A0; Layout change<br />4.3.&#x00A0;&#x00A0; Settings of SAPGUI<br />4.4.&#x00A0;&#x00A0; Restrictions<br /><br />PART 5 - Miscellaneous<br /><br />5.1.&#x00A0;&#x00A0; Regional options of Windows 2000<br />5.2.&#x00A0;&#x00A0; Third-party software<br />5.3.&#x00A0;&#x00A0; Advanced settings<br />5.4.&#x00A0;&#x00A0; Paste on Dynpro input fields<br />5.5.&#x00A0;&#x00A0; Text marking of multiple fields<br /><br />------------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "MODE"}, {"Key": "Transaction codes", "Value": "ICON"}, {"Key": "Transaction codes", "Value": "FULL"}, {"Key": "Transaction codes", "Value": "TREE"}, {"Key": "Transaction codes", "Value": "SO10"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D001665)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D001665)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000508854/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000508854/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "I18N_Guide_Printable.zip", "FileSize": "1180", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000066352002&iv_version=0031&iv_guid=351F4699DEB63D4CA453B736C0D775B7"}, {"FileName": "I18N_Guide.zip", "FileSize": "1175", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000066352002&iv_version=0031&iv_guid=DD9C15AF35894C4AA4375BCA8742E836"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1904609", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Kazakh", "RefUrl": "/notes/1904609"}, {"RefNumber": "1753671", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Hindi", "RefUrl": "/notes/1753671"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3072948", "RefComponent": "BC-FES-GUI", "RefTitle": "Windows setting \"Beta: Use Unicode UTF-8 for worldwide language support\" is not supported in SAP GUI for Windows", "RefUrl": "/notes/3072948 "}, {"RefNumber": "1753671", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Hindi", "RefUrl": "/notes/1753671 "}, {"RefNumber": "1904609", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Kazakh", "RefUrl": "/notes/1904609 "}, {"RefNumber": "1045465", "RefComponent": "EHS-BD", "RefTitle": "Fehlersuche von EH&S-spezifischen NLS-Problemen", "RefUrl": "/notes/1045465 "}, {"RefNumber": "1066952", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "Working with legacy data files in LSMW (Unicode, BOM, FTP)", "RefUrl": "/notes/1066952 "}, {"RefNumber": "493318", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "FAQ: Purchase requisition (ME51N, ME52N, ME53N)", "RefUrl": "/notes/493318 "}, {"RefNumber": "1025631", "RefComponent": "EHS-BD-PHR", "RefTitle": "Characters not displayed correctly in hit list", "RefUrl": "/notes/1025631 "}, {"RefNumber": "904078", "RefComponent": "SD-MD-CM", "RefTitle": "Bad display of the key on Unicode systems", "RefUrl": "/notes/904078 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BC-FES-GUI", "From": "7.20", "To": "7.20", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}