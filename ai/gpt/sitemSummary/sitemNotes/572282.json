{"Request": {"Number": "572282", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 332, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015323262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000572282?language=E&token=DDB3D79403C073EC06BACD3B197FE9B1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000572282", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000572282/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "572282"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2002"}, "SAPComponentKey": {"_label": "Component", "value": "PY-MX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Mexico"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mexico", "value": "PY-MX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-MX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "572282 - Sapser<PERSON> for note 561686, Retrocalculation for Mexico"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><STRONG>************************************************************************</STRONG><br /><STRONG>************************************************************************</STRONG><br /><STRONG>Very important prerequisite: In order to be able to apply the sapserv with success there is a minimun level of HRSP or LCP depending of the release:</STRONG><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;45B&#x00A0;&#x00A0;&#x00A0;&#x00A0; 86</STRONG><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;46B&#x00A0;&#x00A0;&#x00A0;&#x00A0; 68</STRONG><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C&#x00A0;&#x00A0;&#x00A0;&#x00A0; 59</STRONG><br /><STRONG>************************************************************************</STRONG><br /><STRONG>************************************************************************<br /><br /></STRONG><br />A sapserv is needed in order to get the new Retrocalculation Solution for Mexico.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Retrocalculation for Mexico</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>See note 561686</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In order to apply the corrections from note 0561686 included in this sapserv, please follow the next steps :<br />1) open an anonymous ftp connection to sapserv3.wdf.sap-ag.de ( user ftp, pass ftp )<br />2) change to directory general/R3server/abap/note.0561686<br />3) set binary transfer mode ( type bin )<br />4) download the file which matchs to your system :</p> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;new_retro_mx_45b.car if you run a 45B system or</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;new_retro_mx_46b.car if you run a 46B system or</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;new_retro_mx_46c.car if you run a 46C system.<br /></LI></UL> <p>In order to install the corrections you have to use the program tp. Note that the CAR file contains both the R* file ( that should go into your local trans/data directory ) and the K* ( that should go into your local trans/cofiles directory). These files are contained in the directories data and cofiles of the coresponding CAR file. If you prefer, you can extract the files manually with the CAR utility. Please refer to note 13719 for information about using sapserv.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D038023"}, {"Key": "Processor                                                                                           ", "Value": "D038023"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572282/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "561686", "RefComponent": "PY-MX", "RefTitle": "Retrocalculation for Mexico", "RefUrl": "/notes/561686"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "561686", "RefComponent": "PY-MX", "RefTitle": "Retrocalculation for Mexico", "RefUrl": "/notes/561686 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}