{"Request": {"Number": "2214054", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 271, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018160112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002214054?language=E&token=BCC2744864855011D08024035983FF12"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002214054", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002214054/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2214054"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.09.2016"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-FSCM-CLM"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Cash Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Supply Chain Management", "value": "FIN-FSCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Cash Management", "value": "FIN-FSCM-CLM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-CLM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2214054 - Release Information Note: SAP Cash Management (SAP S/4HANA 1511)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for SAP Cash Management&#160;applications, delivered with&#160;SAP S/4HANA 1511. To use this product, make sure you apply all the listed notes below.</p>\r\n<p>Note: The content of this&#160;SAP Note is subject to change.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4HANA, Cash and Liquidity Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Apply the following notes according to your product version, or upgrade to a higher support package stack:</p>\r\n<p><strong><strong>1511 FPS 02</strong></strong></p>\r\n<p><a target=\"_blank\" href=\"/notes/2307825\">2307825 - Fix for Central Fiori Component in All Fiori Apps with Design Studio UI</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2308603\">2308603 - Cannot Load Data in Foreign Bank Account Report App</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2285618\">2285618 - Design Studio S4 corrections: S4_2 (NW 7.50 SP4)</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2287379\">2287379 - INA: new value help - changing filter criterias several times leads to unexpected results</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2281000\">2281000 - Downport of InA functionality - InA Provider 41 + 42 + 43</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2287449\">2287449 - Downport of InA functionality - InA Provider 44</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2287950\">2287950 - Follow up note for 2281000</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2295522\">2295522 - MD Selector: transform var from container is not setting variable defaults correctly</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2106016\">2106016 - Configuration steps for Design Studio in Distributed sFin or S/4 HANA Environments with a Remote Gateway Server</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2360982\">2360982 - Manage Bank Accounts: Fix Invalid Mapping Between G/L Account and Bank Account</a></p>\r\n<p>&#160;</p>\r\n<p><strong>1511 FPS 01</strong></p>\r\n<p><a target=\"_blank\" href=\"/notes/2269272\">2269272 - \"Reference Lost\" Error for FLP Tiles after applying new UI component version</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2294218\">2294218 - Navigation Not Working Between BAM Web Dynpro Apps</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2238757\">2238757 - Error message \"Master data read class &amp;1 not actively available\" (R7B355) although read class is active</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2106016\">2106016 - Configuration steps for Design Studio in Distributed sFin or S/4 HANA Environments with a Remote Gateway Server</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2254344\">2254344 - Patched version 1.32.9 of SAPUI5 Runtime and standard libraries</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2307219\">2307219 - Bank Statement Monitor Fails to Display Data</a></p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p><strong>1511 FPS 00</strong></p>\r\n<p><a target=\"_blank\" href=\"/notes/2209808\">2209808 - Attribute planning throws error in case planning on not existing member is allowed</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2200098\">2200098 - Warning message \"Dimension member can not be empty for auto action\" for use workspace context option in auto work status setting in BPF hyperlink</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2202270\">2202270 - Auto Work Status change in BPF activity failed with Error \"member xxx doesn't exist in hierarchy xxx for InfoObject xxx\"</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2248448\">2248448 - Can't get correct other dimensions list in work status design time for embeded model</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2252764\">2252764 - Referencing dimenson can not be set as work status owner dimension</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2307219\">2307219 - Bank Statement Monitor Fails to Display Data</a></p>\r\n<p>&#160;</p>\r\n<p>Please note that only the most important notes are listed above. For other bug-fixing or functional enhancement notes, you can search using the following parameters:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"height: 267px; width: 461px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"149\">\r\n<p><strong>Software Component</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"313\">\r\n<p><strong>Application Area</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"149\">\r\n<p>S4CORE 100</p>\r\n</td>\r\n<td valign=\"top\" width=\"313\">\r\n<ul>\r\n<li>FIN-FSCM-CLM (SAP Cash Management)</li>\r\n<ul>\r\n<li>FIN-FSCM-CLM-BAM (Bank Account Management)</li>\r\n<li>FIN-FSCM-CLM-COP (Cash Operations)</li>\r\n<li>FIN-FSCM-CLM-LM (Liquidity Planning)</li>\r\n</ul>\r\n<li>FIN-FSCM-FQM&#160; (One Exposure)</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"149\">\r\n<p>UIAPFI70 300</p>\r\n</td>\r\n<td valign=\"top\" width=\"313\">\r\n<ul>\r\n<li>FIN-FIO-CLM (Fiori UI for SAP Cash Management)</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div>\r\n<p>&#160;</p>\r\n</div>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I035708)"}, {"Key": "Processor                                                                                           ", "Value": "I320593"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002214054/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002214054/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2189824", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA, on-premise edition 1511: Release Information Note", "RefUrl": "/notes/2189824 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}