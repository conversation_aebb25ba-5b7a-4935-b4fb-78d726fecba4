{"Request": {"Number": "741396", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 359, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004016492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000741396?language=E&token=EF42FFE38733F5EB89AADCC9AF232152"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000741396", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000741396/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "741396"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.06.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Materials Management", "value": "BW-BCT-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Inventory Management", "value": "BW-BCT-MM-IM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM-IM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "741396 - Incorrect quantities with posting of return (BWART 122)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You post a return delivery for a stock transport order with movement type 122. In this case, the quantities are updated into BW (Business Warehouse) incorrectly. This is indicated by duplicate quantities (plus/minus debit/credit indicator).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SHKZG, ZU_ABGANG, LMCRSF02, XMCMSEG-BWSWITCH, 2LIS_03_BF, FUELLEN_ZUABGANG_MSEG, BWART 122, BWART 123, OLI1BW, T156-XSTBW, XSTOR, ROCANCEL, MCEX_UPDATE_03<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In the case of a return with movement type 122 (or the corresponding reversal: 123), an incorrectly determined debit/credit indicator causes incorrect quantities to be transferred to BW. With a return delivery or reversal of the return delivery, the item lines are extracted to BW with same debit/credit indicators. This means that, during stock transfer, both items are extracted from the material document as goods issues, for example.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D041263"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031272)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000741396/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000741396/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000741396/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "715176", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Incorr. valuat. amount during return deliv. to prev. period", "RefUrl": "/notes/715176"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI", "From": "2003_1_40B", "To": "2003_1_470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2004_1_40B", "To": "2004_1_500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI 2004_1_40B", "SupportPackage": "SAPKIPZI11", "URL": "/supportpackage/SAPKIPZI11"}, {"SoftwareComponentVersion": "PI 2004_1_45B", "SupportPackage": "SAPKIPZI21", "URL": "/supportpackage/SAPKIPZI21"}, {"SoftwareComponentVersion": "PI 2004_1_46B", "SupportPackage": "SAPKIPZI31", "URL": "/supportpackage/SAPKIPZI31"}, {"SoftwareComponentVersion": "PI 2004_1_46C", "SupportPackage": "SAPKIPZI41", "URL": "/supportpackage/SAPKIPZI41"}, {"SoftwareComponentVersion": "PI 2004_1_470", "SupportPackage": "SAPKIPZI51", "URL": "/supportpackage/SAPKIPZI51"}, {"SoftwareComponentVersion": "PI 2004_1_500", "SupportPackage": "SAPKIPZI61", "URL": "/supportpackage/SAPKIPZI61"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI", "NumberOfCorrin": 1, "URL": "/corrins/0000741396/48"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "PI", "ValidFrom": "2003_1_40B", "ValidTo": "2003_1_40B", "Number": "715176 ", "URL": "/notes/715176 ", "Title": "Incorr. valuat. amount during return deliv. to prev. period", "Component": "BW-BCT-MM-IM"}, {"SoftwareComponent": "PI", "ValidFrom": "2003_1_45B", "ValidTo": "2003_1_45B", "Number": "715176 ", "URL": "/notes/715176 ", "Title": "Incorr. valuat. amount during return deliv. to prev. period", "Component": "BW-BCT-MM-IM"}, {"SoftwareComponent": "PI", "ValidFrom": "2003_1_46B", "ValidTo": "2003_1_46B", "Number": "715176 ", "URL": "/notes/715176 ", "Title": "Incorr. valuat. amount during return deliv. to prev. period", "Component": "BW-BCT-MM-IM"}, {"SoftwareComponent": "PI", "ValidFrom": "2003_1_46C", "ValidTo": "2003_1_46C", "Number": "715176 ", "URL": "/notes/715176 ", "Title": "Incorr. valuat. amount during return deliv. to prev. period", "Component": "BW-BCT-MM-IM"}, {"SoftwareComponent": "PI", "ValidFrom": "2003_1_470", "ValidTo": "2003_1_470", "Number": "715176 ", "URL": "/notes/715176 ", "Title": "Incorr. valuat. amount during return deliv. to prev. period", "Component": "BW-BCT-MM-IM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "715176", "RefTitle": "Incorr. valuat. amount during return deliv. to prev. period", "RefUrl": "/notes/0000715176"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "763424", "RefTitle": "Incorrect activity type with 123 transaction type", "RefUrl": "/notes/0000763424"}]}}}}}