{"Request": {"Number": "590056", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1482, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015360702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000590056?language=E&token=41854887F5B2A931D0A2CCFFE6321042"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000590056", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000590056/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "590056"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.07.2003"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-TEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "In Case of LiveCache Problems: Please use SCM-APO-LCA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "In Case of LiveCache Problems: Please use SCM-APO-LCA", "value": "SCM-TEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-TEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "590056 - APO Support Package 23 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes how to import APO Support Package 23 (SAPKY30A23) for APO Release 3.0A into your system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>APO Support Package, APO Release 3.0A, COM routines, liveCache, Optimizer, support release, Support Package, release strategy.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>1. IMPORTANT: Information on Support Package 23</b><br /> <p></p> <UL><LI> <B> 19.03.03: </B><B>SNP heuristic</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you are using SNP heuristic, read the following note immeditately:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;605624 APO-SNP<br /></p> <UL><LI> <B>Release restrictions</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See note 427957. It contains known release restrictions as of APO Support Release 3 (APO Support Package 14).<br /></p> <UL><LI> <B>Release of liveCache Version 7.4.2.xx:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;liveCache Version 7.4.2.xx, which we recommend for APO 3.0A customers, is now available.<br />Release of liveCache Version 7.4.2.xx.You will find detailed information at:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> http://service.sap.com/scm</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; mySAP SCM Technology<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Backup &amp; Recovery<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Link: \"liveCache 7.4 Availability for APO 3.0\"<br /></p> <UL><LI> <B>Prerequisites for APO Support Package 23 (SAPKY30A23)</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following link now provides a general overview of current versions, minimum requirements and the version history of the SAP BASIS, SAP ABA, SAP BW, SAP Kernel, SAP front end, SAP APO liveCache/COM and SAP APO Optimizer components:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> http://service.sap.com/scm</B><br />-&gt; mySAP SCM Technology<br />-&gt; Availability of SAP Support Packages, SAP liveCache and COM Builds<br />-&gt; Overview Matrix SAP APO 3.0A SP/COM /liveCache/Optimizer versions<br />Note also the platform prerequisites, which you can find in the Service Marketplace via the following link:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> http://service.sap.com/scm</B><br />-&gt; mySAP SCM Technology -&gt; Platforms &amp; System Requirements<BR/> -&gt; Availability of DB, OS Platforms &amp; System Requirements for SAP APO<br /></p> <UL><LI> <B>Prerequisite for SAP R/3 Back end</B></LI></UL> <UL><UL><LI>PlugIn 2001.1 (Support Package 01) or higher</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer also to:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 415244 (PlugIn 2001.1)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 449677 (PlugIn 2001.2)<br /></p> <UL><LI> <B>Newly-Recommended Procedure with Transaction SPAM:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of APO Support Release 2 (Support Release 2 includes APO Support Packages 1 - 8), we recommend that you import several APO Support Packages (from Support Package 9 to the<B> </B>current<B></B>Support Package) <B>in</B> <B>a</B> <B>single</B> <B>queue</B>.<br /></p> <UL><LI> <B>Refer to the following notes and if they are relevant for you, implement these after you import the Support Package:</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Number</B><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Component </B><B>Prio</B><B>rity</B><B> </B><B> </B><B> </B><B>Validity</B></TH></TR> <TR><TD></TD></TR> <TR><TD>594248</TD><TD> APO-SNP-CAP</TD><TD> <B>high</B></TD><TD> SP22 - SP23</TD></TR> <TR><TD>605624</TD><TD> APO-SNP</TD><TD> <B>high</B></TD><TD> SP23</TD></TR> <TR><TD>616785</TD><TD> APO-PPS-HEU-PP</TD><TD> <B>high</B></TD><TD> SP23 - SP24</TD></TR> <TR><TD>624570</TD><TD> APO-FCS-MAC</TD><TD> <B>high</B>/SP21 - SP26</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To import the above notes, we recommend that you always use the latest version of the Note Assistant.You can download the Note Assistant as follows from the Service Marketplace:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; <B>http://SERVICE.SAP.COM -&gt; SAP notes -&gt; Note Assistant</B></p> <b>2. Importing the components for Support Package 23</b><br /> <p></p> <UL><LI> <B>Support Package</B></LI></UL> <UL><UL><LI> <B>SPAM Update</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you start to import the Support Packages, you need to <B>update</B> <B>the</B> <B>SPAM</B> <B>manager to the latest</B><B>version</B>.You will find further information on this in the initial screen of the SPAM transaction by pressing the button 'i' (online documentation:Help -&gt; Application help).</p> <UL><UL><LI> <B>Importing the BASIS, ABA, BW and APO components</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you start to import these components with transaction SPAM, we recommend that:<br />1. No system activities are running or taking place in parallel.<br />2. No background jobs are running.<br />3. A checkpoint has been written with the '/SAPAPO/OM_CHECKPOINT_WRITE' program.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import these Support Package components separately in the following sequence:<br /><B>SAP BASIS -&gt; SAP ABA -&gt; SAP BW -&gt; SAP APO</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you always import the latest version of SAP BASIS, ABA and BW components.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The SAP <B>BASIS, SAP ABA and SAP APO </B>components can each be<B></B>imported<B> in</B> <B>a single queue.</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you cannot <B>import BW Support Packages in a single queue!</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 484015 Importing BW Support Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 328237 Importing patches into a BW 2.0B System</p> <b>3. GENERAL INFORMATION</b><br /> <p></p> <b>3.1 General information on APO Support Packages and initial releases</b><br /> <UL><LI> <B>Import sequence</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you import the Support Package components in the following sequence: <B>BASIS -&gt; ABA -&gt; BW -&gt; APO</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of APO Support Release 2 (Support Release 2 includes APO Support Packages 1 - 8), we recommend that you always import several APO Support Packages (from Support Package 9 up to the current Support Package) in one queue.</p> <UL><LI> <B>Initial Releases</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>APO Support Package 23 (SAPKY30A23) </B>for APO Release 3.0A requires a <B>complete</B> <B>installation</B>/<B>upgrade</B> (delivery of May 15, 2000). You must also import APO Support Packages 1 up to the current version.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For APO Release 3. 0A, you must also import Support Packages 5 If the system was set up with <B>Support</B> <B>Release</B> <B>1</B> up to the current version.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the system was set up<B> </B>with<B> Support Release 2</B>for APO Release 3.0A, you must also import Support<B> </B>Packages 9 up to the current version.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the system was set up<B> </B>with<B> Support Release 3 </B> for APO Release 3.0A, you must also import Support Packages 15 up to the current version.</p> <UL><LI> <B>Languages Supported with APO Release 3.0</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian, Czech, Chinese.</p> <b>3.2 Download Area</b><br /> <UL><LI> <B>Service Marketplace:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The SAP Service Marketplace allows customers to collect their SAP software from a central location.The Service Marketplace will gradually offer all SAP application components for download and will replace both the download areas (sapservX, OSS) and the old SAPNet download area (http://sapnet.sap.de/ocs-download)!<br />Currently you can download the 'SAP BASIS', 'SAP ABA', 'SAP BW','SAP APO', 'SAP APO liveCache and COM Build' (APO LC72/LCAPPS30), 'SAP APO Optimizer', 'SAP front end', and 'SAP Kernel' components via the Service Marketplace at the following address:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>http://SERVICE.SAP.COM/PATCHES -&gt; SAP APO -&gt; SAP APO 3.0A ...</B></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "D000325"}, {"Key": "Processor                                                                                           ", "Value": "D034322"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000590056/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000590056/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076"}, {"RefNumber": "448215", "RefComponent": "SCM-APO-SNP-TLB", "RefTitle": "Incorrect unit display during manual creation of an order", "RefUrl": "/notes/448215"}, {"RefNumber": "442764", "RefComponent": "SCM-TEC", "RefTitle": "SAPKY30A16 deletes texts for InfoObjects", "RefUrl": "/notes/442764"}, {"RefNumber": "432027", "RefComponent": "BC-UPG-OCS", "RefTitle": "Strategy for using SAP Support Packages", "RefUrl": "/notes/432027"}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957"}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886"}, {"RefNumber": "422446", "RefComponent": "SCM-APO-OCX", "RefTitle": "APO front end patch", "RefUrl": "/notes/422446"}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002"}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248"}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746"}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002 "}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886 "}, {"RefNumber": "422446", "RefComponent": "SCM-APO-OCX", "RefTitle": "APO front end patch", "RefUrl": "/notes/422446 "}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047 "}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "432027", "RefComponent": "BC-UPG-OCS", "RefTitle": "Strategy for using SAP Support Packages", "RefUrl": "/notes/432027 "}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746 "}, {"RefNumber": "442764", "RefComponent": "SCM-TEC", "RefTitle": "SAPKY30A16 deletes texts for InfoObjects", "RefUrl": "/notes/442764 "}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957 "}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "448215", "RefComponent": "SCM-APO-SNP-TLB", "RefTitle": "Incorrect unit display during manual creation of an order", "RefUrl": "/notes/448215 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "20B", "To": "20B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}