{"Request": {"Number": "2122578", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 702, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000012595852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002122578?language=E&token=4D01969F1E9CCC2476FCCBBA0702F6F6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002122578", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2122578"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-SNC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Secure Network Communications"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Secure Network Communications", "value": "BC-SEC-SNC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-SNC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2122578 - New: Security Audit Log event for unencrypted GUI / RFC connections"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are blocking unencrypted SAPGUI / RFC communication using the methods described in note <a target=\"_blank\" href=\"/notes/1690662\">1690662</a>.<br />Currently, you are not able to audit whether unencrypted communication does take place and whether such attempts are being blocked or not.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;BUJ</span>, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/log_unencrypted_rfc</span>,<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"> snc/only_encrypted_rfc</span>, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/only_encrypted_gui&#65279;</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You require the correction of&#160;<a target=\"_blank\" href=\"/notes/2104732\">2104732</a> for obtaining the new Security Audit Log event \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BUJ</span>\".<br />Afterwards you are able to activate the new Security Audit Log event (using t-code <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SM19</span>).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>After applying the kernel patch associated with this note, a Security Audit Log event is triggered (if activated, see prerequisites described above) when an unencrypted SAPGUI or RFC communication has been detected. The log record will contain the information on the type of request and whether the request has been blocked&#160;'(<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BLOCKED</span>)' or tolerated '(<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TOLERATED</span>)'.</p>\r\n<p>For unencrypted RFC communication you can configure whether you want to be informed only on blocked requests or whether a Security Audit Log event should also be created for tolerated unencrypted RFC communication (for different types of RFC communication) by setting profile parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/log_unencrypted_rfc</span>&#160;accordingly.</p>\r\n<p>Possible values of <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/log_unencrypted_rfc</span>&#160;and their effect:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Value</td>\r\n<td>Effect</td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>only blocked unencrypted RFC communication will create Security Audit Log entries (if activated)</td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td>in addition to 0: log tolerated unencrypted RFC communication of external (non-ABAP) RFC clients</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>in addition to 1: log tolerated unencrypted RFC communication of other ABAP systems/clients/users</td>\r\n</tr>\r\n<tr>\r\n<td>3</td>\r\n<td>in addition to 2: log tolerated unencrytepd internal RFC communication (same system, client &amp; user)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Using t-code <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">RZ11</span> the value of profile parameter&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/log_unencrypted_rfc</span>&#160;can be changed without restarting the ABAP server.</p>\r\n<p>&#160;</p>\r\n<p><strong>Motivation</strong></p>\r\n<p>You want to enforce the usage of encrypted communication (SAPGUI and &#65279;RFC&#65279;). After having enabled SNC on the server and on the client side you should start observing the usage of &#65279;SNC&#65279; by enabling the Security Audit Logging, first. At this point in time, the profile parameters <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/only_encrypted_gui</span>,&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/only_encrypted_rfc</span> and <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/log_unencrypted_rfc</span> should not be set&#160;(i.e. they all have the default value 0).</p>\r\n<p>In the Security Audit Log you might find entries informing you about \"Unencrypted <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SAPGUI</span> communication (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TOLERATED</span>)\", containing information regarding the Terminal Name (= hostname of the SAPGUI client). This helps you to identify the PCs where SNC might not be enabled, yet. Once no more unencrypted SAPGUI communication is reported, you can dare to set profile parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/only_encrypted_gui</span> to value 1. After that, unencrypted SAPGUI communication is blocked, resulting in Security Audit Log entries \"Unencrypted <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SAPGUI</span> communication (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BLOCKED</span>)\".</p>\r\n<p>After having enforced the usage of encrypted SAPGUI communication you might resume with securing &#65279;RFC&#65279; communication:<br />You should start with external RFC clients since those software components are typically installed on the same PCs where SAPGUI is installed and thus might already benefit from the SNC-enabling of the SAPGUI. To determine whether there is still unencrypted RFC communication (caused by external RFC clients) you should change the value of profile parameter&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/log_unencrypted_rfc</span> to 1 and then look for Security Audit Log entries of the kind \"Unencrypted <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ext. RFC</span> communication (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TOLERATED</span>)\". Once no more such Security Audit Log entries are found, you can dare to set profile parameter <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">snc/only_encrypted_rfc</span> to value 1 (see note <a target=\"_blank\" href=\"/notes/1690662\">1690662</a>). After that, unencrypted RFC communication originated from external (non-ABAP) RFC clients is blocked, resulting in Security Audit Log entries \"Unencrypted <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ext. RFC</span> communication (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BLOCKED</span>)\".</p>\r\n<p>You might resume with this pattern also for RFC communication between ABAP systems (ABAP-to-ABAP RFC communication) and potentially even for&#160;internal RFC communication - according to your security requirements.</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SEC-LGN (Authentication)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021767)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D046282)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002122578/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002122578/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2104732", "RefComponent": "BC-SEC-SAL", "RefTitle": "SAL - event definition for SNC client encryption", "RefUrl": "/notes/2104732"}, {"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1690662", "RefComponent": "BC-SEC-SNC", "RefTitle": "Option: Blocking unencrypted SAPGUI/RFC connections", "RefUrl": "/notes/1690662 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.43", "To": "7.43", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.43", "To": "7.43", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "760", "To": "760", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.43", "To": "7.43", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70033", "URL": "/supportpackage/SAPKB70033"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70118", "URL": "/supportpackage/SAPKB70118"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70218", "URL": "/supportpackage/SAPKB70218"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71020", "URL": "/supportpackage/SAPKB71020"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71115", "URL": "/supportpackage/SAPKB71115"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73014", "URL": "/supportpackage/SAPKB73014"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73117", "URL": "/supportpackage/SAPKB73117"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74012", "URL": "/supportpackage/SAPKB74012"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.22 32-BIT", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001791&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 32-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001792&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001793&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 64-BIT UNICODE", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001794&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 32-BIT", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001795&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 32-BIT UC", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001796&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001797&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.22 EXT 64-BIT UC", "SupportPackage": "SP003", "SupportPackagePatch": "000003", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=73555000100200001798&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.43 64-BIT UNICODE", "SupportPackage": "SP018", "SupportPackagePatch": "000018", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200023955&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT UNICODE", "SupportPackage": "SP111", "SupportPackagePatch": "000111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025031&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.42 64-BIT", "SupportPackage": "SP111", "SupportPackagePatch": "000111", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200025032&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP510", "SupportPackagePatch": "000510", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT", "SupportPackage": "SP215", "SupportPackagePatch": "000215", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023698&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.41 64-BIT UNICODE", "SupportPackage": "SP215", "SupportPackagePatch": "000215", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200023700&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70004 - SAPKB70032&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB71019&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71101 - SAPKB71114&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB70117&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70217&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73001 - SAPKB73013&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB72002&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73116&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <h2 data-toc-skip>Instructions for manually creating profile parameter snc/log_unencrypted_rfc</H2> <P></P> <OL>1. start transaction RZ11</OL> <OL>2. switch to maintenance mode (via the menu item \"Goto\"-&gt;\"Maintenance Mode\")</OL> <OL>3. press Create to add a new profile parameter (after applying the  kernel patch, see above) named \"snc/log_unencrypted_rfc\" (caution: the name is case-sensitive)</OL> <OL>4. assign the following attributes:</OL> <UL><UL><LI>short description (Engl.):<br/>\"<NOBR>Security Audit Logging for unencrypted RFC connections</NOBR>\"</LI></UL></UL> <UL><UL><LI>Application Area: \"Secure Network Communication\"</LI></UL></UL> <UL><UL><LI>Parameter Type: \"Integer Value\"</LI></UL></UL> <UL><UL><LI>Changes alloed: \"Change permitted\"</LI></UL></UL> <UL><UL><LI>Valid for Operating Sys.: \" All Oper. Systems\"</LI></UL></UL> <UL><UL><LI>Minimum: 0</LI></UL></UL> <UL><UL><LI>Maximum: 3</LI></UL></UL> <UL><UL><LI>Dynam. switchable: &lt;enable checkbox&gt;</LI></UL></UL> <UL><UL><LI>Same on all servers: &lt;enable checkbox&gt;</LI></UL></UL> <OL>5. save your work</OL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}