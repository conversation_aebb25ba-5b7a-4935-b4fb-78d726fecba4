{"Request": {"Number": "352941", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 317, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014917032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000352941?language=E&token=27CA108CE87892E3C9DFC3D5830DACA2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000352941", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000352941/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "352941"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.01.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS-LAN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Language Transport Tools (application server ABAP)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Language Transport Tools (application server ABAP)", "value": "BC-CTS-LAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS-LAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "352941 - Consultation: Languages and Support Packages"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to know more about the connection between language import, language supplementation, and the implementation of Support Packages.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Languages, language import, patches, support packages, language supplementation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>All Support Packages supplied by SAP mainly contain program corrections which, to a certain extent, also involve text changes. Moreover, Support Packages are also used to improve texts in the SAP system that have been translated incompletely or incorrectly. The text objects are delivered in all translation languages. Therefore Support Packages also contain corrections to the language status.<br />The language CDs produced by SAP contain a language status which fits a system into which no Support Packages have yet been imported. (Exception: For service releases, new language CDs are produced that match the Support Package level included in each release.) Therefore SAP recommends that you always import all the required languages before you import the first Support Package.<br />To offer the customer the option of installing an additional language after importing Support Packages, there are various approaches under the various releases which will be described below.<br />There are also interactions between language supplementation and the import of Support Packages: When importing a transport request into the target system, the transport tool R3trans tries to create the same state as in the export system. A missing SAP translation may cause a deletion in the import system. Especially texts which were created by language supplementation are at risk of being deleted.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>General information for Releases 3.1I and 4.0x.</b><br /> <p>In these releases, the language parts of the Support Packages are imported into the system for all SAP translation languages regardless of whether additional languages have been installed in the system or not. Consequently, language content in languages that have not been installed may enter the system. You can make use of this situation when you install a language after importing Support Packages.<br /></p> <b>General information for releases &gt;= 4.5A.</b><br /> <p>Starting from this release, R3trans offers the option of a selective language import (see Note <B>136649</B>). Here, language parts of transport objects are only imported into the system in those languages for which a language import has already been run. This reduces the database memory requirement and speeds up the import of Support Packages.<br /></p> <b>Language installation after patch import under 3.1I</b><br /> <p>To get new translations from the Support Packages, the following procedure is used under Release 3.1I.</p> <UL><LI>Save the translations from the Support Packages in a file.</LI></UL> <UL><LI>Execute the language import with the data from the language CD.</LI></UL> <UL><LI>Import the texts you saved.</LI></UL> <p>You can find a more detailed description of this procedure in Note <B>123235</B>. You must use the latest language CD for the release (3.1I SR1).<br /></p> <b>Language installation after patch import under 4.0B</b><br /> <p>Follow the same steps as in Release 3.1I. Use the newest language CD for the release (4.0B-SR1).<br /></p> <b>Language installation after patch import under 4.5A, 4.5B, 4.6A, 4.6B</b><br /> <p>Starting from Release 4.5, the \"selective language import\" is active when you import Support Packages. As a result, you cannot perform a backup export as in Releases 3.1I and 4.0x. To enable you to import language parts from the Support Packages later, the following procedure has been implemented (you can find the details in Note <B>154351</B>).</p> <UL><LI>When you create Support Package transport requests, the language parts of the Support Packages are stored in a compressed form in a table (TLSYHPG).</LI></UL> <UL><LI>If you carry out a language installation, the language status from the language CD is imported first.</LI></UL> <UL><LI>Then an additional language file is generated from the data stored in the container table TLSYHPG. This language file is an equivalent of the language part of the Support Packages. This language file is then imported.</LI></UL> <UL><LI></LI></UL> <p>This procedure ensures that, after a language import, a language is consistent with the Support Package level. Unfortunately, this procedure puts a great burden on the database and may cause long upgrade runtimes (phase LANG _IMP 3, Note <B>325186</B>). You should also be aware that not all texts of the Support Package are saved in table TLSYHPG; texts for logical transport objects or texts from the view maintenance are not saved and reimported (refer to Note <B>95681</B>).<br /></p> <b>Language installation after patch import under 4.6C and 4.6D</b><br /> <p>Starting from Release 4.6C, language transport is based on the tools \"tp\" and \"R3trans\". Moreover, there is no redundant data storage in a container table as in 4.5 and 4.6A/B. Instead, \"tp\" and \"R3trans\" have been equipped with the option of only importing the language content of a particular language from a Support Package transport request. By importing the language parts of all imported Support Packages you can achieve a consistent language status (for details see in Note <B>195442</B>).</p> <UL><LI>First import the newest language status from the language CD,</LI></UL> <UL><LI>then import the language parts from the Support Packages.</LI></UL> <p><br />This procedure works for all R3trans object types and is therefore the most precise of all the procedures described. The program for subsequent imports RSTLAN_IMPORT_OCS works best in conjunction with Support Package Collection CDs so that you are not forced to download all Support Packages from SAPNet again.<br /></p> <b>Language installation after patch import starting from Basis Release 6.x</b><br /> <p>In the Basis Releases 6.x, the same procedure is used as in Releases 4.6C/D, which means</p> <UL><LI>the language import from the language CD is carried out first, and then</LI></UL> <UL><LI>the language parts from the Support Packages are imported.</LI></UL> <p><br />The subsequent import of the language parts from the Support Packages (RSTLAN_IMPORT_OCS) can be accessed in transaction SMLT, using the menu path<br />Language -&gt; Special Actions -&gt; Import Support Packages<br />.<br /></p> <b>Subsequent import of language parts from add-ons in Basis Release 4.6C</b><br /> <p>RSTLAN_IMPORT_OCS is only able to import the language part of Support Packages that belong to a component previously supplied with a language installation.<br />This means that the language part of add-ons that do not have their own language packages will not be subsequently imported by RSTLAN_IMPORT_OCS. Language parts of Installation Packages and Support Packages for these add-ons must therefore be subsequently installed at operating system level using the TP POSTLANGUAGEIMPORT command (see Note 432272, 'Special Requirements' section).<br /></p> <b>Subsequent import of language parts from add-ons starting from Basis Release 6.x</b><br /> <p>Starting from SPAM version 22, RSTLAN_IMPORT_OCS can also import the language part of add-ons and their Support Packages (see Note 195442).<br />In this case, you do not have to use the TP POSTLANGUAGEIMPORT command anymore.<br /></p> <b>Importing a patch after language supplementation</b><br /> <p>If you have supplemented a language and then imported Support Packages, there is a risk of supplemented texts being deleted again. This especially affects languages that are rarely translated.<br />For this reason we recommend you repeat the language supplementation after you have imported a Support Package. A language supplementation in client 000 (client-independent tables) should be sufficient.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D042454)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035990)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000352941/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000352941/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000352941/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "928317", "RefComponent": "BC-CTS-LAN", "RefTitle": "Add-on without language data during OCS import", "RefUrl": "/notes/928317"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "676714", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on 6.40 language transport", "RefUrl": "/notes/676714"}, {"RefNumber": "662453", "RefComponent": "XX-SER-REL", "RefTitle": "Composite SAP Note Inst./Upgrade SAP R/3 Enterprise 47x200", "RefUrl": "/notes/662453"}, {"RefNumber": "647182", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/647182"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "534334", "RefComponent": "XX-SER-REL", "RefTitle": "Composite SAP note Install./Upgrade SAP R/3 Enterprise 47x11", "RefUrl": "/notes/534334"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "432272", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting language data using tp/R3trans", "RefUrl": "/notes/432272"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "330104", "RefComponent": "BC-CTS-LAN", "RefTitle": "Transportrequests used for language delivery // SRx", "RefUrl": "/notes/330104"}, {"RefNumber": "325186", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import:long runtime in SMLT and LANG_IMP3", "RefUrl": "/notes/325186"}, {"RefNumber": "195442", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import and Support Packages", "RefUrl": "/notes/195442"}, {"RefNumber": "18601", "RefComponent": "BC-CTS-LAN", "RefTitle": "Frequently asked questions about language transport", "RefUrl": "/notes/18601"}, {"RefNumber": "1755236", "RefComponent": "BC-CTS-LAN", "RefTitle": "Enhancements for RSTLAN_IMPORT_OCS", "RefUrl": "/notes/1755236"}, {"RefNumber": "1629934", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1629934"}, {"RefNumber": "154351", "RefComponent": "BC-CTS-LAN", "RefTitle": "Known problems during lang. import of patch texts", "RefUrl": "/notes/154351"}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438"}, {"RefNumber": "136649", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Selective language import", "RefUrl": "/notes/136649"}, {"RefNumber": "123235", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language installation after patch import", "RefUrl": "/notes/123235"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438 "}, {"RefNumber": "330104", "RefComponent": "BC-CTS-LAN", "RefTitle": "Transportrequests used for language delivery // SRx", "RefUrl": "/notes/330104 "}, {"RefNumber": "195442", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import and Support Packages", "RefUrl": "/notes/195442 "}, {"RefNumber": "1755236", "RefComponent": "BC-CTS-LAN", "RefTitle": "Enhancements for RSTLAN_IMPORT_OCS", "RefUrl": "/notes/1755236 "}, {"RefNumber": "18601", "RefComponent": "BC-CTS-LAN", "RefTitle": "Frequently asked questions about language transport", "RefUrl": "/notes/18601 "}, {"RefNumber": "1629934", "RefComponent": "BC-CTS-TLS", "RefTitle": "POSTLANGUAGEIMPORT imports texts incompletely", "RefUrl": "/notes/1629934 "}, {"RefNumber": "136649", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Selective language import", "RefUrl": "/notes/136649 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "154351", "RefComponent": "BC-CTS-LAN", "RefTitle": "Known problems during lang. import of patch texts", "RefUrl": "/notes/154351 "}, {"RefNumber": "928317", "RefComponent": "BC-CTS-LAN", "RefTitle": "Add-on without language data during OCS import", "RefUrl": "/notes/928317 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "676714", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on 6.40 language transport", "RefUrl": "/notes/676714 "}, {"RefNumber": "432272", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting language data using tp/R3trans", "RefUrl": "/notes/432272 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "123235", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language installation after patch import", "RefUrl": "/notes/123235 "}, {"RefNumber": "325186", "RefComponent": "BC-CTS-LAN", "RefTitle": "Language import:long runtime in SMLT and LANG_IMP3", "RefUrl": "/notes/325186 "}, {"RefNumber": "534334", "RefComponent": "XX-SER-REL", "RefTitle": "Composite SAP note Install./Upgrade SAP R/3 Enterprise 47x11", "RefUrl": "/notes/534334 "}, {"RefNumber": "662453", "RefComponent": "XX-SER-REL", "RefTitle": "Composite SAP Note Inst./Upgrade SAP R/3 Enterprise 47x200", "RefUrl": "/notes/662453 "}, {"RefNumber": "692544", "RefComponent": "XX-TRANSL-JA", "RefTitle": "Improvement of Japanese translation for R/3 Enterprise 4.7", "RefUrl": "/notes/692544 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}