{"Request": {"Number": "2305513", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 482, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013622152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002305513?language=E&token=59D2CBCCE87AC65EF63F5DFC75CB261C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002305513", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002305513/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2305513"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.08.2016"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2305513 - RE-FX: Changeover of posting log to ALV grid list display"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note enables you to display the posting log of selected posting processes as an ALV Grid list. Previously, it was only possible to display the documents as a classic ALV list.</p>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>REITDS, REITDSRV, RERAPP, RERAPPRV, RERAPP_SINGLE, RERAVP, RERAVPRV</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Reason: This is a functional enhancement.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached correction instructions or import the specified Support Package.</p>\r\n<p>Both the list of cash flow items and the document list have been changed over.</p>\r\n<p>For the changed reports, an additional \"Use Grid Control\" indicator is provided on the selection screen. This indicator can be set manually or preassigned.</p>\r\n<p>The BAdI BADI_REIS_LIST, method SET_ALV_GRID is available for preassignment. Use the process, that is, for example, REPP (periodic posting), REIT (input tax distribution), REVP (vacancy posting), REEP (valuation posting) as filter value.</p>\r\n<p>It is now possible to create separate layouts for each process for the posting log (display of documents). Previously, this was not possible.</p>\r\n<p>For the periodic posting (RERAPP), periodic postings for vacancies (RERAVP) and the valuation posting (RECEEP, available using SAP Note 2305088), the cash flow log is also displayed as an ALV grid if this was selected on the selection screen. You can still go from the cash flow log to the old display (choose \"Documents\") and the new display (choose \"Line Items\").</p>\r\n<p>Changed transactions:</p>\r\n<p>REITDS, REITDSRV</p>\r\n<p>RERAPP, RERAPPRV, RERAPP_SINGLE</p>\r\n<p>RERAVP, RERAVPRV</p>\r\n<p>RECEEP, RECEEPRV, RECEEP_SINGLE (available with SAP Note 2305088)</p>\r\n<p>Other transactions, such as the service charge settlement (SCS), sales-based settlements and the one-time postings have not been converted.</p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D027300)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026407)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002305513/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002305513/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555 "}, {"RefNumber": "2367498", "RefComponent": "RE-FX-RA", "RefTitle": "RERAPPCMP: Cash flow/posting log as ALV grid", "RefUrl": "/notes/2367498 "}, {"RefNumber": "2360849", "RefComponent": "RE-FX-RA", "RefTitle": "Display of document header data in case of error", "RefUrl": "/notes/2360849 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10003INS4CORE", "URL": "/supportpackage/SAPK-10003INS4CORE"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60618INEAAPPL", "URL": "/supportpackage/SAPK-60618INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 616", "SupportPackage": "SAPK-61611INEAAPPL", "URL": "/supportpackage/SAPK-61611INEAAPPL"}, {"SoftwareComponentVersion": "EA-FIN 617", "SupportPackage": "SAPK-61712INEAFIN", "URL": "/supportpackage/SAPK-61712INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61803INSAPFIN", "URL": "/supportpackage/SAPK-61803INSAPFIN"}, {"SoftwareComponentVersion": "EA-FIN 700", "SupportPackage": "SAPK-70009INEAFIN", "URL": "/supportpackage/SAPK-70009INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72005INSAPFIN", "URL": "/supportpackage/SAPK-72005INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73004INSAPFIN", "URL": "/supportpackage/SAPK-73004INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 1, "URL": "/corrins/0002305513/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002305513/19773"}, {"SoftwareComponent": "EA-FIN", "NumberOfCorrin": 1, "URL": "/corrins/0002305513/15842"}, {"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 2, "URL": "/corrins/0002305513/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10002INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>See EA-APPL<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 618&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61802INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-72004INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73003INSAPFIN - SAPK-73003INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 617&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61701INEAFIN - SAPK-61711INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70008INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEAAPPL - SAPK-60617INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61610INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><br/>Perform the following manual activity as an advance correction:<br/><br/>Use transaction SE11 to open the structure REEX_GUI_CASHFLOW_LIST in change mode.<br/>Add the following new components at the end:<br/><br/>Component  Typing Method Component Type<br/>ICON_STATUS  TYPE  RECAICON<br/>COLORTAB&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TYPE   LVC_T_SCOL<br/>STYLETAB&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TYPE   LVC_T_STYL<br/><br/>Change the enhancement category to \"Can Be Enhanced (Deep)\".<br/><br/>Save and activate the structure.<br/><br/><br/><br/><br/><br/>Use transaction SE11 to create a new structure REEX_GUI_DOC_HEADER_ITEM_L with the description \"Document Header and  Lines for ALV Grid List\" in the package RE_EX_FI.<br/><br/>Insert the following components:<br/><br/><br/>Component  Typing Method Component Type<br/><br/>DOCGUID  Type  RERADOCGUID<br/>ICON_LIGHTS Type  RERA_STATUS_LIGHT<br/>.INCLUDE Type  BAPIACHE09<br/>AC_DOC_NO_R Type  BELNS<br/>ITEMNO_ACC Type  POSNR_ACC<br/>SP_GL_IND Type  UMSKZ<br/>AMT_DOCCUR Type  RERAAMOUNT<br/>CURRENCY Type  WAERS<br/>CTLCAMOUNT Type  RECACTLCAMOUNT<br/>CTLCCURRKEY Type  RECACTLCCURRKEY<br/>ZFBDT  Type  DZFBDT<br/>GSBER  Type  GSBER<br/>TAX_CODE Type  MWSKZ<br/>TXJCD  Type  TXJCD<br/>XITEM  Type  SGTXT<br/>GL_ACCOUNT Type  HKONT<br/>ACCTYPE  Type  RERAACCTYPE<br/>ACCOUNTID Type  CHAR10<br/>XACCOUNTIDENT Type  REEXXLACCOUNTIDENT<br/>FLOW_TYPE Type  RECDFLOWTYPE<br/>REF_DATE Type  DABRBEZ<br/>OBJNR  Type  RECAOBJNR<br/>XOBJIDENT Type  RECAIDENT<br/>CLEAREDIND Type  REEXCLEAREDIND<br/>ZUONR  Type  DZUONR<br/>ZSCHL  Type  SCHZW_BSEG<br/>ZLSPR  Type  DZLSPR<br/>ZTERM  Type  DZTERM<br/>HBKID  Type  HBKID<br/>HKTID  Type  HKTID<br/>BVTYP  Type  BVTYP<br/>MSCHL  Type  MSCHL<br/>MANSP  Type  MANSP<br/>MADAT  Type  MADAT<br/>MANST  Type  MAHNS_D<br/>MABER  Type  MABER<br/>PRCTR  Type  PRCTR<br/>FIPOS  Type  FIPOS<br/>FISTL  Type  FISTL<br/>GEBER  Type  FM_FUND<br/>GRANT_NBR Type  GM_GRANT_NBR<br/>FKBER_LONG Type  FKBER<br/>REFGUID  Type  RERAREFGUID<br/>BUPLA  Type  BUPLA<br/>SECCO  Type  SECCO<br/>BUDGET_PD Type  FM_BUDGET_PERIOD<br/>DATEOFSERVICE Type  REITTCDATEOFSERVICE<br/>WBS_ELEMENT Type  PS_POSID<br/>COSTCENTER Type  KOSTL<br/>ORDERID  Type  AUFNR<br/>PPRCTR  Type  PPRCTR<br/>SEPAMNDID Type  RETMSEPAMNDID<br/>SEPARECCRDID Type  RETMSEPARECCRDID<br/>MAPREFGUID Type  RERAREFGUID<br/>ICON_STATUS Type  RECAICON<br/>COLORTAB Type  LVC_T_SCOL<br/>STYLETAB Type  LVC_T_STYL<br/><br/>Switch to the Currency/Quantity Fields tab.<br/>Component AMT_DOCCUR<br/>Reference table REEX_GUI_DOC_HEADER_ITEM_L<br/>Reference field CURRENCY<br/><br/>Component CTLCAMOUNT<br/>Reference table REEX_GUI_DOC_HEADER_ITEM_L<br/>Reference field CTLCCURRKEY<br/><br/>Set the enhancement category to \"Can Be Enhanced (Deep)\".<br/><br/>Save and activate the structure.<br/><br/><br/><br/><br/><br/>Use transaction SE11 to create a new table type RE_T_EX_DOC_HEADER_ITEM  with the description \"Document Header and Lines for ALV Grid Output\" and  the line type REEX_GUI_DOC_HEADER_ITEM_L in the package RE_EX_FI.<br/><br/>Save and activate the table type.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10002INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 618&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61802INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-72004INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73003INSAPFIN - SAPK-73003INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 617&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61701INEAFIN - SAPK-61711INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70008INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See EA_APPL<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEAAPPL - SAPK-60617INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61610INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>After you have implemented the corrections, perform the following manual post-implementation steps:<br/><br/><br/>Call the following reports using transaction SE38, and choose Goto -&gt; Text Elements -&gt; Selection Texts to go to<br/>text maintenance:<br/><br/>RFREITDS, RFREITDSRV, RFRERAOPSUTPRV, RFRERAPP, RFRERAPPRV, RFRERAPP_SINGLE,RFRERAVP, RFRERAVPRV<br/>New selection text for the parameter P_GRID<br/>Set the dictionary indicator for P_GRID.<br/>Save and activate your changes.<br/><br/><br/>Function group REEX_GUI_LOG_APPL<br/>Choose \"Goto -&gt; Text elements -&gt; Text Symbols\" and create the following:<br/><br/>Symbol  Text   Max. length<br/><br/>001   Document Header Fields 30<br/>002  Line Item Fields 30<br/>BPR  Posting Log 30<br/>BST   Status    6<br/>PRO  Error Log  30<br/><br/>Save and activate your changes.<br/><br/><br/><br/>REPT SAPLRERA_GUI_CASHFLOW_DOC_APPL<br/>Choose \"Goto -&gt; Text elements -&gt; Text Symbols\" and create the following:<br/><br/>Symbol  Text   Max. length<br/><br/>BSL  Posting Status  30<br/>BST  PstgSts   7<br/>PDO  Documents   30<br/>PPG  Line items  30<br/>PPR  Posting log 30<br/>PRO&nbsp;&nbsp;&nbsp;&nbsp; Error log 30<br/><br/>Save and activate your changes.<br/><br/><br/>In transaction SE80, call the function group RERA_GUI_CASHFLOW_DOC_APPL.<br/>Create a new GUI status \"GRID\" (short text GRID) of the type Dialog Status.<br/><br/>Function keys:<br/><br/>For Back icon:<br/>Function code BACK<br/>Function type E Exit command<br/>Function text Back<br/>Fastpath B<br/><br/><br/>for Exit icon:<br/>Function code EXIT<br/>Function type E Exit command<br/>Function text Exit<br/>Fastpath E<br/><br/><br/>for Cancel icon:<br/>Function code CANC<br/>Function type E Exit command<br/>Function text Cancel<br/>Fastpath C<br/>Icon name:  ICON_CANCEL<br/><br/><br/>Application toolbar:<br/>No entries<br/><br/>Menu bar:<br/><br/>Choose \"Display Standards\", if available. If the Modification Assistant is active, this is not possible.<br/><br/>1st column: 'List'<br/>In the first column, replace \"Object\" with \"List\".<br/>For the \"Exit\" entry, insert the function code EXIT.<br/><br/>2nd column ‘Edit’<br/><br/>For the \"Cancel\" entry, insert the function code CANC.<br/><br/>3rd column: ‘Goto’<br/><br/>For the \"Back\" entry, insert the function code BACK.<br/><br/><br/>Save and activate the menu.<br/><br/><br/>Important: If the system issues error message EC 352 when you do this  (Modification Assistant) during processing, proceed as described in detail in SAP Note 550180.<br/><br/><br/><br/><br/><br/>In transaction SE80, call the function group REEX_GUI_LOG_APPL.<br/>Create a new GUI status \"GRID\" (short text GRID) of the type Dialog Status.<br/><br/>Function keys:<br/><br/>for the \"Ok\" icon:<br/>Function code OK<br/>Function type  Application function<br/>Function text Enter<br/>Fastpath: <br/><br/><br/>For Back icon:<br/>Function code BACK<br/>Function type E Exit command<br/>Function text Back<br/>Fastpath B<br/><br/><br/>for Exit icon:<br/>Function code EXIT<br/>Function type E Exit command<br/>Function text Exit<br/>Fastpath E<br/><br/><br/>for Cancel icon:<br/>Function code CANC<br/>Function type E Exit command<br/>Function text Cancel<br/>Fastpath C<br/>Icon name:  ICON_CANCEL<br/><br/><br/>Application toolbar:<br/>No entries<br/><br/>Menu bar:<br/><br/>Choose \"Display Standards\".<br/><br/>1st column:<br/>In the first column, replace \"Object\" with \"List\".<br/>For the \"Exit\" entry, insert the function code EXIT.<br/><br/>2nd column Edit<br/><br/>For the \"Cancel\" entry, insert the function code CANC.<br/><br/>3rd column: Goto<br/><br/>For the \"Back\" entry, insert the function code BACK.<br/><br/><br/>Save and activate the menu.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 8, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 26, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "606", "Number": "1634710 ", "URL": "/notes/1634710 ", "Title": "RERAPP: Cash flow log / header / absolute amounts", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "606", "Number": "1694300 ", "URL": "/notes/1694300 ", "Title": "RERAPP: Documents with errors not displayed", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "616", "Number": "1912312 ", "URL": "/notes/1912312 ", "Title": "RERAPP: GETWA_NOT_ASSIGNED when exporting to spreadsheet", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "616", "Number": "1826259 ", "URL": "/notes/1826259 ", "Title": "RERAPPRV: Wrong doctype for reversal doc", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "604", "ValidTo": "606", "Number": "1753102 ", "URL": "/notes/1753102 ", "Title": "RERAPP: Feldlängen im Layout des Finanzstromprotokolls", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "606", "Number": "1743486 ", "URL": "/notes/1743486 ", "Title": "Direktes Buchen aus Vertrag: falsche Berechtigungsprüfung", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "605", "ValidTo": "616", "Number": "1916149 ", "URL": "/notes/1916149 ", "Title": "Direct posting from the contract: Authorization RERAPP", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1629194 ", "URL": "/notes/1629194 ", "Title": "RERAPP: Partner profit center missing in RE document list", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1666761 ", "URL": "/notes/1666761 ", "Title": "Posting log: Display of withholding tax in simulation", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1700030 ", "URL": "/notes/1700030 ", "Title": "RE document display also required for other reports", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1720966 ", "URL": "/notes/1720966 ", "Title": "Posting log simulation: Display of currencies", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1741740 ", "URL": "/notes/1741740 ", "Title": "Erweiterung Selektionsbild REITDS um Bezugsdatum", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1746551 ", "URL": "/notes/1746551 ", "Title": "REITDS: Berechtigung zu allen Buchungskreisen geprüft", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1778596 ", "URL": "/notes/1778596 ", "Title": "RERAPP: Cash flow list for summarization", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1863414 ", "URL": "/notes/1863414 ", "Title": "RE-FX performance: Access to FI documents", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1868489 ", "URL": "/notes/1868489 ", "Title": "RE-FX performance: Parallel processing in RERAPP/RERAPPRV", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1884598 ", "URL": "/notes/1884598 ", "Title": "REITDS: Posting log", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2093200 ", "URL": "/notes/2093200 ", "Title": "RE-FX: Specifying the number of objects per task", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2305513 ", "URL": "/notes/2305513 ", "Title": "RE-FX: Changeover of posting log to ALV grid list display", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "1769817 ", "URL": "/notes/1769817 ", "Title": "REITDS: No result list in case of option rate of 100%", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "1845354 ", "URL": "/notes/1845354 ", "Title": "Simulation posting log: Currency display in case of error", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "1870539 ", "URL": "/notes/1870539 ", "Title": "RE-FX performance: Unnecessary DB accesses in package RE_IT", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "1996601 ", "URL": "/notes/1996601 ", "Title": "RERAPPRV:  Selection by contracts and several company codes", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "2218678 ", "URL": "/notes/2218678 ", "Title": "Incorrect parameter type for ID_PSTNGTYPE", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "1778596 ", "URL": "/notes/1778596 ", "Title": "RERAPP: Cash flow list for summarization", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "1863414 ", "URL": "/notes/1863414 ", "Title": "RE-FX performance: Access to FI documents", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "1868489 ", "URL": "/notes/1868489 ", "Title": "RE-FX performance: Parallel processing in RERAPP/RERAPPRV", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "1869691 ", "URL": "/notes/1869691 ", "Title": "RE-FX: Open item account number for key date", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "1884598 ", "URL": "/notes/1884598 ", "Title": "REITDS: Posting log", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2093200 ", "URL": "/notes/2093200 ", "Title": "RE-FX: Specifying the number of objects per task", "Component": "RE-FX"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1868489 ", "URL": "/notes/1868489 ", "Title": "RE-FX performance: Parallel processing in RERAPP/RERAPPRV", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1869691 ", "URL": "/notes/1869691 ", "Title": "RE-FX: Open item account number for key date", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1870539 ", "URL": "/notes/1870539 ", "Title": "RE-FX performance: Unnecessary DB accesses in package RE_IT", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1884598 ", "URL": "/notes/1884598 ", "Title": "REITDS: Posting log", "Component": "RE-FX-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1912312 ", "URL": "/notes/1912312 ", "Title": "RERAPP: GETWA_NOT_ASSIGNED when exporting to spreadsheet", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1916149 ", "URL": "/notes/1916149 ", "Title": "Direct posting from the contract: Authorization RERAPP", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2093200 ", "URL": "/notes/2093200 ", "Title": "RE-FX: Specifying the number of objects per task", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "1996601 ", "URL": "/notes/1996601 ", "Title": "RERAPPRV:  Selection by contracts and several company codes", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2218678 ", "URL": "/notes/2218678 ", "Title": "Incorrect parameter type for ID_PSTNGTYPE", "Component": "RE-FX-RA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2305513 ", "URL": "/notes/2305513 ", "Title": "RE-FX: Changeover of posting log to ALV grid list display", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2093200 ", "URL": "/notes/2093200 ", "Title": "RE-FX: Specifying the number of objects per task", "Component": "RE-FX"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2218678 ", "URL": "/notes/2218678 ", "Title": "Incorrect parameter type for ID_PSTNGTYPE", "Component": "RE-FX-RA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2305513 ", "URL": "/notes/2305513 ", "Title": "RE-FX: Changeover of posting log to ALV grid list display", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "720", "Number": "2218678 ", "URL": "/notes/2218678 ", "Title": "Incorrect parameter type for ID_PSTNGTYPE", "Component": "RE-FX-RA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "730", "Number": "2305513 ", "URL": "/notes/2305513 ", "Title": "RE-FX: Changeover of posting log to ALV grid list display", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2296200 ", "URL": "/notes/2296200 ", "Title": "Enhancement of internal function", "Component": "RE-FX"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2360849", "RefTitle": "Display of document header data in case of error", "RefUrl": "/notes/0002360849"}, {"RefNumber": "2363329", "RefTitle": "REITTCCALC: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED_NO when displaying documents", "RefUrl": "/notes/0002363329"}]}}}}}