{"Request": {"Number": "2508380", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 481, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019296802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002508380?language=E&token=92E8AA9FF1EABDA6E9933083F716C03A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002508380", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002508380/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2508380"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.04.2020"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PC-OBJ"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cost Object Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Cost Controlling", "value": "CO-PC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cost Object Controlling", "value": "CO-PC-OBJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC-OBJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2508380 - FAQ: Product Costing: Setup Manufacturing Order Plan Costs and Plan/Actual Costs by Work Center/Operation in S/4 HANA OP1709"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are trying to set up SAP S/4 HANA New Cost Object Controlling Manufacturing Planning Costs, or you would like to use the Fiori application F1780 Production Cost Analysis.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4 HANA OP1709, S/4 HANA New Product Costing, F1780 Production Cost Analysis, Manufacturing Planning Costs, Plan Category, PLANORD01</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to analyze plan costs for&#160;manufacturing orders, which includes order categories 10 (Production Order) and 40 (Process Orders) in&#160;Fiori Application F1780 Production Cost Analysis&#160;in SAP S/4HANA Cloud OP1709.</p>\r\n<p>Plan Costs calculation of manufacturing orders has errors.</p>\r\n<p>Actual Posting is missing from the Work Center view.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SAP delivered the first innovation of New Product Costing in SAP S/4HANA 1709. With this solution you can run the Fiori report F1780 Production Cost Analysis based on New Financials Planning Solution. Detailed innovations are described as follows:</p>\r\n<p><strong>1.&#160;&#160;&#160;&#160; </strong><strong>Harmonizing Plan Costs for manufacturing orders based on New Financials Planning Solution</strong></p>\r\n<ul>\r\n<li>Starting from SAP S/4HANA 1709, Released Orders&#160;belong to&#160;order categories 10 (Production Order) and 40 (Process Orders)&#160;are updating Plan Costs in ACDOCP and&#160;existing ERP tables.</li>\r\n<li>Planned overhead costs at work center/Operation level is not supported in SAP S/4HANA 1709.</li>\r\n</ul>\r\n<p><strong>1.1&#160;&#160; Categories for Plan Data</strong></p>\r\n<ul>\r\n<li>To use the new Financials Planning for manufacturing orders, you need to create and maintain relevant categories (or activate SAP pre-delivered content). Categories can be created in each system separately but need to&#160;be transported into the test system and the production system. You can do this in Customizing, under Controlling -&gt; General Controlling -&gt; Planning -&gt; Maintain Category for Planning.</li>\r\n<li>Only categories PLANORD01 is relevant for the&#160;Manufacturing Orders Plan Costs update.</li>\r\n</ul>\r\n<p><strong>1.2&#160;&#160; </strong><strong>Calculate Plan Costs for Existing Orders based on new Financials Planning Solution</strong></p>\r\n<ul>\r\n<li>Before you run the Fiori Application F1780 Production Cost Analysis, please calculate Plan Costs for existing manufacturing orders to ensure plan costs can&#160;be updated based on New&#160;Financials Planning Solution.</li>\r\n<li>\r\n<p>Order plan costs and order standard costs update when you change the order, only key elements which are relevant to plan costs/standard costs calculation are applicable. The fields are as follows: Total Quantity, Scrap Portion, Plan Cost Variant, Costing Sheet, Overhead Key, Quantity of component and Planned Activities time.</p>\r\n</li>\r\n</ul>\r\n<p><strong>2.&#160;&#160; </strong><strong>Posting Actual Costs at a more detailed responsible area </strong></p>\r\n<ul>\r\n<li>Starting from SAP S/4HANA 1709, order categories 10 (Production Order) and 40 (Process Orders) post Actual Costs from Confirmation/Goods Movements at Work Center and Operation level.</li>\r\n<li>Overhead per work center/operation is not supported in SAP S/4HANA 1709.</li>\r\n<li>To post Actual Costs at Work Center and Operation level, you need to switch it off (BSEG Summarization: delete the field from transaction OBCY or OBCYX for reference procedure MKPF / ACDOCA summarization: the same in transaction OBCYA).</li>\r\n</ul>\r\n<p><strong>3. &#160;</strong><strong>Enhancement of Production Cost Analysis</strong></p>\r\n<p>&#160;&#160;&#160;&#160; Starting from SAP S/4HANA 1709, you can:</p>\r\n<ul>\r\n<li>Run the Fiori Application based on new Financials Planning solution.</li>\r\n<li>For orders&#160;belonging to&#160;order categories 10 (Production Order) and 40 (Process Orders), you can&#160;view Plan Costs (excluding overhead costs) and Actual Costs (excluding overhead costs) at Work Center and Operation level. For overhead related items, Work Center and Operation&#160;display as&#160;blank in the reporting.</li>\r\n</ul>\r\n<p>&#160;<strong>4. Remarks</strong></p>\r\n<ul>\r\n<li>Category PLANORD02 is reserved in customization for extension.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CO-PC-OBJ-ORD (Product Cost by Order)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I333151)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I074388)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002508380/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002508380/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2807519", "RefComponent": "CO-PC-OBJ", "RefTitle": "SAP S/4HANA 1909: On-the-Fly Target Cost Calculation for Manufacturing Orders", "RefUrl": "/notes/2807519 "}, {"RefNumber": "2661581", "RefComponent": "CO-PC-OBJ", "RefTitle": "SAP S/4HANA: On-the-Fly Target Cost Calculation for Manufacturing Orders (Until SAP S/4HANA 2021)", "RefUrl": "/notes/2661581 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}