{"Request": {"Number": "498973", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2056, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002336482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000498973?language=E&token=977B09467E2AE9D4CB6B96F038D2B89A"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000498973", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000498973/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "498973"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.03.2002"}, "SAPComponentKey": {"_label": "Component", "value": "IS-H-CM-OUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital External Data Transmission"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Communication", "value": "IS-H-CM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-CM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital External Data Transmission", "value": "IS-H-CM-OUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-CM-OUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "498973 - IS-H AT: ELDA - Import File (Advance Development)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=498973&TargetLanguage=EN&Component=IS-H-CM-OUT&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/498973/D\" target=\"_blank\">/notes/498973/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Austria (AT).<br />Data Exchange: ELDA - Import File (Advance Development).<br />When a file is imported, a message is created for each message type so that it can then be managed in the EDI Workbench.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>ELDA, import of insurance verification, data exchange, P321, VZE</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>When the insurance verification is imported, the imported file is decrypted and a message is stored for each message type. This message can then be managed in the EDI workbench.<br />CAUTION: The prerequisites were changed on March 25, 2002.<br />IMPORTANT for all systems up to 4.62C patch level 3:<br />Implement the relevant corrections (see validities).<br />Afterwards, a syntax error will occur. This is corrected with SAP Note 445076. --> Implement this SAP Note immediately afterwards.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Release 4.63: An advance correction is not possible. The changes are contained in Patch 06 for 4.63.<br /><B>Releases 4.62B and 4.62C:</B><br />Implement the attached corrections in your system.<br />The correction consists of a transport request (of SapServ3) and a correction instruction. Note the validity of the correction instructions.<br />It is irrelevant whether you make the manual correction first or import the request beforehand.<br /><br />The transport request (PJ7K010593) is located in the directory<br />/general/R3server/abap/note.0498973<br /><br />For more information about importing transport requests, see SAP Note 13719.<br /><br />CAUTION: This SAP Note is a prerequisite for Lv AT for all further corrections or Changes in the Data Exchange - Import File area.<br /></p> <b>Import Procedure: Documentation<br /></b><br /> <UL><LI>READ 1 CC: GUIDE</LI></UL> <p>           As before, a file is imported using the report RNC301I0 (Workbench -> Import File).<br />With automatic recognition, the file that has the next sequential number for the receiving office according to the naming convention is imported.<br />Example: Data collection point ZENTRALGKK  LFDNR the last imported file = 5 (see Agreement with Receiving Office). The system tries to import the file Kr000006.<br />The directory where the file must be located is defined in the agreement with the receiving office.<br />Selecting the checkbox Log has no effect. The log is always displayed.<br />The log has not changed much compared to the previous version, that is, the log is not saved automatically and can no longer be displayed after you exit the transaction.<br />There are changes in the sorting of the displayed messages and in some checks (see also section 2).<br />The records are processed in the same order as they are in the file.<br />Any information or error messages for the messages are also displayed in this order of precedence.<br />For ätzen F.. records (error records), an error message is displayed. In this case, the system displays the contents of the Feldes Error Code field in the message. The error code is not converted to text.<br />For comment records (K12), the comment text is displayed as information in the log.<br />For error comment records (F12), the comment text is displayed as an error in the log.<br />If the check results (see section 2) are negative, corresponding messages are displayed in the log.<br />The message type (E, W, I) is fixed and cannot be changed. It is also not possible to suppress certain messages.<br />Each record of the file (excl. Final record) is created as a message in IS-H. The created EDI messages are then visible in the workbench (in the same way as the messages created or sent).<br />Error, warning, and information messages created by the processing of the record are attached to the imported messages. These messages are the same as those displayed in the log (for exceptions, see chapter 2).<br />Messages for cases that are locked or where the IV request is missing are imported as incorrect.<br />ATTENTION: This check only applies to messages of type K03.<br />These messages can be processed automatically. This means that these messages can be imported again by the report RNC301I1 (automatic processing) without the entire file being imported again. This report can be scheduled several times a day. For more information about the report, see the online help.<br />All other messages with errors must be processed manually. You can use the report RNC301I2 (Workbench Button Receipt List) for a better overview and processing. For more information about the report, see the online help.<br /></p> <UL><LI>2 CHECKS AND ACTIONS</LI></UL> <UL><UL><LI>2.1 Preprocessing Record (K00)</LI></UL></UL> <UL><UL><LI>2.1.1 Checks</LI></UL></UL> <p>                    The system checks that:<br />- Each insurance provider block starts with a preliminary record<br />- the insurance provider exists in the system (institute number)<br />- The hospital number occurs in one of the institutions in the system. Here are  2 options:<br />a) The KA number refers to the institution that is currently being imported (according to the report selection): The block is then processed further.<br />b) The KA number refers to the institution that exists in the system (cross-client), but this is not the one that is currently being imported (according to the report selection): the block is ignored (this is processed if the report is started with the correct institution). This is the case, for example, if a file for several institutions is delivered by the receiving office).</p> <UL><UL><LI>2.1.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system.<br />- If an error occurs, the file is rejected completely.<br />- The file is flagged as imported and contains errors.<br />- A corresponding message is displayed in the log (CAUTION only in the log).</p> <UL><UL><LI>2.2 Final record (K99)</LI></UL></UL> <p>                    2.2. 1 Checks<br />The system checks that:<br />- Each insurance provider block is closed with a closing record<br />- the number of records in the block matches the number in the final record<br />- the insurance provider exists in the system (institute number) and is the same as in the preliminary record</p> <UL><UL><LI>2.2.2 Actions<br />- The message is NOT stored in the system.<br />- If an error occurs, the file is rejected completely.<br />- The file is flagged as imported and contains errors.<br />- A corresponding message is displayed in the log (CAUTION only in the log).</LI></UL></UL> <UL><UL><LI>2.3 VZE (K03)</LI></UL></UL> <UL><UL><LI>2.3.1 Checks</LI></UL></UL> <p>                    The system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider<br />- the case is not locked<br />- The previous care days and the exemptions match those of the health insurance fund (only for billed PPA services). If this is not the case, the system issues a warning. An error message is not necessary because the error is made aware of the error during final settlement at the latest.<br />- If the KComment Follows indicator is set, the comment is actually followed (K12 or F12)</p> <UL><UL><LI>2.3.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system<br />- If the commitment already exists, the system issues an information message.<br />Previous care days and exemption reason are updated only if the imported message is another confirmation.<br />- If a confirmation exists and a rejection is imported, the IV data is not updated. An error message is issued. The message is marked as incorrect.<br />- If a rejection exists and a confirmation is imported, the rejection is not reset, but previous days and exemption are imported. An error message is issued. The message is marked as incorrect.<br />- The IV data is also stored in the table NKSA (transaction NK50). If an error occurs, the message is flagged as incorrect and an error message is issued.<br />- If the KB services are billed and a rejection is imported, the system issues a warning.<br />- If the case has been final billed and the (n)previous days in the system or Exemption does not match the imported values, an error message is displayed in the log and the message is created as incorrect with a corresponding error message.<br />- If the case is locked or the IV request is missing, the message is created as Automatically Processable with Errors.</p> <UL><UL><LI>2.4 Reversal of Advance Payment (K04)</LI></UL></UL> <UL><UL><LI>2.4.1 Audits</LI></UL></UL> <p>                    The system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider</p> <UL><UL><LI>2.4.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system and marked as incorrect. The case must be revised manually.</p> <UL><UL><LI>2.5 Admission Notification Error (F01)</LI></UL></UL> <UL><UL><LI>2.5.1 Checks</LI></UL></UL> <p>                    The system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider</p> <UL><UL><LI>2.5.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system.<br />- The last admission notification sent for the insurance provider is assigned the status Rejected with Errors. This means that the next admission notification for the insurance provider is reported without canceling the old message. If no message is found with the status Shipped and Unconfirmed, a warning is displayed.</p> <UL><UL><LI>2.6 Error in discharge notification (F05)</LI></UL></UL> <UL><UL><LI>2.6.1 Checks</LI></UL></UL> <p>                    The system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider</p> <UL><UL><LI>2.6.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system<br />- The last discharge notification sent for the insurance provider is assigned the status With Errors Rejected.  This means that the next discharge notification for the insurance provider is reported without canceling the old message. If no message is found with the status Shipped and Unconfirmed, a warning is displayed.</p> <UL><UL><LI>2.7 Comment Record (K12)</LI></UL></UL> <UL><UL><LI>2.7.1 Checks</LI></UL></UL> <p>                    The system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider</p> <UL><UL><LI>2.7.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system<br />- The comment text is displayed in the log as information and is attached to the message.</p> <UL><UL><LI>2.8 Error in comment record (F12)</LI></UL></UL> <UL><UL><LI>2.8.1 Checks</LI></UL></UL> <p>                    The system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider</p> <UL><UL><LI>2.8.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system<br />- The comment text is displayed in the log as information and is attached to the message.</p> <UL><UL><LI>2.9 Other Record Types</LI></UL></UL> <UL><UL><LI>2.9.1 Checks</LI></UL></UL> <p>                    Is the message type supported by data exchange?<br />the system checks that:<br />- the case exists in the institution to be imported<br />- The insurance provider is active in the case<br />- An IV request exists for the insurance provider<br /></p> <UL><UL><LI>If the message type is unknown:</LI></UL></UL> <p>                    - no checks are performed.</p> <UL><UL><LI>2.9.2 Actions</LI></UL></UL> <p>                    - The message is stored in the system<br /></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction Codes", "Value": "CLEAR"}, {"Key": "Transaction Codes", "Value": "MOVE"}, {"Key": "Transaction Codes", "Value": "HERE"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (C5008711)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (C5008711)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000498973/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "499887", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H AT: ELDA- NK50 (Deactivate Time Overlap Check)", "RefUrl": "/notes/499887"}, {"RefNumber": "445076", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H EDI Note 441061: Empty list in batch", "RefUrl": "/notes/445076"}, {"RefNumber": "363157", "RefComponent": "IS-H", "RefTitle": "IS-H AT: Collective Note for Release 4.62", "RefUrl": "/notes/363157"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "445076", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H EDI Note 441061: Empty list in batch", "RefUrl": "/notes/445076 "}, {"RefNumber": "363157", "RefComponent": "IS-H", "RefTitle": "IS-H AT: Collective Note for Release 4.62", "RefUrl": "/notes/363157 "}, {"RefNumber": "499887", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H AT: ELDA- NK50 (Deactivate Time Overlap Check)", "RefUrl": "/notes/499887 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "462B", "To": "462C", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "463", "To": "463", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 462C", "SupportPackage": "SAPKIPH929", "URL": "/supportpackage/SAPKIPH929"}, {"SoftwareComponentVersion": "IS-H 463", "SupportPackage": "SAPKIPHC06", "URL": "/supportpackage/SAPKIPHC06"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0000498973/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "403C", "ValidTo": "463", "Number": "441061 ", "URL": "/notes/441061 ", "Title": "IS-H RNC301I0 Runtime error due to too large list", "Component": "IS-H-CM-OUT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=498973&TargetLanguage=EN&Component=IS-H-CM-OUT&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/498973/D\" target=\"_blank\">/notes/498973/D</a>."}}}}