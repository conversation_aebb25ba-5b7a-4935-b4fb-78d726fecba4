{"Request": {"Number": "604962", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3381, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003092542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000604962?language=E&token=2839AD80FA517032D2E27AF4D7C797BF"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000604962", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000604962/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "604962"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2003"}, "SAPComponentKey": {"_label": "Component", "value": "IS-H-CM-OUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital External Data Transmission"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Communication", "value": "IS-H-CM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-CM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital External Data Transmission", "value": "IS-H-CM-OUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-CM-OUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "604962 - IS-H DE: P21 - Support for Individual Data Determination"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=604962&TargetLanguage=EN&Component=IS-H-CM-OUT&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/604962/D\" target=\"_blank\">/notes/604962/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The determination of the data according to §21 KhEntgG using the report RNAP21F01 may return many error messages because there may be individual case constellations that the report cannot completely take into account.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>DRG BADI ish_p21_transfer</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Individual mapping of case constellations cannot be fully covered by the procedure of report RNAP21F01 for determining data according to §21.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>This SAP Note enables you to recognize and, if necessary, correct individual circumstances during the creation process of the individual files FAB, FALL, ICD, and OPS.<br /><br />To do this, perform the following steps in the specified sequence:<br /><br />Step 1:<br />Unpack one of the attached files accordingly.</p> <UL><LI>For IS-H Version 4.63/18, the file P21_463.ZIP</LI></UL> <UL><LI>For IS-H Version 4.63B/8, the file P21_436B.ZIP</LI></UL> <UL><LI>Note that the attached files cannot be downloaded using OSS, but can only be downloaded from SAP Service Marketplace (see also Note 480180). For more information, see SAP Note 13719 for information about importing attachments.</LI></UL> <p><br />Step2:<br />Import the unpacked requests into your system. After you import the order, you can use the Business Add-In ISH_P21_TRANSFER with the methods GET_P21_FALL, GET_P21_FAB, GET_P21_ICD, and GET_P21_OPS to determine the §21 data individually. For more information, see the documentation for the Business Add-In.<br /><br />Step3:<br />Then implement the correction instructions belonging to this SAP Note that enable the calls of the individual methods of the Business Add-In in the report RNAP21F01.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON> (D030297)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (D035931)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000604962/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "p21_463.zip", "FileSize": "18", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000229562003&iv_version=0004&iv_guid=C51757E3DE5A444FAD0261386F9C9808"}, {"FileName": "P21_463B.zip", "FileSize": "19", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000229562003&iv_version=0004&iv_guid=E77B61B3EC7D4F408B1AF8179A2FA998"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "622165", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - Intercompetitive Dialysis and Surgery Facilities", "RefUrl": "/notes/622165"}, {"RefNumber": "614691", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - FAB Key for Special Institutions", "RefUrl": "/notes/614691"}, {"RefNumber": "606556", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - Program termination during age calculation", "RefUrl": "/notes/606556"}, {"RefNumber": "597176", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H: P21: Delivery of DRG Files", "RefUrl": "/notes/597176"}, {"RefNumber": "520950", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H: Data Transmission According to P21 KHEntgG", "RefUrl": "/notes/520950"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "597176", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H: P21: Delivery of DRG Files", "RefUrl": "/notes/597176 "}, {"RefNumber": "622165", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - Intercompetitive Dialysis and Surgery Facilities", "RefUrl": "/notes/622165 "}, {"RefNumber": "614691", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - FAB Key for Special Institutions", "RefUrl": "/notes/614691 "}, {"RefNumber": "606556", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - Program termination during age calculation", "RefUrl": "/notes/606556 "}, {"RefNumber": "520950", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H: Data Transmission According to P21 KHEntgG", "RefUrl": "/notes/520950 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463", "To": "463", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463", "SupportPackage": "SAPKIPHC19", "URL": "/supportpackage/SAPKIPHC19"}, {"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD09", "URL": "/supportpackage/SAPKIPHD09"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0000604962/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463", "ValidTo": "463", "Number": "597176 ", "URL": "/notes/597176 ", "Title": "IS-H: P21: Delivery of DRG Files", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463", "ValidTo": "463", "Number": "602162 ", "URL": "/notes/602162 ", "Title": "IS-H DE: P21 - Specialties Outside KHEntgG", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463", "ValidTo": "463", "Number": "602838 ", "URL": "/notes/602838 ", "Title": "IS-H DE: P21 - Alternatives for Data Determination", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463", "ValidTo": "463", "Number": "603271 ", "URL": "/notes/603271 ", "Title": "IS-H DE: P21 - Preadmission and Postdischarge Visits", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "597176 ", "URL": "/notes/597176 ", "Title": "IS-H: P21: Delivery of DRG Files", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "602162 ", "URL": "/notes/602162 ", "Title": "IS-H DE: P21 - Specialties Outside KHEntgG", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "602838 ", "URL": "/notes/602838 ", "Title": "IS-H DE: P21 - Alternatives for Data Determination", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "603271 ", "URL": "/notes/603271 ", "Title": "IS-H DE: P21 - Preadmission and Postdischarge Visits", "Component": "IS-H-CM-OUT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=604962&TargetLanguage=EN&Component=IS-H-CM-OUT&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/604962/D\" target=\"_blank\">/notes/604962/D</a>."}}}}