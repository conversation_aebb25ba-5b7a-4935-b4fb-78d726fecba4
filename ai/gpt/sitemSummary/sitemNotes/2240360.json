{"Request": {"Number": "2240360", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 629, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018203192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002240360?language=E&token=F234D582A5A6D014356EC105825CBF96"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002240360", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002240360/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2240360"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 29}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2240360 - SAP S/4HANA: Always-On Business Functions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In SAP S/4HANA Business Functions can have the following status: always_on , customer_switchable , and always_off . This results in the following behavior:</p>\r\n<p>&#9679; always_off: If a business function was switched on in the SAP ERP source system, but is defined as always_off in the SAP S/4HANA target release, then a system conversion is not possible at the current point in time. On newly installed SAP S/4HANA systems it's not possible to acivate always_off Business Functions in the first place.</p>\r\n<p>&#9679; always_on: If a business function was not swithced on in the SAP ERP source system, but is defined as always_on in the SAP S/4HANA&#160;target release, then the business function will be automatically activated during the conversion. On newly installed SAP S/4HANA systems always_on&#160;Business Functions will be activated right from the beginning, like functionality that is not switched at all.</p>\r\n<p>&#9679; customer_switchable: Business Functions behave as in SAP ERP and can be switched on per individual decision of the customer.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For a full list of always_on Business Functions in SAP S/4HANA releases and feature packages please refer to the attachments.</p>\r\n<ul>\r\n<li>SAP S/4HANA 1709 initial shipment see attachment ALWAYS_ON_BFS_S4H_1709.txt</li>\r\n<li>SAP S/4HANA 1709 feature pack stack 1 (FPS1)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_1709_FPS1.txt</li>\r\n<li>SAP S/4HANA 1709 feature pack stack 2 (FPS2)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_1709_FPS2.txt</li>\r\n</ul>\r\n<ul>\r\n<li>SAP S/4HANA 1809 initial shipment see attachment ALWAYS_ON_BFS_S4H_1809.txt</li>\r\n<li>SAP S/4HANA 1809 feature pack stack 1 (FPS1)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_1809_FPS1.txt</li>\r\n<li>SAP S/4HANA 1809 feature pack stack 2 (FPS2)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_1809_FPS2.txt<br /><br /></li>\r\n<li>SAP S/4HANA 1909 initial shipment see attachment ALWAYS_ON_BFS_S4H_1909.txt</li>\r\n<li>SAP S/4HANA 1909 feature pack stack 1 (FPS1)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_1909_FPS1.txt</li>\r\n<li>SAP S/4HANA 1909 feature pack stack 2 (FPS2)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_1909_FPS2.txt<br /><br /></li>\r\n<li>SAP S/4HANA 2020 initial shipment,&#160;feature pack stack 1 (FPS1) and&#160;feature pack stack 2 (FPS2)&#160;see attachment ALWAYS_ON_BFS_S4H_2020.txt<br /><br /></li>\r\n<li>SAP S/4HANA 2021 initial shipment see attachment ALWAYS_ON_BFS_S4H_2021.txt</li>\r\n<li>SAP S/4HANA 2021 feature pack stack 1 (FPS1)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_2021_FPS1.txt</li>\r\n<li>SAP S/4HANA 2021 feature pack stack 2 (FPS2)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_2021_FPS2.txt<br /><br /></li>\r\n<li>SAP S/4HANA 2022 initial shipment see attachment ALWAYS_ON_BFS_S4H_2022.txt</li>\r\n<li>SAP S/4HANA 2022 feature pack stack 1 (FPS1)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_2022_FPS1.txt</li>\r\n<li>SAP S/4HANA 2022 feature pack stack 2 (FPS2)&#160;see attachment&#160;ALWAYS_ON_BFS_S4H_2022_FPS2.txt<br /><br /></li>\r\n<li>SAP S/4HANA 2023 initial shipment see attachment ALWAYS_ON_BFS_S4H_2023.txt</li>\r\n<li>SAP S/4HANA 2023&#160;feature pack stack 1 (FPS1): no changes compared to initial shipment</li>\r\n</ul>\r\n<p>For detailed information on the functionality behind each of these Business Functions please refer to the documentation of the corresponding Business Function in the SAP help portal.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041393)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019168)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002240360/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ALWAYS_ON_BFS_S4H_1909.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D5921ED9B68D1DE4387CE0CF"}, {"FileName": "ALWAYS_ON_BFS_S4H_1809.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D5921ED9B68D21B7896860CF"}, {"FileName": "ALWAYS_ON_BFS_S4H_1809_FPS1.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D6221ED9B68D21E08A05A0D3"}, {"FileName": "ALWAYS_ON_BFS_S4H_1809_FPS2.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D5821ED9B68D228AA2D8A0DC"}, {"FileName": "ALWAYS_ON_BFS_S4H_1709.txt", "FileSize": "4", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=6CAE8B28A04B1ED7A78ABF592A3720C8"}, {"FileName": "ALWAYS_ON_BFS_S4H_1709_FPS1.txt", "FileSize": "4", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=001999D61DA61ED8819E6BFDA47D80C7"}, {"FileName": "ALWAYS_ON_BFS_S4H_1709_FPS2.txt", "FileSize": "4", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=6EAE8B27F6791ED896CFCA55A148E0CD"}, {"FileName": "ALWAYS_ON_BFS_S4H_2020.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36BCAE1EDB80B004E9974A60E2"}, {"FileName": "ALWAYS_ON_BFS_S4H_1909_FPS2.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D6A21EDAA3DC5687936680DB"}, {"FileName": "ALWAYS_ON_BFS_S4H_2021.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D5D21EDC8AFE7BD5091B4A96"}, {"FileName": "ALWAYS_ON_BFS_S4H_1909_FPS1.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D7321EDA93B3F99DD80260E4"}, {"FileName": "ALWAYS_ON_BFS_S4H_2021_FPS1.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D5BA1EECA4FB4739C39ED7C1"}, {"FileName": "ALWAYS_ON_BFS_S4H_2021_FPS2.txt", "FileSize": "5", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D58A1EDCB7817143D04A34E8"}, {"FileName": "ALWAYS_ON_BFS_S4H_2022.txt", "FileSize": "6", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36BCB61EDD91ABB4C4A6812E17"}, {"FileName": "ALWAYS_ON_BFS_S4H_2022_FPS1.txt", "FileSize": "6", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=0090FAAA5DF81EEDACBEB50341740BF0"}, {"FileName": "ALWAYS_ON_BFS_S4H_2023.txt", "FileSize": "6", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=0090FAAA5DF81EEE99E89D0ED78BEBFD"}, {"FileName": "ALWAYS_ON_BFS_S4H_2022_FPS2.txt", "FileSize": "6", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001981902015&iv_version=0029&iv_guid=00109B36D73A1EDEA2A8761E6E19A886"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1641394", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SFW5: How-to activate Business Function", "RefUrl": "/notes/1641394 "}, {"RefNumber": "2847109", "RefComponent": "XX-SER-MCC", "RefTitle": "MCC FAQ: S/4HANA On Premise", "RefUrl": "/notes/2847109 "}, {"RefNumber": "3348949", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2023: Restriction note", "RefUrl": "/notes/3348949 "}, {"RefNumber": "3230844", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2022: Restriction Note", "RefUrl": "/notes/3230844 "}, {"RefNumber": "3079550", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2021: Restriction Note", "RefUrl": "/notes/3079550 "}, {"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206 "}, {"RefNumber": "2769186", "RefComponent": "TM-CF", "RefTitle": "System Transformation SAP TM to TM in SAP S/4HANA", "RefUrl": "/notes/2769186 "}, {"RefNumber": "2659710", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Restriction Note", "RefUrl": "/notes/2659710 "}, {"RefNumber": "2491467", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Restriction Note", "RefUrl": "/notes/2491467 "}, {"RefNumber": "2333141", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1610: Restriction Note", "RefUrl": "/notes/2333141 "}, {"RefNumber": "2549751", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "1610 FPS02: New Always On Business Functions Have Status Off", "RefUrl": "/notes/2549751 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}