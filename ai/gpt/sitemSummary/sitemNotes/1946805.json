{"Request": {"Number": "1946805", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 335, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017768532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001946805?language=E&token=8D6EE9B2B5C95E6E1C2BEAFC77CD665D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001946805", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001946805/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1946805"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1946805 - Installation / Delta Update of EHSM 500 on SAP ERP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to install/upgrade to \"SAP EHS MGMT. EXTENSION 5.0\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, add-on, installation, EHSM, 500, SAPK-500AGINEHSM, SAPK-500BGINEHSM, Component extension 5.0 for SAP Environment, Health, and Safety Management, MOPZ, MOB SAFETY ISSUE INT 1.0.0, LWMEHS01 605</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to install \"SAP EHS MGMT. EXTENSION 5.0\".<br /><br />You have the following installation options:</p>\r\n<p>a) You already have EHP7 FOR SAP ERP 6.0/EHP8 FOR SAP ERP 6.0 installed. You can install / upgrade to EHSM 500 via SAINT.</p>\r\n<p>b) You need to upgrade to EHP7 FOR SAP ERP 6.0: Include the installation package of EHSM 500 while calculating the target stack, so that EHSM 500 is installed/upgraded together with the ERP upgrade.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>This note is updated on a regular basis. Make sure you have the current version of this note before you start the installation.</strong></p>\r\n<p style=\"padding-left: 30px;\"><br />Contents<br />&#160;&#160;1.&#160; &#160;Change history<br />&#160;&#160;2.&#160; &#160;Prerequisites for installing the EHSM 500 add-on<br />&#160;&#160;3.&#160;&#160; Prerequisites for executing a Delta Update to EHSM 500<br />&#160;&#160;4.&#160;&#160; Preparing the EHSM 500 installation<br />&#160;&#160;5.&#160;&#160; Executing the EHSM 500 installation<br />&#160;&#160;6.&#160;&#160; Preparing the EHSM 500 delta update<br />&#160;&#160;7.&#160;&#160; Executing the EHSM 500 delta update.<br />&#160;&#160;8.&#160;&#160; After you have installed the EHSM 500 add-on<br />&#160;&#160;9.&#160; &#160;Language support<br />&#160;10.&#160; Password<br />&#160;11.&#160; Known issues<br />&#160;12. &#160;Installation of EHSM 500 on top of Compliance for Products 2.2(CfP 2.2) or on top of SAP Product and REACH Compliance 2.0 (SPRC 2.0)<br /><br /><strong>&#160;&#160;&#160; </strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>&#160;&#160;&#160;&#160;&#160; 1. Change History:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" style=\"padding-left: 60px;\">\r\n<tbody>\r\n<tr><th>Date</th><th>Topic</th><th>Short Description</th></tr>\r\n<tr>\r\n<td>11.06.2013</td>\r\n<td>Note Created</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 2. Prerequisites for installing the EHSM 500 add-on</strong></p>\r\n<p style=\"padding-left: 30px;\">It is not possible to uninstall EHSM 500.<br />Before you install EHSM 500, keep in mind that you cannot uninstall ABAP add-ons. You will need to implement the following notes, before installing EHSM 500.</p>\r\n<p style=\"padding-left: 30px;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; 1925447 - Feature request: create Exit method for GET_QUICKVIEW_KEY</p>\r\n<p style=\"padding-left: 30px;\">Kindly refer to the Master Guide at :</p>\r\n<p style=\"padding-left: 30px;\">Folder: <a target=\"_blank\" href=\"http://service.sap.com/&#126;form/sapnet?_SHORTKEY=01100035870000767444\">http://service.sap.com/&#126;form/sapnet?_SHORTKEY=01100035870000767444</a></p>\r\n<p style=\"padding-left: 30px;\">Document: <a target=\"_blank\" href=\"https://websmp105.sap-ag.de/&#126;sapidb/011000358700000371422014E/EHSM_5_Master_Guide.pdf\">https://websmp105.sap-ag.de/&#126;sapidb/011000358700000371422014E/EHSM_5_Master_Guide.pdf</a></p>\r\n<p style=\"padding-left: 30px;\">1934349 - Overwrite of the authorization checks of the standard class /BOBF/CL_FRW_AUTHORITY_CHECK</p>\r\n<p style=\"padding-left: 30px;\">Further restrictions that concern the upgrade and maintenance of your SAP system and that occur as a result of installing an add-on are described in release strategy note 1489703.</p>\r\n<ul style=\"padding-left: 30px;\">\r\n<ul style=\"padding-left: 30px;\">\r\n<li style=\"padding-left: 30px;\">Required release:<br />SAP ERP 6.0 EHP7 / EHP8 FOR SAP ERP 6.0</li>\r\n<li style=\"padding-left: 30px;\">Import the latest R3trans and tp. Ensure that you have imported the latest kernel version into you system. If a newer version is available on SAP Service Marketplace import the most recent kernel.</li>\r\n</ul>\r\n<li style=\"padding-left: 30px;\">Obtain the following notes before you begin the installation:<br />Add-ons: Conditions: 70228<br />Release strategy Note: 1489703<br />Problems with transaction SAINT: 822380<br />Release Information Note (RIN):1996021</li>\r\n<li style=\"padding-left: 30px;\">Prerequisites for EHSM 500.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Installation on SAP ERP 6.0 EHP7:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"padding-left: 30px;\">\r\n<tbody style=\"padding-left: 30px;\">\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">Component</td>\r\n<td style=\"padding-left: 30px;\">Release</td>\r\n<td style=\"padding-left: 30px;\">Support Package</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">EA-APPL</td>\r\n<td style=\"padding-left: 30px;\">617</td>\r\n<td style=\"padding-left: 30px;\">SAPK-61701INEAAPPL</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">EA-FIN</td>\r\n<td style=\"padding-left: 30px;\">617</td>\r\n<td style=\"padding-left: 30px;\">SAPK-61701INEAFIN</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_BASIS</td>\r\n<td style=\"padding-left: 30px;\">740</td>\r\n<td style=\"padding-left: 30px;\">SAPKB74003</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_GWFND</td>\r\n<td style=\"padding-left: 30px;\">740</td>\r\n<td style=\"padding-left: 30px;\">SAPK-74006INSAPGWFND</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_UI</td>\r\n<td style=\"padding-left: 30px;\">740</td>\r\n<td style=\"padding-left: 30px;\">SAPK-74007INSAPUI</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p style=\"padding-left: 30px;\">Installation on SAP ERP 6.0 EHP8:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"padding-left: 30px;\">\r\n<tbody style=\"padding-left: 30px;\">\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">Component</td>\r\n<td style=\"padding-left: 30px;\">Release</td>\r\n<td style=\"padding-left: 30px;\">Support Package</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_FIN</td>\r\n<td style=\"padding-left: 30px;\">618</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_APPL</td>\r\n<td style=\"padding-left: 30px;\">618</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_BASIS</td>\r\n<td style=\"padding-left: 30px;\">750</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_GWFND</td>\r\n<td style=\"padding-left: 30px;\">750</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_UI</td>\r\n<td style=\"padding-left: 30px;\">750</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p style=\"padding-left: 60px;\">Additional information about the installation:<br />CD material number add-on installation - 51048251.</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 3. Prerequisites for executing a Delta Update to EHSM 500</strong></p>\r\n<ul>\r\n<li>It is not possible to uninstall EHSM 500. Keep in mind that you cannot uninstall ABAP add-ons. Further restrictions that concern the upgrade and maintenance of your SAP system and that occur as a result of installing an add-on are described in release strategy note 1489703.</li>\r\n<li>Upgrade Paths:</li>\r\n<ul>\r\n<li>You can execute a Delta upgrade to EHSM 500 from EHSM 100 SP01/EHSM 200/EHSM 300/EHSM 400</li>\r\n<li>You can execute an Upgrade from a system with \"LWMEHS01 605\" and an older EHSM release to EHSM 500. LWMEHS01 605 was retrofitted into EHSM 400</li>\r\n</ul>\r\n<li>Import the latest R3trans and tp.</li>\r\n<li>Obtain the following notes before you begin the Delta Update:<br />Add-ons: Conditions: 70228<br />Release strategy Note: 1489703<br />Problems with transaction SAINT: 822380<br />Release Information Note (RIN):1996021</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Prerequisites for Update to EHSM 500 on \"EHP7 FOR SAP ERP 6.0\"</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component</td>\r\n<td>Release</td>\r\n<td>Support Package</td>\r\n</tr>\r\n<tr>\r\n<td>EA-APPL</td>\r\n<td>617</td>\r\n<td>SAPK-61701INEAAPPL</td>\r\n</tr>\r\n<tr>\r\n<td>EA-FIN</td>\r\n<td>617</td>\r\n<td>SAPK-61701INEAFIN</td>\r\n</tr>\r\n<tr>\r\n<td>EHSM</td>\r\n<td>100</td>\r\n<td>SAPK-10001INEHSM*</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>740</td>\r\n<td>SAPKB74003</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_GWFND</td>\r\n<td>740</td>\r\n<td>SAPK-74006INSAPGWFND</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_UI</td>\r\n<td>740</td>\r\n<td>SAPK-74007INSAPUI</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Prerequisites for Update to EHSM 500 on \"EHP8 FOR SAP ERP 6.0\"</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"padding-left: 30px;\">\r\n<tbody style=\"padding-left: 30px;\">\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">Component</td>\r\n<td style=\"padding-left: 30px;\">Release</td>\r\n<td style=\"padding-left: 30px;\">Support Package</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_FIN</td>\r\n<td style=\"padding-left: 30px;\">618</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_APPL</td>\r\n<td style=\"padding-left: 30px;\">618</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_BASIS</td>\r\n<td style=\"padding-left: 30px;\">750</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_GWFND</td>\r\n<td style=\"padding-left: 30px;\">750</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td style=\"padding-left: 30px;\">SAP_UI</td>\r\n<td style=\"padding-left: 30px;\">750</td>\r\n<td style=\"padding-left: 30px;\"></td>\r\n</tr>\r\n<tr>\r\n<td>&#160;&#160;&#160;&#160;&#160;EHSM</td>\r\n<td>&#160;&#160;&#160;&#160;&#160; 100</td>\r\n<td>SAPK-10001INEHSM*</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>You can also have EHSM 200 SP00&#160; OR EHSM 300 SP00 OR EHSM 400 SP00, and then, execute an upgrade to EHSM 500 If you have not yet installed these Component Support Packages, you can include them during the upgrade to EHSM 500. For more information, see Note 83458.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Kindly download the ACP file \"EHSM======500\" (EPS File Name: CSR0120031469_0086852.PAT) to&#160;install/upgrade EHSM 500 in a system having EA-FIN 720 and SAP_FIN 720.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Additional Component Support Packages<br />-/-</li>\r\n<li>Required SAP Industry Solutions<br />-/-</li>\r\n<li>Additional information about the Delta Update:<br />DVD material number: 51048251</li>\r\n</ul>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;4. Preparing the EHSM 500 installation</strong></p>\r\n<ul>\r\n<li>Making the Add-On EHSM 500 available.</li>\r\n</ul>\r\n<p>The installation CD for EHSM 500 is not automatically sent to all customers. Request the CD with material number 51048251 from your local subsidiary or download the CD from SAP Service Marketplace. The CD/DVD can be found in Service Market Place at the following location: http://service.sap.com/swdc -&gt; Installations &amp; Upgrades -&gt; Browse our Download Catalog&#160;&#160;-&gt; SAP Application Components&#160;&#160;-&gt; SAP EHS Management -&gt; SAP EHS MGMT. EXTENSION -&gt; SAP EHS MGMT. EXTENSION 5.0<br /><br />Log on as user:</p>\r\n<p>&lt;sid&gt;adm on UNIX<br />&lt;SID&gt;OFR on IBM i (previously i5/OS or OS/500)<br />&lt;SID&gt;adm on Microsoft Windows</p>\r\n<ul>\r\n<li>Switch to the &lt;DIR_EPS_ROOT&gt; directory of your SAP system usually &lt;DIR_TRANS&gt;/EPS. The &lt;DIR_EPS_ROOT&gt; directory is also displayed under DIR_EPS_ROOT after you execute the RSPFPAR report.</li>\r\n<li>Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;.</li>\r\n<li>Unpack the SAR archive K-500AGINEHSM.SAR on the CD with the following statement:</li>\r\n</ul>\r\n<p>UNIX:<br />SAPCAR -xvf /&lt;CD_DIR&gt;/EHSM_500_INST/DATA/K-500AGINEHSM.SAR<br />IBM i (previously i5/OS or OS/500):<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/EHSM_500_INST/DATA/K-500AGINEHSM.SAR'<br />Microsoft Windows:<br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\EHSM_500_INST\\DATA\\K-500AGINEHSM.SAR<br />The CSR0120031469_0086850.PAT file should now be in the &lt;DIR_EPS_ROOT&gt;/in directory.</p>\r\n<p style=\"padding-left: 30px;\"><strong>5. Executing the EHSM 500 installation</strong></p>\r\n<p style=\"padding-left: 30px;\">User to be used<br />Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</p>\r\n<ul>\r\n<ul>\r\n<li>Display the add-on installation package:<br />Call transaction SAINT and choose 'Start' and 'Load'. After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing F3 or 'Back'.</li>\r\n<li>Starting the installation.<br />The add-on EHSM 500 can be installed</li>\r\n</ul>\r\n<li>On EHP7 FOR SAP ERP 6.0 system using SAINT</li>\r\n<li>during a delta update to EHP7 FOR SAP ERP 6.0 , as part of the Delta update itself. This would be done using SUM/MOPZ.</li>\r\n<ul>\r\n<li>Call transaction SAINT and choose 'Start'. Select the Add-On EHSM 500 and choose \"Continue\". If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the installation package, and it may also contain Support Packages and Add-On Support Packages. To start the installation process, choose 'Continue'. For more information, call transaction SAINT and choose 'Info' on the application toolbar.</li>\r\n<li>To install EHSM 500 along with a Delta Update to EHP7 FOR SAP ERP 6.0, refer to Release strategy note 1489703. The general procedure for Enhancement Packages is described in the Enhancement Package Installation Guide.Ensure that you use the most recent version of SUM, which is provided on SAP Service Marketplace.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>6. Preparing the EHSM 500 Delta Update</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Making the Add-On EHSM 500 available<br />Kindly refer to Chapter: 4, to know the path to the DVD with material number 51048251. The Delta Upgrade Package: K-500BGINEHSM.SAR can be found in the directory: EHSM_300_DELT. Uncar using the SAPCAR commands described in Chapter:4. Once you have uncared the SAR file, pat file:CSR0120031469_0086852.PAT should be in the&#160; &lt;DIR_EPS_ROOT&gt;/in directory.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>7. Executing the EHSM 500 delta upgrade</strong></p>\r\n<ul>\r\n<ul>\r\n<li>User to be used<br />Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</li>\r\n<li>Display the add-on Delta Upgrade package:<br />Call transaction SAINT and choose 'Start' and 'Load'. After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing F3 or 'Back'.</li>\r\n<li>Call transaction SAINT and choose 'Start'. Select the Add-On EHSM 500 and choose \"Continue\". If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the installation package, and it may also contain Support Packages and Add-On Support Packages. To start the installation process, choose 'Continue'. For more information, call transaction SAINT and choose 'Info' on the application toolbar.</li>\r\n<li>You can also upgrade to EHSM 500 using the Software Update Manager (SUM) during an ERP ehp upgrade to EHP7 FOR SAP ERP 6.0.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>8. After you have installed the EHSM 500 add-on</strong></p>\r\n<ul>\r\n<li style=\"padding-left: 30px;\">Generation errors</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">When installing EHSM 500 SP00&#160;on EA-FIN 700, you might encounter the following generation error:</p>\r\n<p style=\"padding-left: 60px;\">Program SAPLEHFND_BOBF, Include LEHFND_BOBFU01:<br />Function Module EHFND_SHLP_EXIT_BO_ACTION</p>\r\n<p style=\"padding-left: 60px;\">In PERFORM or CALL FUNCTION \"BO_ACTION_SELECT\", the actual parameter<br />\"RECORD_TAB\" is incompatible with the formal parameter \"P_RECORD_TAB\".</p>\r\n<p style=\"padding-left: 90px;\">Solution: Kindly implement the note: 2009414, or apply sp01 of EHSM 500(SAPK-50001INEHSM)</p>\r\n<p style=\"padding-left: 90px;\">Delivery Customizing<br />Delivery Customizing is imported into client 000 and may need to be copied to other clients. For more information, see Note 337623. Use the tool support in the SAP Solution Manager http://service.sap.com/solutionmanager to customize your processes and process steps. In the area of Product&#160; Compliance this will be optimized by using BC sets.</p>\r\n<ul>\r\n<li>Activation of services</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In transaction SICF activate all required WebDynpros services below the node \"/default_host/sap/bc/webdynpro/sap/\":</p>\r\n<ul>\r\n<ul>\r\n<li>All EHFND*, EHHSS* and EHPRC* services</li>\r\n<li>POWL</li>\r\n<li>IBO_WDA_INBOX</li>\r\n<li>WDR_CHIP_PAGE&#160;</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">For more information, refer to note 1088717.</p>\r\n<p style=\"padding-left: 60px;\">Activate other required services:</p>\r\n<ul>\r\n<ul>\r\n<li>The NWBC runtime node on the path \"/default_host/sap/bc/nwbc\". <br />Refer to link: <a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw74/helpdata/en/4c/5bdb8497817511e10000000a42189b/content.htm?frameset=/en/07/7042cc063f4c6dad72af7773a8ba2a/frameset.htm&amp;current_toc=/en/66/48a793bc2f4ec5bdb8e7e93ea6cd9f/plain.htm&amp;node_id=71\">http://help.sap.com/saphelp_nw74/helpdata/en/4c/5bdb8497817511e10000000a42189b/content.htm?frameset=/en/07/7042cc063f4c6dad72af7773a8ba2a/frameset.htm&amp;current_toc=/en/66/48a793bc2f4ec5bdb8e7e93ea6cd9f/plain.htm&amp;node_id=71</a></li>\r\n<li>The SAPUI5 Application handler on the path \"/default_host/sap/bc/ui5_ui5\".</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">For EHSM 500, Please activate all the WebDynpros services below the \"/default_host/sap/bc/webdynpro/sap/\" node in transaction SICF. All EHFND*, EHHSS* and EHPRC* services, POWL, IBO_WDA_INBOX and WDR_CHIP_PAGE&#160;service need to be activated. For more information, refer to note 1088717.</p>\r\n<p style=\"padding-left: 30px;\"><strong>9. Language support</strong></p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; In addition to German and English, the following languages are supported by EHSM 500:</p>\r\n<p style=\"padding-left: 30px;\">Arabic</p>\r\n<p style=\"padding-left: 30px;\">Catalan</p>\r\n<p style=\"padding-left: 30px;\">Czech</p>\r\n<p style=\"padding-left: 30px;\">Spanish</p>\r\n<p style=\"padding-left: 30px;\">Finnish</p>\r\n<p style=\"padding-left: 30px;\">French</p>\r\n<p style=\"padding-left: 30px;\">Hebrew</p>\r\n<p style=\"padding-left: 30px;\">Hungarian</p>\r\n<p style=\"padding-left: 30px;\">Italian</p>\r\n<p style=\"padding-left: 30px;\">Japanese</p>\r\n<p style=\"padding-left: 30px;\">Korean</p>\r\n<p style=\"padding-left: 30px;\">Dutch</p>\r\n<p style=\"padding-left: 30px;\">Norwegian</p>\r\n<p style=\"padding-left: 30px;\">Portuguese</p>\r\n<p style=\"padding-left: 30px;\">Russian</p>\r\n<p style=\"padding-left: 30px;\">Slovak</p>\r\n<p style=\"padding-left: 30px;\">Swedish</p>\r\n<p>&#160;&#160;&#160;&#160;&#160; Turkish</p>\r\n<p style=\"padding-left: 30px;\">Chinese trad</p>\r\n<p>&#160;&#160;&#160;&#160;&#160; Chinese</p>\r\n<ul>\r\n<li>All the language-dependent parts of EHSM are contained in the installation package of the add-on. If the relevant standard language exists in the system when you import EHSM, the language part of the add-on is automatically imported. For new language, you need to import a language transport separately.<br /><br />Note: whenever you plan to use EHSM together with languages requiring non-ANSI characters(like e.g.Hebrew, Arabic, Japanese, Chinese, Korean) a non-unicode system is not supported.<br />Refer to note 73606 for detailed information on Supported Languages and Code Pages in general.</li>\r\n<li>If you import a new standard language into your system after installing the EHSM add-on, you must manually ensure that the corresponding language-dependent part of the add-on is imported. See Note 195442 for further information.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>&#160;&#160;&#160;&#160;&#160; 10. Password:</strong></p>\r\n<p style=\"padding-left: 60px;\">If the transaction SAINT is used to install the add-on/upgrade to the add-on release: EHSM 500, the system prompts you to enter a password.</p>\r\n<p style=\"padding-left: 30px;\">&#160;&#160;&#160;&#160;&#160; This password is mentioned below:</p>\r\n<p style=\"padding-left: 30px;\">&#160;&#160;&#160;&#160;&#160; SAPK-500AGINEHSM - 53BDB63638</p>\r\n<p style=\"padding-left: 30px;\">&#160;&#160;&#160;&#160;&#160; SAPK-500BGINEHSM - 53BDB63538</p>\r\n<ol></ol>\r\n<p style=\"padding-left: 30px;\"><strong>&#160;&#160;&#160;&#160; &#160;11.Known Issues</strong></p>\r\n<p style=\"padding-left: 60px;\">Shared Memory:The size of shared memory should be about 250 MB. The usage of shared memory can be monitored in transaction SHMM.If you encounter an error when trying to navigate to Search in SAP Product Stewardship Network kindly apply note 1766375.</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;12. Installation of EHSM 500 on top of Compliance for Products 2.2 (CfP 2.2) or on top of SAP Product and REACH Compliance 2.0 (SPRC 2.0)</strong></p>\r\n<p style=\"padding-left: 60px;\">The installation stops with an ABAP dictionary activation error of table ESTVP.Solution: Kindly apply the note 1751276.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I039876)"}, {"Key": "Processor                                                                                           ", "Value": "I035156"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001946805/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001946805/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1996021", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 5.0 for SAP EHS Management: RIN", "RefUrl": "/notes/1996021 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EHSM", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}