{"Request": {"Number": "2403290", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 605, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014151252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002403290?language=E&token=BAC7F3450C10BB648A8D3DA53C4D4D38"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002403290", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002403290/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2403290"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.03.2021"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2403290 - RE-FX IT: Valuation Cashflow"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have enabled the valuation cash flow for Credit Memo postings.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>IFRS 16, leasing, valuation, Cashflow, Credit memo</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have switched on business function&#160;FIN_LOC_CI_20 to enable the&#160;Italian RE-FX localization. Also you have&#160;enabled the Valuation of leasing contracts in accordance with SAP Note 2255555.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>As a general rule, SAP recommends that you install a solution by applying a Support Package. However, if you need to install a solution earlier, use the Note Assistant to implement the correction instruction.</p>\r\n<p>You can find more information about the Note Assistant in SAP Service Marketplace, under service.sap.com/note-assistant.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Version</td>\r\n<td>Description</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>Adding EA-FIN 617</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I049136)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I503839)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002403290/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10004INS4CORE", "URL": "/supportpackage/SAPK-10004INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10102INS4CORE", "URL": "/supportpackage/SAPK-10102INS4CORE"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60619INEAAPPL", "URL": "/supportpackage/SAPK-60619INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 616", "SupportPackage": "SAPK-61611INEAAPPL", "URL": "/supportpackage/SAPK-61611INEAAPPL"}, {"SoftwareComponentVersion": "EA-FIN 617", "SupportPackage": "SAPK-61714INEAFIN", "URL": "/supportpackage/SAPK-61714INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61806INSAPFIN", "URL": "/supportpackage/SAPK-61806INSAPFIN"}, {"SoftwareComponentVersion": "EA-FIN 700", "SupportPackage": "SAPK-70011INEAFIN", "URL": "/supportpackage/SAPK-70011INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72007INSAPFIN", "URL": "/supportpackage/SAPK-72007INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73007INSAPFIN", "URL": "/supportpackage/SAPK-73007INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 3, "URL": "/corrins/0002403290/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002403290/19773"}, {"SoftwareComponent": "EA-FIN", "NumberOfCorrin": 2, "URL": "/corrins/0002403290/15842"}, {"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 2, "URL": "/corrins/0002403290/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1918820 ", "URL": "/notes/1918820 ", "Title": "Credit memo of an invoice posted more then 1 year ago", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1959261 ", "URL": "/notes/1959261 ", "Title": "Credit memo of an invoice posted more then 1 year ago - BAdIs", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1971777 ", "URL": "/notes/1971777 ", "Title": "Credit memo of an invoice posted more then 1 year ago", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1999237 ", "URL": "/notes/1999237 ", "Title": "Enhancement of internal function", "Component": "RE-FX-CN"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2011600 ", "URL": "/notes/2011600 ", "Title": "Credit Memo w/o VAT: Object related cash flow", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2092708 ", "URL": "/notes/2092708 ", "Title": "RE-FX Italy: Usage Measurement for Italy", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2121046 ", "URL": "/notes/2121046 ", "Title": "Credit Memo w/o VAT: FASB13", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2170129 ", "URL": "/notes/2170129 ", "Title": "Credit Memo w/o VAT: BAdI BADI_REXC_IT_CR_MEMO", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2186303 ", "URL": "/notes/2186303 ", "Title": "Rounding error when posting 0% VAT", "Component": "RE-FX-LC-IT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}