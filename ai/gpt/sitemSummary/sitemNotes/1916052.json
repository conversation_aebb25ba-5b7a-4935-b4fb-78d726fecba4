{"Request": {"Number": "1916052", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 245, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017724312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001916052?language=E&token=A8BAFD798F2CF3308E6A7FC94CC7FC97"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001916052", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1916052"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.09.2013"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-LDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Landscape Management Database (LMDB)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Management Database (LMDB)", "value": "SV-SMG-LDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-LDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1916052 - Automation in landscape management"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>It takes too much time and effort to maintain the landscape data in the Landscape Management Database (LMDB).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Landscape Management Database (LMDB)<br />System Landscape Directory (SLD)<br />Common Information Model (CIM)<br />System Landscape Directory (SLD), SLD data supplier<br />Technical systems<br />Automation landscape data<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The entry of the landscape information in the LMDB is largely automated. If the SLD Data Supplier is fully configured and the meta data is completely available in the system, the descriptions of the technical systems in SLD and LMDB are automatically up to date. However, depending on the version of SAP Solution Manager, SLD and participating technical systems, there are gaps in the automation of individual processes. This SAP Note summarizes existing gaps. It also describes existing solutions.<br /><br />An automation cannot be provided reliably in all cases. In these cases, the manual effort is reduced as much as possible instead.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following landscape management areas are not fully automated in all versions of the participating software.<br /><br /><B><U>The verification of a product system fails after an upgrade.</U></B><br />The product system was migrated with SAP Solution Manager 7.1 SP5 from SMSY to LMDB. Up to and including SAP Solution Manager 7.1 SP8, the product system is not adjusted automatically after the upgrade of the contained technical system. This gap was closed with SAP Solution Manager 7.1 SP9/10. In most cases, the adjusted status is to be calculated uniquely and the system performs an automatic adjustment. For details, refer to SAP Notes 1832868 and 1774194.<br /><br /><B><U>The \"Diagnostics relevant\" indicator is incorrect after the upgrade.</U></B><br />Up to and including SAP Solution Manager 7.1 SP8, there is no automatic adjustment of the \"Diagnostics relevant\" indicator after the upgrade of a technical system. As of SP9/10, the indicator is adjusted automatically in most cases. Refer to SAP Note 1832868 for details.<br /><br /><B><U>The information about the installed products is missing or incorrect.</U></B><br />The target of receiving the installed product instances of a technical system from the system itself correctly and in all cases has not been achieved yet. Unfortunately, an automatic correction in LMDB is not possible, because often, the correct status cannot be calculated uniquely. The correction procedure considered most effective so far is described in SAP Note 1816146. The intention is to solve the problem using complete and correct information in the technical system itself.<br /><br /><B><U>Changed host names lead to system duplicates.</U></B><br />The change of a host name in the system profiles can be reduced but it cannot be completely avoided by using unchangeable aliases. Due to the new host name, the CIM key \"Name\" of the system changes and a duplicate is created in SLD and LMDB. The case described can be clearly identified if the system number remains the same. the automatic renaming is available and is described in SAP Note 1727294. For further information, refer to SAP Notes 1694004 and 1052122.<br /><br /><B><U>Deinstalled application servers remain</U></B><br />The SLD Data Supplier reports application servers running at the time of registration. The permanent deinstallation of an application server can therefore not be detected; an immediate deletion in SLD does not make sense. SAP Note 1694072 describes the option of setting a parameter \"Lifetime\" for application servers. If the period during which an application server is missing in the system data exceeds the value of the parameter \"Lifetime\", the SLD deletes the application server automatically.<br /><br /><B><U>Deinstalled systems remain</U></B><br />For the deinstallation of a system, there is no automatic solution in the SLD. Often, systems are deleted at hardware level. For this reason, an automatic process is no longer possible. The manual deletion of a technical system in SLD is propagated in the LMDB and configurations based on this may become invalid due to the deletion. As an alternative to the deletion, as of SAP Solution Manager 7.1 SP9/10 you can set the \"Lifecycle Status\" in LMDB to \"Disused\".<br /><br /><B><U>Configuration changes in SAP Solution Manager applications</U></B><br />The applications in SAP Solution Manager must react to changes to the system landscape. Even though some applications already do this, the target has not been reached yet for SAP Solution Manager 7.1 SP9/10. Examples for existing gaps are the logical components, parts of SOLMAN_SETUP and parts of Monitoring. As of SP10, Monitoring reacts automatically to a host switch in a high availability configuration. The aim is to close all existing gaps.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CCM-SLD (System Landscape Directory / Component Repository)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D046840)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D032961)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001916052/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001916052/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1869701", "RefComponent": "SV-SMG-LDB", "RefTitle": "Overview of the SLD Data Suppliers of Technical Systems", "RefUrl": "/notes/1869701"}, {"RefNumber": "1832868", "RefComponent": "SV-SMG-LDB", "RefTitle": "Automatic Managed System Adjustment after Product Upgrade", "RefUrl": "/notes/1832868"}, {"RefNumber": "1816146", "RefComponent": "BC-UPG-MP", "RefTitle": "Correction of installed software information (CISI)", "RefUrl": "/notes/1816146"}, {"RefNumber": "1774194", "RefComponent": "SV-SMG-LDB", "RefTitle": "Installed products in LMDB and SLD", "RefUrl": "/notes/1774194"}, {"RefNumber": "1727294", "RefComponent": "BC-CCM-SLD-JAV", "RefTitle": "AS Java/ABAP System move functionality", "RefUrl": "/notes/1727294"}, {"RefNumber": "1694072", "RefComponent": "BC-CCM-SLD-JAV", "RefTitle": "Technical instances consistency management feature", "RefUrl": "/notes/1694072"}, {"RefNumber": "1694004", "RefComponent": "BC-CCM-SLD", "RefTitle": "Dealing with duplicate technical system names (SIDs)", "RefUrl": "/notes/1694004"}, {"RefNumber": "1052122", "RefComponent": "SV-SMG-LDB", "RefTitle": "Host names in SLD and LMDB", "RefUrl": "/notes/1052122"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1869701", "RefComponent": "SV-SMG-LDB", "RefTitle": "Overview of the SLD Data Suppliers of Technical Systems", "RefUrl": "/notes/1869701 "}, {"RefNumber": "1816146", "RefComponent": "BC-UPG-MP", "RefTitle": "Correction of installed software information (CISI)", "RefUrl": "/notes/1816146 "}, {"RefNumber": "1832868", "RefComponent": "SV-SMG-LDB", "RefTitle": "Automatic Managed System Adjustment after Product Upgrade", "RefUrl": "/notes/1832868 "}, {"RefNumber": "1694072", "RefComponent": "BC-CCM-SLD-JAV", "RefTitle": "Technical instances consistency management feature", "RefUrl": "/notes/1694072 "}, {"RefNumber": "1774194", "RefComponent": "SV-SMG-LDB", "RefTitle": "Installed products in LMDB and SLD", "RefUrl": "/notes/1774194 "}, {"RefNumber": "1694004", "RefComponent": "BC-CCM-SLD", "RefTitle": "Dealing with duplicate technical system names (SIDs)", "RefUrl": "/notes/1694004 "}, {"RefNumber": "1727294", "RefComponent": "BC-CCM-SLD-JAV", "RefTitle": "AS Java/ABAP System move functionality", "RefUrl": "/notes/1727294 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}