{"Request": {"Number": "2270530", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 260, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018250032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E0173912D3074D96213B81FB6E14A9F8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2270530"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.03.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-FSCM-TRM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Treasury and Risk Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Supply Chain Management", "value": "FIN-FSCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Treasury and Risk Management", "value": "FIN-FSCM-TRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2270530 - S4TWL - Several Kinds of Financial Objects Disabled"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S4TC, S/4 Transition</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong></p>\r\n<p>SEM Banking is not part of SAP S/4HANA edition. So the creation of the following outdated kinds of financial objects of SEM Banking&#160;has been disabled in&#160;function <em>Process Financial Objects</em> (transaction JBDO) of the&#160;Risk Analyzers of the <em>Treasury and Risk Management</em>:</p>\r\n<ul>\r\n<li>Security Order</li>\r\n<ul>\r\n<li>Namespace of Financial Object: T2*</li>\r\n<li>Master Data Maintenance Transaction: JBWPORD</li>\r\n<li>[IMPORTANT] The namespace T2* is shared between the security order of SEM Banking (maintained via transaction JBWPORD; Radiobutton \"Security Order\" in financial object master data transaction JBDO), and the security transaction of SAP Treasury and Risk Management (financial transaction created via FTR_CREATE or TS01; Radiobutton <em>Derivatives/Money Market/Forex/Listed Transaction/Security Transaction</em> in financial object master data transaction JBDO). The T2*-financial objects of SAP Treasury and Risk Management are continued to be supported.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Positions</li>\r\n<ul>\r\n<li>Namespace of Financial Object: B1*</li>\r\n<li>Master Data Maintenance Transactions: JBB0, JBB1. JBB2</li>\r\n<li>[IMORTANT] This is to be distinguished from the financial objects for different kinds of positions in SAP Treasury and Risk Management, like the securities account class position (namespace of financial object: T9*), class position in futures account (namespace of financial objects: TA*) or the lot-based position in futures account (namespace of financial objects: TF*) which are continued to be supported.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Variable Transactions (applies for the SAP Banking application component only)</li>\r\n<ul>\r\n<li>Namespace of Financial Object: BV*</li>\r\n<li>Master Data Maintenance Transactions:&#160;JBVT*&#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Positions of Fictitious/Simulated Transactions</li>\r\n<ul>\r\n<li>Namespace of Financial Object: R3*</li>\r\n<li>Master Data Maintenance Transaction: RTBST&#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Facilities in SAP Banking</li>\r\n<ul>\r\n<li>Namespace of Financial Object: F2*</li>\r\n<li>Master Data Maintenance Transactions: KLFZ01, KLFZ02. KLFZ03</li>\r\n<li>[IMORTANT] This is to be distinguished from the facility instrument&#160;in SAP Treasury and Risk Management (product category 560) which is continued to be supported in SAP S/4HANA edition. The namespace of the financial object for facilities of product category 560 is T5*.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Non-Interest Profit and Loss Items</li>\r\n<ul>\r\n<li>Namespace of Financial Object: PL*</li>\r\n<li>Master Data Maintenance Transaction: JBA_US_MD_NIPL</li>\r\n</ul>\r\n</ul>\r\n<p>The other&#160;financial objects of group 1 (= SAP Banking) and of&#160;group 2 (= Treasury and Risk Management objects) are still available.</p>\r\n<p>Customer code which makes use of coding objects which are specific to the disabled financial object types is no longer relevant.</p>\r\n<p><strong>Business Process Related Information</strong></p>\r\n<p>The SEM Banking is not part of SAP S/4HANA edition. So these objects are obsolete.</p>\r\n<p><strong>How to Determine Relevancy</strong></p>\r\n<p>Check in the Customizing of your&#160;system whether you&#160;activated&#160;the financial object integration for these financial objects or not.</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022054)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022210)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2726778", "RefComponent": "FIN-FSCM-TRM", "RefTitle": "S4TWL - Obsolete Transaction Codes and Programs in Treasury and Risk Management (TRM)", "RefUrl": "/notes/2726778 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}