{"Request": {"Number": "2479674", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 572, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018972852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002479674?language=E&token=D0360063A79ABCB4D33E1B2F9F23808E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002479674", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002479674/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2479674"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BW-B4H-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Conversion to SAP BW/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP BW/4HANA Starter Add-On", "value": "BW-B4H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Conversion to SAP BW/4HANA", "value": "BW-B4H-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2479674 - BW4SL & BWbridgeSL - Myself Source System"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use the \"Myself\" Source System and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>LSYS, RSDS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Run program&#160;RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\r\n<p>See node: Automated Cleanup --&gt; Deletions</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Target SAP BW/4HANA:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">The \"Myself\" Source System serves two data load scenarios and will not be available with SAP BW/4HANA:</p>\r\n<ol><ol>\r\n<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW: Such scenarios cannot be transferred to SAP BW/4HANA with the transfer tool. These scenarios are deprecated already from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider. From BW 7.30 transaction RSMIGRATE helps with migrating such scenarios to DTP loads.</li>\r\n<li>Extraction from customer-owned (prefixed with Z) or other application DataSources into the same BW: These scenarios need to be replaced by ODP extraction. Since SAP BW/4HANA does not allow to create load scenarios with the SAPI-Context, it is recommended to transfer them to the ODP-CDS. However for convenience transfer to ODP-SAPI is supported as well. Still, no new such ODP-SAPI DataSources can be created in BW/4 but ODP-CDS must be used. The transfer of the BW-DataSources (RSDS) requires that the corresponding SAPI-DataSources (OSOA) in the source system (which in myself case is the BW resp. BW/4 itself) are already available. These you have to manually transport from the sender to the receiver system prior to the transfer. Report&#160;RS_B4HANA_CREATE_OSOA_TRANSP is available to simplify the collection of the required objects on that transport.</li>\r\n</ol></ol>\r\n<p style=\"padding-left: 30px;\">When executing the switch to \"Ready for conversion mode\" the Myself-Source System will be automatically deleted, provided that there are no more DataSources existing for it. Else the deletion gives error and subsequently the conversion cannot be executed. This means that all Myself-DataSources must either be deleted or transferred like explained above.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Related Information</span></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2473145\">2473145</a> - BW4SL &amp; BWbridgeSL - BW &amp; SAP Source Systems</li>\r\n<li><a target=\"_blank\" href=\"/notes/2483299\">2483299</a> - BW4SL &amp; BWbridgeSL - Export DataSources (SAP BW/4HANA as Source)</li>\r\n<li><a target=\"_blank\" href=\"/notes/2443863\">2443863</a> - BW4SL &amp; BWbridgeSL - S-API DataSources (SAP BW/4HANA as Source)</li>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/46b9297716c94e509fcbe62d3c795e85.html\">Creating an ODP Source System</a></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Target Datasphere, SAP BW bridge:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">The \"Myself\" Source System serves two data load scenarios and will not be available with SAP Datasphere, SAP BW bridge:</p>\r\n<ol><ol>\r\n<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW: Such scenarios cannot be transferred to SAP Datasphere, SAP BW bridge with the transfer tool.&#160;These scenarios are deprecated already from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider. From BW 7.30 transaction RSMIGRATE helps with migrating such scenarios to DTP loads.</li>\r\n<li>Extraction from customer-owned (prefixed with Z) or other application DataSources into the same BW: These scenarios are not supported anymore with SAP Datasphere, SAP BW bridge.</li>\r\n</ol></ol>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Related Information</span></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2473145\">2473145</a>&#160;- BW4SL &amp; BWbridgeSL - BW &amp; SAP Source Systems</li>\r\n<li><a target=\"_blank\" href=\"/notes/2483299\">2483299</a>&#160;- BW4SL &amp; BWbridgeSL - Export DataSources (SAP BW/4HANA as Source)</li>\r\n<li><a target=\"_blank\" href=\"/notes/2443863\">2443863</a>&#160;- BW4SL &amp; BWbridgeSL - S-API DataSources (SAP BW/4HANA as Source)</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "DS-BWB (BW Bridge)"}, {"Key": "Other Components", "Value": "BW-WHM-DST-SRC (Source Systems)"}, {"Key": "Responsible                                                                                         ", "Value": "I822646"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028847)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002479674/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002479674/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2481315", "RefComponent": "BC-BW-ODP", "RefTitle": "Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations", "RefUrl": "/notes/2481315"}, {"RefNumber": "2480284", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - Hierarchy DataSources", "RefUrl": "/notes/2480284"}, {"RefNumber": "2473145", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - SAP and BW Source Systems", "RefUrl": "/notes/2473145"}, {"RefNumber": "2470315", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Generic and Export DataSources", "RefUrl": "/notes/2470315"}, {"RefNumber": "2421930", "RefComponent": "BW-B4H-CNV", "RefTitle": "Simplification List for SAP BW/4HANA", "RefUrl": "/notes/2421930"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2473145", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - SAP and BW Source Systems", "RefUrl": "/notes/2473145 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}