{"Request": {"Number": "2429588", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 453, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018755062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002429588?language=E&token=B88C50A61BA0EC06ABAAE02CE436C2DE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002429588", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002429588/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2429588"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.03.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2429588 - SAPBWNews SAP BW/4HANA 1.0 SP 04"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package&#160;04 for SAP BW/4HANA 1.0 SP 04</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for SAP BW/4HANA 1.0 SP 04, Patches</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package&#160;04 of SAP BW/4 HANA 1.0 SP 04.<br />It provides a list of all notes describing the corrections or enhancements in Support Package 04. This note will be updated when other notes are added.<br /><br /><strong>Factors you must take into account when you import the Support Package:</strong></p>\r\n<ul>\r\n<li>SAP strongly recommends to apply the latest available SAP HANA revision.</li>\r\n<li>\r\n<p><strong>Before</strong> you import the Note please read note <a target=\"_blank\" href=\"/notes/2248091\">2248091 - Change to reimplementation handling </a></p>\r\n</li>\r\n<li>\r\n<p>Please&#160;investigate note 2455856 - Nearline Storage (NLS) f&#252;r das Advanced DataStore Objekt (corporate memory)</p>\r\n</li>\r\n<li>\r\n<p>Support for some application server platforms will be discontinued with the next release of SAP BW/4HANA. See SAP Note <a target=\"_blank\" href=\"/notes/2620910\">2620910</a> for recommended application server platforms.</p>\r\n</li>\r\n</ul>\r\n<p><strong>Issues that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<li>Please check out the following SAP Notes which describe and fix delivered errors:</li>\r\n<ul>\r\n<li>SAP Note 2491221 - ABAP routines (expert routines) are not handled correctly</li>\r\n<li>SAP Note 2467802&#160;- adso: Error for cube-like adso with more than 120 key elements</li>\r\n<li>SAP Note 2492137 - SAP HANA processing: BW/4 HANA SP04 - SP05: SAP HANA analysis processes and SAP HANA transformations (II)</li>\r\n<li>SAP Note 2446575&#160;- Error in transformation</li>\r\n<li>SAP Note 2488772 - BW/41.0 SP6: Transport of Individual routines/formula doesn't work</li>\r\n<li>SAP Note 2484713 - Error during transport - changes in routines are not transferred</li>\r\n<li>SAP Note 2480967 - Remodeling/dump during import</li>\r\n<li>SAP Note 2486681 - Consistency check of executing user in transaction RS2HANA_CHECK</li>\r\n</ul>\r\n<li>The generic delta of a DTP may return duplicate or incorrect data after you have implemented this SP or SAP Note 2420568. This issue is solved with SP 6 or SAP Note 2500221</li>\r\n<li>After implementing BW/4HANA 1.0 SP4, there can occur various errors with ABAP routines and with SAP HANA script routines. Also errors in the SAP HANA processing of the transformation are possible.<br />Right after the implementation of the BW4/HANA SP4 implement the following notes: 2491221 and 2492137. For details see Hotnews 2501899</li>\r\n<li>For further information on fixes please see the referenced notes</li>\r\n</ul>\r\n<p><strong>Errors corrected /Important Enhancements delivered with this Support Package:</strong></p>\r\n<ul>\r\n<li>\r\n<p><span style=\"font-family: Calibri; color: #1f497d;\">&#160;</span>For full list please see <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/supportpackage/\">https://launchpad.support.sap.com/#/supportpackage/</a><a target=\"_blank\" href=\"https://service.sap.com/sap/bc/bsp/spn/spat/index.htm?sp1=SAPK-10001INDW4CORE\">SAPK-10004INDW4CORE</a></p>\r\n</li>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031867)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002429588/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_BW4HANA_100_SP_04_Release_Notes_v2.xlsx", "FileSize": "29", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800000735802017&iv_version=0008&iv_guid=6CAE8B3EA6731ED79FBF4FA3799160CC"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2620910", "RefComponent": "BC-OP-PLNX", "RefTitle": "SAP S/4HANA 1511, 1610, 1709, 1809 and SAP BW/4HANA 1.0, 2.0: Recommended and released Application Server Platforms", "RefUrl": "/notes/2620910"}, {"RefNumber": "2347382", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "RefUrl": "/notes/2347382"}, {"RefNumber": "2248091", "RefComponent": "BC-UPG-NA", "RefTitle": "Change to reimplementation handling", "RefUrl": "/notes/2248091"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2347382", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "RefUrl": "/notes/2347382 "}, {"RefNumber": "2493164", "RefComponent": "BW-BEX-OT", "RefTitle": "RSZTABLES: Deletion queries from RSRREPDIR causes an exception 'CX_RSR_X_MESSAGE'", "RefUrl": "/notes/2493164 "}, {"RefNumber": "2448802", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No SIDget on cs-part if cumul. char. with constant selection", "RefUrl": "/notes/2448802 "}, {"RefNumber": "2439835", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Rework of Query Properties and Query Property Mass Maintenance", "RefUrl": "/notes/2439835 "}, {"RefNumber": "2436803", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Support: New Debug Script for Data Manager OLAP interface", "RefUrl": "/notes/2436803 "}, {"RefNumber": "2409401", "RefComponent": "BW", "RefTitle": "BICS Object service enablement", "RefUrl": "/notes/2409401 "}, {"RefNumber": "2459417", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: Editing of formulas - dump INHERITED_ERROR", "RefUrl": "/notes/2459417 "}, {"RefNumber": "2458110", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception Aggregation in BWA/HANA and NO1/NOP key figure", "RefUrl": "/notes/2458110 "}, {"RefNumber": "2457860", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "non-cumulative query returns 'no applicable data'", "RefUrl": "/notes/2457860 "}, {"RefNumber": "2457761", "RefComponent": "BW4-DM-DTO", "RefTitle": "Rebuilding Hive Table Definitions for Hadoop NLS independently from the connected BW system", "RefUrl": "/notes/2457761 "}, {"RefNumber": "2457618", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "Advanced DSO: Process chain for deleting requests sets unnecessary locks", "RefUrl": "/notes/2457618 "}, {"RefNumber": "2457382", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception Aggregation in BWA/HANA: MATLQ and AVG/AV0", "RefUrl": "/notes/2457382 "}, {"RefNumber": "2457415", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Corrections", "RefUrl": "/notes/2457415 "}, {"RefNumber": "2453773", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Forbidding XXL InfoObjects in planning", "RefUrl": "/notes/2453773 "}, {"RefNumber": "2456761", "RefComponent": "BW-MT-ADSO", "RefTitle": "BWMT 7.5x - ADSO Planning Mode is not available in 7.50", "RefUrl": "/notes/2456761 "}, {"RefNumber": "2456689", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "System dump CONVT_NO_NUMBER when loading a query", "RefUrl": "/notes/2456689 "}, {"RefNumber": "2456669", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Conversion is applied to key figures without currency/unit", "RefUrl": "/notes/2456669 "}, {"RefNumber": "2455861", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Error BRAIN_DEV 079 for hierarchy with intervals", "RefUrl": "/notes/2455861 "}, {"RefNumber": "2455714", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: incorrect data if optional variable without default value is omitted", "RefUrl": "/notes/2455714 "}, {"RefNumber": "2455549", "RefComponent": "BW-BEX-OT", "RefTitle": "Internal functionality for creating SAP HANA Calcscenario", "RefUrl": "/notes/2455549 "}, {"RefNumber": "2455623", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: error with hierarchy filter and unit conversion", "RefUrl": "/notes/2455623 "}, {"RefNumber": "2455334", "RefComponent": "BW-PLA-IP", "RefTitle": "Missing transformation for characteristic keys", "RefUrl": "/notes/2455334 "}, {"RefNumber": "2455295", "RefComponent": "BW-BEX-ET-OSD", "RefTitle": "Open Dialogs: Obsolet authorization check", "RefUrl": "/notes/2455295 "}, {"RefNumber": "2453498", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDXTEST: Functions are no longer displayed", "RefUrl": "/notes/2453498 "}, {"RefNumber": "2453464", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Prevention of saving of inconsistent models", "RefUrl": "/notes/2453464 "}, {"RefNumber": "2453122", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Terminations due to main memory bottlenecks when complex calculated members are used", "RefUrl": "/notes/2453122 "}, {"RefNumber": "2452708", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for usage of ParallelPeriod function", "RefUrl": "/notes/2452708 "}, {"RefNumber": "2452799", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Currency keyfigures not displayed on hierarchy nodes", "RefUrl": "/notes/2452799 "}, {"RefNumber": "2452660", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Change allowed despite planning mode", "RefUrl": "/notes/2452660 "}, {"RefNumber": "2452112", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Check for valid values for FIX-CUKY and FIX-UNIT", "RefUrl": "/notes/2452112 "}, {"RefNumber": "2447489", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA_SYB_PARTITION_MONITOR ends with error in process chain in case of success", "RefUrl": "/notes/2447489 "}, {"RefNumber": "2451619", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA GetServerInfo Cache-Behaviour", "RefUrl": "/notes/2451619 "}, {"RefNumber": "2451457", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formulas calculated in SAP HANA: constant selection and no SIDs", "RefUrl": "/notes/2451457 "}, {"RefNumber": "2451378", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: Planning function is not executed on SAP HANA DB", "RefUrl": "/notes/2451378 "}, {"RefNumber": "2451477", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Text Symbols for the Language dependent texts in the Local Formula Editor.", "RefUrl": "/notes/2451477 "}, {"RefNumber": "2451123", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Termination during remodeling", "RefUrl": "/notes/2451123 "}, {"RefNumber": "2450859", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Invalid value for 'Master Data Check'", "RefUrl": "/notes/2450859 "}, {"RefNumber": "2450457", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception INIT_XS-01- in CL_RSR_CHABIT", "RefUrl": "/notes/2450457 "}, {"RefNumber": "2449716", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: New line planning features enhancement - ABAP", "RefUrl": "/notes/2449716 "}, {"RefNumber": "2449602", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BW runtime problems during authorization check for documents", "RefUrl": "/notes/2449602 "}, {"RefNumber": "2449048", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "NCUM pushdown: \"Failed to read from column '...' at index '...'", "RefUrl": "/notes/2449048 "}, {"RefNumber": "2431828", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Enhancement of function StrToMember", "RefUrl": "/notes/2431828 "}, {"RefNumber": "2448281", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZTREE: expanded nodes are collapsed after editing of the tree", "RefUrl": "/notes/2448281 "}, {"RefNumber": "2447727", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "SAP HANA view: Problems with range restrictions", "RefUrl": "/notes/2447727 "}, {"RefNumber": "2447753", "RefComponent": "BW4-DM-OUT-OHD", "RefTitle": "Dump when loading data into an open hub destination", "RefUrl": "/notes/2447753 "}, {"RefNumber": "2447691", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Hierarchies displayed unsorted in selector", "RefUrl": "/notes/2447691 "}, {"RefNumber": "2446572", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query Generation: Formula causes Arithmetic Exception", "RefUrl": "/notes/2446572 "}, {"RefNumber": "2447141", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for use of functions NextMember and PrevMember", "RefUrl": "/notes/2447141 "}, {"RefNumber": "2446399", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA: Conditons with two or more thresholds", "RefUrl": "/notes/2446399 "}, {"RefNumber": "2446257", "RefComponent": "BW", "RefTitle": "BACKUP: Korrekturen zu SAP BW / SAP BW/4HANA (ABAP)", "RefUrl": "/notes/2446257 "}, {"RefNumber": "2446218", "RefComponent": "BW", "RefTitle": "BACKUP: Corrections for SAP BW / SAP BW/4HANA (ABAP)", "RefUrl": "/notes/2446218 "}, {"RefNumber": "2446248", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Administrative changes in Analysis Office Server Runtime", "RefUrl": "/notes/2446248 "}, {"RefNumber": "2445519", "RefComponent": "BW-BEX-ET", "RefTitle": "Metadata Repository: usage of query elements (ELEM) in external objects is not shown", "RefUrl": "/notes/2445519 "}, {"RefNumber": "2445390", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Really long runtime for execution of input help for characteristic 0CALDAY, 0CALWEEK, or 0CALYEAR", "RefUrl": "/notes/2445390 "}, {"RefNumber": "2444866", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Changes on variable referenced to multiple characteristics doesn't get reflected", "RefUrl": "/notes/2444866 "}, {"RefNumber": "2442860", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Problems with external characteristics in hierarchies and display attributes", "RefUrl": "/notes/2442860 "}, {"RefNumber": "2203685", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Report RSZ_ANALYZE_TABLES", "RefUrl": "/notes/2203685 "}, {"RefNumber": "2443906", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: External aggregation levels", "RefUrl": "/notes/2443906 "}, {"RefNumber": "2426576", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in Program CL_RSR_RRI2_FAGGR and Form _CHANGE_AGGRQ-03-", "RefUrl": "/notes/2426576 "}, {"RefNumber": "2443311", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Repair inconsistent node table entries for remote hierarchies", "RefUrl": "/notes/2443311 "}, {"RefNumber": "2443364", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "HANA View for Query not generated after transport", "RefUrl": "/notes/2443364 "}, {"RefNumber": "2442737", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Long runtime in the method _VALUE_READ of class CL_RSMD_RS_READ_MDATA", "RefUrl": "/notes/2442737 "}, {"RefNumber": "2442925", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query aborts with shortdump BIT_OFFSET_NOT_POSITIVE", "RefUrl": "/notes/2442925 "}, {"RefNumber": "2442583", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Long runtime in the function module RRSI_VAL_SID_SINGLE_CONVERT during determination of currencies", "RefUrl": "/notes/2442583 "}, {"RefNumber": "2442222", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Long runtime in method __GET_AMOUNT of class CL_RSMD_RS_READ_MDATA", "RefUrl": "/notes/2442222 "}, {"RefNumber": "2442037", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Adjustments for transfer to API", "RefUrl": "/notes/2442037 "}, {"RefNumber": "2441930", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "BICS and BEx: Avoid inconsistent behavior of zero suppression and universal display hierarchy (BW ABAP)", "RefUrl": "/notes/2441930 "}, {"RefNumber": "2439475", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA: Ensure stable sort order in Fixed Filter", "RefUrl": "/notes/2439475 "}, {"RefNumber": "2438783", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Wrong data for query on aggregation level", "RefUrl": "/notes/2438783 "}, {"RefNumber": "2440400", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSECAUTH: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED during search using function key (F4)", "RefUrl": "/notes/2440400 "}, {"RefNumber": "2439922", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Corrections", "RefUrl": "/notes/2439922 "}, {"RefNumber": "2439725", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Extension of authorization log with BPC authorizations", "RefUrl": "/notes/2439725 "}, {"RefNumber": "2439823", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime/memory problems if using the LastPeriods function", "RefUrl": "/notes/2439823 "}, {"RefNumber": "2439560", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Current Member variable doesn't work correctly with NCUM key figure", "RefUrl": "/notes/2439560 "}, {"RefNumber": "2438521", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning function for delta execution in PAK: Error message RSPLF 016 - Value of characteristic &1: &2 is not included in the selection", "RefUrl": "/notes/2438521 "}, {"RefNumber": "2438022", "RefComponent": "BW4-ME-TRFN", "RefTitle": "Non-cumulative ODS cannot be loaded with SAP HANA execution", "RefUrl": "/notes/2438022 "}, {"RefNumber": "2437854", "RefComponent": "BW4-DM-DTO", "RefTitle": "SQL error -131 during activation of a Data Arching Process", "RefUrl": "/notes/2437854 "}, {"RefNumber": "2437739", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Manage-API: Löschen aller Daten und Löschen eines Datenziels", "RefUrl": "/notes/2437739 "}, {"RefNumber": "2437823", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis URL launcher ICF service does not offer variables", "RefUrl": "/notes/2437823 "}, {"RefNumber": "2435187", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Characteristic 0CALWEEK1: Cannot adjust first day of week", "RefUrl": "/notes/2435187 "}, {"RefNumber": "2436828", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Mistaken variable dependency causes error FILL-DEP-1-", "RefUrl": "/notes/2436828 "}, {"RefNumber": "2436433", "RefComponent": "BW4-ME-TRFN", "RefTitle": "DTP execution does not allow mixed scenarios", "RefUrl": "/notes/2436433 "}, {"RefNumber": "2436204", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "THJ + NCUM combination doesn't deliver the correct result", "RefUrl": "/notes/2436204 "}, {"RefNumber": "2435758", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Back-end implementation of SAVE-AS", "RefUrl": "/notes/2435758 "}, {"RefNumber": "2431484", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong authorization error for temporal hierarchy join", "RefUrl": "/notes/2431484 "}, {"RefNumber": "2435539", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "The value help for unit convertions shows duplicate entries", "RefUrl": "/notes/2435539 "}, {"RefNumber": "2435394", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy activation aborts with CX_SY_ARITHMETIC_OVERFLOW", "RefUrl": "/notes/2435394 "}, {"RefNumber": "2434888", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "ABAP runtime error ITAB_DUPLICATE_KEY when you call the input help", "RefUrl": "/notes/2434888 "}, {"RefNumber": "2434679", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "MultiProvider query with restriction for 0INFOPROV: Unexpected data, long runtime", "RefUrl": "/notes/2434679 "}, {"RefNumber": "2433676", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "SAP HANA view and 'Master Data' as the 'Access Type for Result Values': Unexpected data", "RefUrl": "/notes/2433676 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DW4CORE", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "DW4CORE 100", "SupportPackage": "SAPK-10004INDW4CORE", "URL": "/supportpackage/SAPK-10004INDW4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}