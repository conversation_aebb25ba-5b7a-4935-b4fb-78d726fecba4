{"Request": {"Number": "2495630", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 219, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019140602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002495630?language=E&token=E55D64421BD37390E331F6A1EF5943C3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002495630", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002495630/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2495630"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.06.2022"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLP-FE-COR"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Fiori Launchpad Frontend Core and Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Fiori Launchpad", "value": "CA-FLP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Fiori Launchpad Frontend", "value": "CA-FLP-FE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLP-FE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Fiori Launchpad Frontend Core and Services", "value": "CA-FLP-FE-COR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLP-FE-COR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2495630 - Reference lost displayed in Fiori Launchpad Designer, in Launchpad Content Manager or in Manage Launchpad Pages"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Tile or target mapping with title \"Reference lost\" in Fiori Launchpad Designer,&#8239;Fiori Launchpad Content Manager, Manage Launchpad Pages, content check report /UI2/FLC or similar tool</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Fiori Launchpad Designer, Fiori Catalogs, Reference Lost, Backend Catalog, Catalog Replication, Fiori Tile, Fiori Target Mapping, Fiori Launchpad Content Manager</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<div class=\"OutlineElement Ltr SCXW25021187 BCX8\">\r\n<p class=\"Paragraph SCXW25021187 BCX8\">To enable reuse, original tiles and target mappings are defined once, in a technical catalog, and many business catalogs can contain references to these originals.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW25021187 BCX8\">\r\n<p class=\"Paragraph SCXW25021187 BCX8\">&#8220;Reference lost&#8221; means that a reference exists but its original tile or target mapping is not present in the current system/client. The system can only provide the ID of the original tile or target mapping and its original catalog ID. It&#8217;s not possible to resolve information like title, texts or semantic object/action because this information doesn't exist in the system/client.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\"><strong>1. Analyzing a specific &#8216;Reference lost&#8217; issue</strong></p>\r\n<p>Run report <em>/UI2/REFERENCE_LOST</em> and follow its explicit troubleshooting tips.<span style=\"font-size: 10px;\">&#160; &#160;</span></p>\r\n<p>How to determine the required input &#8216;<em>Instance ID</em>&#8217;:</p>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\">\r\n<ul class=\"BulletListStyle1 SCXW78061981 BCX8\">\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\" lang=\"EN-US\">In <em>Launchpad Designer</em> and <em>Manage Launchpad Pages</em>, the title of the broken tile/target mapping contains the instance ID.</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\" lang=\"EN-US\">In <em>Launchpad Content Manager</em>, you can find&#160;the instance ID in columns &#8216;<em>Original Tile ID</em>&#8217; or &#8216;<em>Original Target Mapping ID</em>&#8217;.</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">See Note 2269272 in case an SAP-delivered catalog is affected.</p>\r\n</li>\r\n</ul>\r\n<p><strong>2. Deeper Analysis of Reference Lost Issues</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\" lang=\"EN-US\">This section provides information on</p>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\">\r\n<ul class=\"BulletListStyle1 SCXW78061981 BCX8\">\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">getting an overview of all lost references</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">determining the original catalog type</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">fixing replicated catalogs: Replicate the corresponding backend catalogs</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">fixing standard catalogs</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">deleting the broken reference and creating a new original and new references</p>\r\n</li>\r\n</ul>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\"></div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\"><strong>2.1 Getting an Overview of all Lost References</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">You can use&#160;<em>SAP Fiori Launchpad Checks</em> (transaction /UI2/FLC) to get an overview of lost references, see SAP Help topic&#160;<a target=\"_blank\" class=\"Hyperlink SCXW78061981 BCX8\" href=\"https://help.sap.com/saphelp_uiaddon10/helpdata/en/12/43902e4e3f4ac8a9bd07bf8dbc78c3/content.htm?no_cache=true\" rel=\"noreferrer noopener\">Checking Launchpad Content</a>.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Alternatively, you can analyze lost references directly in <em>SAP&#160;Fiori&#160;</em><em>Launchpad Content Manager:</em></p>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\">\r\n<ul class=\"BulletListStyle1 SCXW78061981 BCX8\">\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Access <em>Launchpad Content Manager</em> via transaction /UI2/FLPCM_CUST(client-dependent) or /UI2/FLPCM_CONF (cross-client).</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Switch to tab &#8216;<em>Tiles/Target Mappings</em>&#8217; and search for the term &#8216;REFERENCE LOST&#8217;.</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Result: You will get a list of all affected tile/target mapping combinations</p>\r\n</li>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\"><strong>Warnings</strong> with <em>{Reference Lost to Backend Catalog}</em>&#160;in column <em>Title/Subtitle/Information</em>:&#160;</span></li>\r\n<ul>\r\n<li>These can usually be resolved by replicating the backend catalogs, see section &#8216;Fixing Replicated Catalogs&#8217;<em>.</em></li>\r\n<li>See Note 2269272 in case an SAP-delivered catalog is affected.</li>\r\n</ul>\r\n<li><strong>Errors</strong> with <em>{Reference Lost}</em> in column <em>Title/Subtitle/Information</em>:</li>\r\n<ul>\r\n<li>see section &#8216;Fixing Standard Catalogs&#8217;</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul class=\"BulletListStyle1 SCXW78061981 BCX8\">\r\n<li>Tip: For a quicker analysis of lost references and their originals in <em>Launchpad Content Manager</em>, use the button '<em>Change Layout'</em> to add the following columns to the table of tile/target mapping combinations:</li>\r\n</ul>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\" style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">Tile Title (contains&#160;<em>{Reference Lost}</em> if the tile is affected)</span></div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\" style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">Original Tile ID</span><br /><span style=\"font-size: 14px;\">Original Tile Catalog ID</span></div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\" style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">Original Tile Catalog Type</span></div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\" style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">TM Title&#160;(contains {Reference Lost} if the target mapping is affected)</span></div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\" style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">Original Target Mapping ID</span><br /><span style=\"font-size: 14px;\">Original Target Mapping Catalog ID</span></div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\" style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">Original Target Mapping Catalog Type</span></div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\"></div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\"><strong>2.2 Determining the Catalog Type</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">In <em>Launchpad Content Manager</em>, you can determine the catalog type of an original tile or target mapping from columns &#8216;<em>Original Tile Catalog Type&#8217;</em> and &#8216;<em>Original Target Mapping:</em></p>\r\n</div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\">\r\n<ul class=\"BulletListStyle1 SCXW78061981 BCX8\">\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">CAR stands for replicated catalogs</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">CAT stands for standard Fiori catalogs</p>\r\n</li>\r\n</ul>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">In <em>Launchpad Designer</em> and in <em>Manage Launchpad Pages</em>, items with lost references have a title like &#8216;<em>Reference Lost: Page &lt;catalogID&gt;&#160; Instance ID &lt;instanceID&gt;</em>&#8217;.</p>\r\n</div>\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\">\r\n<ul class=\"BulletListStyle1 SCXW78061981 BCX8\">\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">the key after &#8216;Page&#8217; indicates the original catalog ID, i.e. the catalog the broken reference points to.</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">&#160;Determining the catalog type by catalog ID:</li>\r\n<ul>\r\n<li>Page X-SAP-UI2-ADCAT:CATXX_TC_XXX_BE_XXX:YYYY refers to a backend catalog which has to be replicated e.g. via transaction /UI2/APPDESC_GET_ALL. SAP-delivered backend technical catalogs typically follow the pattern &#8230;_TC_..._BE_...</li>\r\n<li>Page X-SAP-UI2-CATALOGPAGE:ZABC_CATALOG refers to a standard Fiori catalog that does not require any replication.</li>\r\n</ul>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">the &#8216;InstanceID&#8217; is the ID of the original tile or target mapping</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Example: Reference lost: Note 2495630 Page X-SAP-UI2-CATALOGPAGE:ZRG_TC_TEST_REFERENCE_LOST, Instance ID 42010AEECB101EDCB1BC4F5032AF02B0</p>\r\n</li>\r\n</ul>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\"></div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\"><strong>2.3 Fixing Replicated Catalogs: Replicate the Corresponding Backend Catalogs.</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">See Note 2269272 in case an SAP-delivered catalog is affected.</p>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">For custom content:</p>\r\n</div>\r\n<div>\r\n<ul>\r\n<li>\r\n<p>Check that your backend catalog has been replicated to this system - search for the catalog ID in <em>Launchpad Content Manager</em> or <em>Launchpad Designer</em>.&#160;<br />Replicate the catalog if it doesn&#8217;t exist.</p>\r\n</li>\r\n<li>\r\n<p>If the catalog is present but is missing some tiles or target mappings, replicate the catalog again.</p>\r\n</li>\r\n<li>\r\n<p>For information on replication, see SAP Help&#160;<a target=\"_blank\" href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/a7b390faab1140c087b8926571e942b7/61d5111797c04591bf324bb16a6a2e19.html\" rel=\"noreferrer noopener\">Replicating Back-End Catalogs.</a></p>\r\n</li>\r\n<li>\r\n<p>If the problem persists after replication, check the existence of catalog and tile/target mapping in the backend:</p>\r\n</li>\r\n<li>\r\n<p>Search for the Instance ID in table SUI_TM_MM_APP in the backend and check whether the data the is still present or keys have been altered (e.g.object has been deleted or assigned to a different catalog/alias etc.). If the Instance ID does not exist in the backend either, the broken reference needs to be deleted and recreated as described in section 2.5.</p>\r\n</li>\r\n</ul>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\"><strong>2.4 Fixing Standard Catalogs</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">As the broken reference only gives technical information, try to find a non-broken version of the same catalog, if possible.</p>\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Determine the ID of the missing original, either directly from the error message or from the following columns in&#160;<em>Launchpad Content Manager</em>&#8217;s list of tile/target mapping combinations:</p>\r\n<ul>\r\n<ul>\r\n<li>Original Tile ID&#160;</li>\r\n<li>Original Tile Catalog ID</li>\r\n<li>Original Target Mapping ID</li>\r\n<li>Original Target Mapping Catalog ID</li>\r\n</ul>\r\n</ul>\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Analyzing a non-broken client or system:</p>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"ListContainerWrapper SCXW78061981 BCX8\">\r\n<blockquote style=\"font-size: 14px; margin: 0px 0px 0px 40px; border: none; padding-top: 0px; padding-right: 0px; padding-bottom: 0px;\">\r\n<ul>\r\n<li>Sometimes items are missing only in a certain client. So if you encountered the problem in a client-specific tool, check out the original catalog in the cross-client configuration layer, e.g. with cross-client<em> Launchpad Content Manager (</em>transaction /n/UI2/FLPCM_CONF).</li>\r\n<ul>\r\n<li>If the item exists in the cross-client catalog and is missing only in a certain client, evaluate if the cross-client catalog meets all your requirements and can be used instead.</li>\r\n<li>Before deleting a client-specific version of a business catalog, take a snapshot with <em>Launchpad Content Aggregator</em> (transaction /UI2/FLPCA), in case you need to recreate it later. For more information on the <em>Launchpad Content Aggregator</em>, see&#160;<a target=\"_blank\" href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/a7b390faab1140c087b8926571e942b7/e5edf22831f44b94bdc09f7f29135525.html?q=analyzing%20launchpad%20content\"><em>Analyzing Launchpad Content</em></a><em>.</em>&#160; &#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>If all clients are affected, try a system upstream or downstream the transport chain not yet affected by the change. Be careful to check both the client-dependent CUST and the cross-client CONF layer.</li>\r\n<ul>\r\n<li>Search for this ID in <em>Launchpad Designer</em> or in<em>&#160;SAP Fiori Launchpad Application Manager</em> (transaction /UI2/FLPAM) and evaluate:</li>\r\n<li>If the original still exists in another system, re-transporting the catalog into the affected system will make the content available again</li>\r\n<li>If you judge that a lost reference shall be deleted, simply remove the broken reference.</li>\r\n<li>If you judge that a lost reference should point to a different tile/target mapping, delete the broken reference and create a new reference to the intended tile/target mapping.</li>\r\n<li>If none of the above applies, the broken reference needs to be deleted and recreated as described in section 2.5.</li>\r\n</ul>\r\n</ul>\r\n</blockquote>\r\n</div>\r\n</div>\r\n<div class=\"SCXW78061981 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\"></div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\"><strong>2.5. Deleting the Broken Reference and Creating a New Original and New References</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">If a catalog cannot be replicated or re-transported, the only way forward is to remove the broken reference and create a new original tile/target mapping.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Please note that in this case the&#160;<strong>ID of the new original will be different from the old one, so it's necessary to delete all references to the old original and create references to the new original.&#160;</strong></p>\r\n</div>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">Important: Before deleting any content, find out where it is used so that you can recreate the references later.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW78061981 BCX8\">\r\n<p class=\"Paragraph SCXW78061981 BCX8\">From Release 2021 FP01 onwards, <em>Launchpad Content Manager</em> offers where-used checks for tiles, see&#160;<a target=\"_blank\" class=\"Hyperlink SCXW78061981 BCX8\" href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/a7b390faab1140c087b8926571e942b7/c88c15e4159d4d8d8b131a220acd1f10.html\" rel=\"noreferrer noopener\">SAP Help</a>.</p>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-FLP-ABA (SAP Fiori Launchpad ABAP Services)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023688)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D025909)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495630/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2500483", "RefComponent": "CA-UI2-INT-BE", "RefTitle": "Supportability Enhancements FLP", "RefUrl": "/notes/2500483"}, {"RefNumber": "2269272", "RefComponent": "CA-UI2-INT-BE", "RefTitle": "\"Reference Lost\" Error for FLP Tiles after applying new UI component version", "RefUrl": "/notes/2269272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3037468", "RefComponent": "CA-WUI-UI", "RefTitle": "Fiori Launchpad Designer - Reference Lost on Web Client UI Fiori apps (WEBCUIF), tiles and catalogs", "RefUrl": "/notes/3037468 "}, {"RefNumber": "2677058", "RefComponent": "CA-FE-FLP-EU", "RefTitle": "Apps are not appearing on Fiori Launchpad", "RefUrl": "/notes/2677058 "}, {"RefNumber": "2616973", "RefComponent": "CA-FE-FLP-DT", "RefTitle": "Fiori Reference of Business Catalogs versus Technical Catalogs", "RefUrl": "/notes/2616973 "}, {"RefNumber": "3375711", "RefComponent": "EHS-SUS-HS", "RefTitle": "Not able to create or navigate from an EHS application to another", "RefUrl": "/notes/3375711 "}, {"RefNumber": "3226560", "RefComponent": "CA-FLP-ABA", "RefTitle": "SAP Fiori Launchpad: Preventing and Mitigating Lost References after an Upgrade", "RefUrl": "/notes/3226560 "}, {"RefNumber": "2269272", "RefComponent": "CA-UI2-INT-BE", "RefTitle": "\"Reference Lost\" Error for FLP Tiles after applying new UI component version", "RefUrl": "/notes/2269272 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}