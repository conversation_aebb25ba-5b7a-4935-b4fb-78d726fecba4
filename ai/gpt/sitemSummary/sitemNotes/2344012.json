{"Request": {"Number": "2344012", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 210, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018368342017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002344012?language=E&token=B5288088745D2E0BEB0209064E5F0C24"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002344012", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002344012/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2344012"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.11.2021"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2344012 - Currencies in Universal Journal"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing new installation of SAP S/4HANA or a system conversion to SAP S/4HANA. All currency&#160;configurations can be&#160;migrated&#160;to the universal journal of S/4HANA.</p>\r\n<p>This note provides information on the new configuration&#160;capabilities for parallel currencies in FI and CO.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>This note is relevant, if you are use FI&#160;&#160;/ CO in S/4HANA.</strong></p>\r\n<p>&#160;</p>\r\n<p><strong>Situation in ECC:&#160;</strong></p>\r\n<p>In the Business Suite (ECC) there used to be up to 3 parallel currencies in FI (table T001A / tx OB22) and 2 parallel currencies in CO (TKA01 / tx OKKP): CO area currency and object currency.</p>\r\n<p>The currencies of non leading ledgers in NewGL (T882G)&#160;were&#160;a subset of the currencies in the leading ledger (T001A).</p>\r\n<p>One of the CO currencies needed to be the local currency (CT 10), but it was not necessary that the other&#160;currency in CO was also&#160;configured in FI.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Situation in S/4H:</strong></p>\r\n<p>With the universal journal and the&#160;common line item table ACDOCA for FI and CO, there is also a central currency configuration for the universal journal. As the currency configuration depends on the universal journal ledgers, there is a combined&#160;view cluster&#160;for ledgers and currencies, tx FINSC_LEDGER.</p>\r\n<p>IMG menu path: Financial Accounting( New ) - Financial Accounting Global Settings (New) - Ledgers - Ledger - Define Settings for Ledgers and Currency Types.</p>\r\n<p>&#160;</p>\r\n<p><strong>Overview on&#160;amounts fields of the Universal Journal </strong>(as of S/4H 1610 and later&#160;releases&#160;and S/4H Finance 1605)</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>ACDOCA Fieldname</strong></p>\r\n</td>\r\n<td><strong>Description</strong></td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>WSL</td>\r\n<td>\r\n<div class=\"O2\">Amount in Transaction Currency&#160; (Document Currency)</div>\r\n</td>\r\n<td>Original currency of the transaction, not contained in balances, as it might happen that different currencies cannot be aggregated; corresponds to BSEG-WRBTR.</td>\r\n</tr>\r\n<tr>\r\n<td>TSL</td>\r\n<td>\r\n<div class=\"O2\">Amount in Balance Transaction Currency</div>\r\n</td>\r\n<td>Amount&#160;converted to the currency of the G/L account, in order to allow aggregation on G/L account level; corresponds to BSEG-PSWBT.</td>\r\n</tr>\r\n<tr>\r\n<td>HSL</td>\r\n<td>Amount in Company Code&#160;Currency (Local Currency)</td>\r\n<td>Currency of the company code; corresponds to BSEG-DMBETR in FI and object currency in CO (except co area currency type is local currency)</td>\r\n</tr>\r\n<tr>\r\n<td>KSL</td>\r\n<td>Amount in Global Currency</td>\r\n<td>Currency type configured in the controlling area (TKA01-CTYP)</td>\r\n</tr>\r\n<tr>\r\n<td>OSL</td>\r\n<td>Amount in Freely Defined Currency 1</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>VSL</td>\r\n<td>Amount in Freely Defined Currency 2</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>BSL</td>\r\n<td>Amount in Freely Defined Currency 3</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>CSL</td>\r\n<td>Amount in Freely Defined Currency 4</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>DSL</td>\r\n<td>Amount in Freely Defined Currency 5</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>ESL</td>\r\n<td>Amount in Freely Defined Currency 6</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>FSL</td>\r\n<td>Amount in Freely Defined Currency 7</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>GSL</td>\r\n<td>Amount in Freely Defined Currency 8</td>\r\n<td>Currency&#160;configured per ledger and company code in tx finsc_ledger</td>\r\n</tr>\r\n<tr>\r\n<td>CO_OSL</td>\r\n<td>Amount in Object currency of CO</td>\r\n<td>Object currency of CO, only if it is really the object currency (70)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>WSL, TSL, HSL and KSL mandatory currencies in all ledgers.</p>\r\n<p>KSL is filled, if the company code is assigned to a controlling area. The currency type for KSL&#160;is defined by the setting in the controlling area.</p>\r\n<p>The second and third parallel currencies of FI (BSEG-DMBE2 or BSEG-DMBE3) correspond to 2 amount&#160;fields of&#160;KSL&#160; - GSL according to&#160;configuration in tx FINSC_LEDGER.</p>\r\n<p>Table BSEG is&#160;not (and will not be)&#160;extended and still contains only 3 parallel currencies;&#160;the BSEG relevant currency types&#160;can be configured in the overview screen of the company code assignment in tx FINSC_LEDGER in the columns on the right side&#160; (1st / 2nd / 3rd FI Currency). Here you can choose from the configured currency types the BSEG relevant currency types. Currency types in customer name space are not allowed as BSEG relevant currency.</p>\r\n<p>When a currency type is marked as BSEG relevant in the leading ledger (2nd or 3rd FI Currency) and this currency type is also configured in a non-leading ledger, then it needs to be a BSEG relevant in the non leading ledger as well.</p>\r\n<p><strong>Overview on supported processes for currency fields</strong></p>\r\n<p>Our goal is to implement full process integration of all currency fields in all processes in accounting. With S/4H 1610, 1709 and 1809 (or S/4H Finance 1605) we achieved the following coverage:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td><strong>HSL</strong></td>\r\n<td><strong>KSL</strong></td>\r\n<td><strong>OSL</strong></td>\r\n<td><strong>VSL</strong></td>\r\n<td><strong>BSL</strong></td>\r\n<td><strong>CSL</strong></td>\r\n<td><strong>DSL</strong></td>\r\n<td><strong>ESL</strong></td>\r\n<td><strong>FSL</strong></td>\r\n<td><strong>GSL</strong></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em><em><strong>Example configuration</strong> </em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;</em></p>\r\n</td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160; Universal Journal (ACDOCA)</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;10</em></p>\r\n</td>\r\n<td><em>&#160;30</em></td>\r\n<td><em>&#160;40</em></td>\r\n<td><em>&#160;50</em></td>\r\n<td><em>&#160;60</em></td>\r\n<td><em>&#160;Z1</em></td>\r\n<td><em>&#160;Z2</em></td>\r\n<td><em>&#160;Z3</em></td>\r\n<td><em>&#160;Z4</em></td>\r\n<td><em>&#160;Z5</em></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160; BSEG and Fixed Asset Accountning</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;10</em></p>\r\n</td>\r\n<td><em>&#160;30</em></td>\r\n<td><em>&#160;40</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160; Material Ledger</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;10</em></p>\r\n</td>\r\n<td><em>&#160;30</em></td>\r\n<td><em>&#160;40</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><em>&#160; Controlling</em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;10</em></p>\r\n</td>\r\n<td><em>&#160;30</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n<td><em>&#160;</em></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Supported Processes (for&#160;example config.)</strong></td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Realtime Currency Conversion</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Balance zero per Journal Entry</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Open Item Management (FI-AP, FI-AR, FI-GL)</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Foreign Currency Valuation</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Regrouping</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; GL Allocations (Assessment and Distribution)</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>\r\n<p>&#160;X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; CO Allocations (Assessment and Distribution)</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;4</td>\r\n<td>&#160;4</td>\r\n<td>&#160;4</td>\r\n<td>&#160;4</td>\r\n<td>&#160;4</td>\r\n<td>&#160;4</td>\r\n<td>&#160;4</td>\r\n<td>\r\n<p>&#160;4</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; CO-PA Allocations</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n<td>&#160;3</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Fixed Asset Accounting</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>\r\n<p>&#160;1</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; Material Ledger</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; CO Settlement</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n<td>&#160;2</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; CO Reposting</td>\r\n<td>&#160;X</td>\r\n<td>&#160;X</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>&#160;1</td>\r\n<td>\r\n<p>&#160;1</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>This means that <strong>all</strong> journal&#160;entry items are converted in the accounting interface for all configured currencies, regardless of the source where the business transaction originates from. In the accounting interface the journal entry is converted according to the conversion settings in tx FINSC_LEDGER (exchange rate type, ....), and balance zero per journal entry is guaranteed.</p>\r\n<p>Some processes&#160;require more that a mere document wise currency conversion with the current rate. These processes require that the amounts are converted with rates of the&#160;historical data being processed. This is not yet supported for all processes. We achieved this for open item management in FI-AP, FI-AR, and FI-GL or for CO and GL allocations (marked with X in the above table), but not yet for Fixed Asset depreciations or CO settlements.</p>\r\n<p>1: As a&#160;fallback the amounts are converted with the current exchange rate. In such a case there might remain a difference caused by rounding&#160;effects or different exchange rates for the amount fields which are marked with '1' in the above table.</p>\r\n<p>2: Since Release OnPrem 2020 CO Settlement supports all ACDOCA currencies, if this is activated in the settlement profile</p>\r\n<p>3: Since Release OnPrem 2021 CO-PA Allocation are available as part of Universal Allocation. Universal Allocation supports all currencies; the classic CO-PA Allocations still support 10 and 30 only.</p>\r\n<p>4: CO Allocations can treat all 10 amounts field precisely, if you include amount field in the cycle definition, or otherwise convert with current exchange rates. Also in cross company or in the COGM scenario, the amounts are converted with current exchange rates.</p>\r\n<p><strong>&#160;&#160;</strong></p>\r\n<p><strong>Overview on supported reporting for currency fields</strong></p>\r\n<p>With S/4H 1610 and later releases the currencies that are not stored in BSEG can be displayed in following UIs:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Description</strong></td>\r\n<td><strong>Transaction Code</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Display Document</td>\r\n<td>FB03</td>\r\n</tr>\r\n<tr>\r\n<td>Display Document in General Ledger View</td>\r\n<td>FB03L</td>\r\n</tr>\r\n<tr>\r\n<td>Display G/L Account Balances</td>\r\n<td>FAGLB03</td>\r\n</tr>\r\n<tr>\r\n<td>Display G/L Account Line Items</td>\r\n<td>FAGLL03</td>\r\n</tr>\r\n<tr>\r\n<td>Line Item Browser</td>\r\n<td>FAGLL03H</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Display G/L Account Line Items</p>\r\n</td>\r\n<td>Fiori App</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Journal Entry Analyzer</p>\r\n</td>\r\n<td>Fiori App</td>\r\n</tr>\r\n<tr>\r\n<td>Audit Journal&#160;</td>\r\n<td>Fiori App</td>\r\n</tr>\r\n<tr>\r\n<td>Display Financial Statement&#160;</td>\r\n<td>Fiori App</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In addition the new analytics with FIORI UI based on CDS Views supports all currencies of the Universal Journal. Note 2579584 contains details on the CDS based analytics.</p>\r\n<p><strong>Usage of new currencies</strong></p>\r\n<p>New Installations</p>\r\n<ul>\r\n<li>You can configure the new currencies in the universal journal.</li>\r\n<li>Please carefully consider which currencies are integrated with BSEG, FI-AA, CO, ML, FI-AA, and which is generically converted.</li>\r\n</ul>\r\n<p>Migration of Customer Installations</p>\r\n<ul>\r\n<li>Migration from ERP, SFIN 1503 or S/4H 1511</li>\r\n<li>Old currency configuration is migrated to universal journal without changes&#160;of the used currencies.</li>\r\n<li>S/4H Migration is not able to introduce new currencies.</li>\r\n<li>ERP Installations using&#160;transfer pricing make use of the new currency fields, as all&#160;currencies/valuations combinations are migrated to the same ledger.</li>\r\n<li>Usage of additional currencies is not possible without a conversion project. You must not simply configure additional currencies, are the existing data does not contain the new currencies, and some open&#160; processes would not work properly, if the currency configuration was changed.</li>\r\n<li>Since S/4Hana OP 1809 it is possible to introduce a freely defined currency,(but not a BSEG relevant currency!). You find the conversion tools in the IMG tree below Financial Accounting Global Settings - Tools - Manage Currencies.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong><strong>Currency configuration in previous S/4H releases</strong></strong></p>\r\n<p>In S/4H 1511 there were&#160;only 2 freely defined currencies (WSL, TSL, HSL, KSL, OSL, VSL, FSL) .&#160;There was no realtime conversion available for the customer defined currency types. Foreign&#160;currency valuation could be used as a substitute for the missing realtime conversion.</p>\r\n<p>In SFIN 1503 there are&#160;amounts fields&#160;(WSL, TSL, HSL, KSL, OSL, VSL) to cover the CO and FI amount fields of ECC.</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024900)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022188)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002344012/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002344012/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002344012/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2579584", "RefComponent": "CO-OM-IS", "RefTitle": "Recommendations for Usage of Reports in Financial Reporting in S/4 HANA", "RefUrl": "/notes/2579584"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3304355", "RefComponent": "FI-FIO-GL-IS", "RefTitle": "How to configure Trial Balance with document currency?", "RefUrl": "/notes/3304355 "}, {"RefNumber": "3291942", "RefComponent": "FI-FIO-AR-ANA", "RefTitle": "Unexpected amounts in Analytical Fiori Applications in Group Currency", "RefUrl": "/notes/3291942 "}, {"RefNumber": "3009909", "RefComponent": "FI-GL-GL", "RefTitle": "FINSC_LEDGER Currency type 30 is missing for global currency", "RefUrl": "/notes/3009909 "}, {"RefNumber": "2810569", "RefComponent": "FIN-MIG-ML", "RefTitle": "S/4HANA  Message FINS_ACDOC_CUST298 with transaction FINSC_LEDGER", "RefUrl": "/notes/2810569 "}, {"RefNumber": "2593455", "RefComponent": "FI-GL-IS", "RefTitle": "Display Trial Balance in freely defined currency types", "RefUrl": "/notes/2593455 "}, {"RefNumber": "2541064", "RefComponent": "FI-GL-IS", "RefTitle": "FBL3H, FBL3N cannot be found in SAP Easy Access Menu", "RefUrl": "/notes/2541064 "}, {"RefNumber": "3200089", "RefComponent": "FI-GL", "RefTitle": "Functional Currency in SAP S/4HANA", "RefUrl": "/notes/3200089 "}, {"RefNumber": "2996043", "RefComponent": "FI-GL-GL-G", "RefTitle": "FAGL_CL_REGROUP / FAGL_FCV: Field BSEG-DMBE2. does not exist in DYNPRO SAPMF05A 0330", "RefUrl": "/notes/2996043 "}, {"RefNumber": "2894297", "RefComponent": "CO-OM", "RefTitle": "Handling of Currencies in Controlling in SAP S/4HANA", "RefUrl": "/notes/2894297 "}, {"RefNumber": "2842890", "RefComponent": "FI-GL", "RefTitle": "FAQ SAP S/4HANA Finance", "RefUrl": "/notes/2842890 "}, {"RefNumber": "2756653", "RefComponent": "FI-GL-IS", "RefTitle": "Report S_ALR_87012277 is displaying wrong values with currency type 11", "RefUrl": "/notes/2756653 "}, {"RefNumber": "2577551", "RefComponent": "CO-PC-ACT", "RefTitle": "Material Ledger Production Setup in S4HANA (Green field approach)", "RefUrl": "/notes/2577551 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}