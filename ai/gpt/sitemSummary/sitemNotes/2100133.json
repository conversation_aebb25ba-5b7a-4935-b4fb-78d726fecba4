{"Request": {"Number": "2100133", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 388, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017988052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002100133?language=E&token=3AEB046D4063268723AA3D3178D32199"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002100133", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002100133/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2100133"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2016"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2100133 - SAP Simple Finance, on-premise edition 1503: Compatible Partner Products"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You plan to run 3<sup>rd</sup> party products&#160;listed on SAP price list on the same system or connected by integration with SAP Simple Finance, on-premise edition 1503.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>PQ VBR extensions addons SFIN compatibility partner</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Third party products available in SAP Service Marketplace.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p class=\"longtext\" style=\"font-size: 100.01%;\">Release of&#160;3<sup>rd</sup> party products that are listed on SAP price list.<br /><br />The following partner add-ons listed on SAP price list are already validated and found compatible with the SAP Simple Finance, on-premise edition 1503:</p>\r\n<ul>\r\n<li>SAP INVOICE MGMT BY OT 7.0 SP05</li>\r\n<li>SAP INVOICE MGMT BY OT 7.5 SP01</li>\r\n<li>SAP OCR Option to Invoice Management by OpenText 7.0 SP05</li>\r\n<li>SAP OCR Option to Invoice Management by OpenText 7.5 SP01</li>\r\n<li>SAP Archiving and Document Access 10.0 by OpenText</li>\r\n<li>SAP Archiving and Document Access 10.5 by OpenText</li>\r\n<li>SAP Paybacks and Chargeback 6.0E SP2 by Vistex</li>\r\n<li>SAP Incentive Administration 6.0E SP2 by Vistex</li>\r\n<li>SAP Data Maintenance for Pricing and Resources 6.0E SP2 by Vistex</li>\r\n</ul>\r\n<p class=\"longtext\" style=\"font-size: 100.01%;\">For certain 3<sup>rd</sup> party products not yet listed in this SAP note testing process is under way. SAP may provide on request a statement, that using a certain partner product in non-productive environment for testing purpose is uncritical or even recommended.</p>\r\n<p class=\"longtext\" style=\"font-size: 100.01%;\">General comments:</p>\r\n<div class=\"longtext\" style=\"font-size: 100.01%;\">\r\n<ul>\r\n<li>Once&#160;SAP Simple Finance, on-premise edition 1503 compatibility testing and validation&#160;is completed the above list will be updated.</li>\r\n<li>This list is only for partner add-ons which are interacting with core finance applications.</li>\r\n<li>If you are using another&#160;3<sup>rd</sup> party product&#160;not in the list, please directly contact SAP.</li>\r\n<li>For all 3<sup>rd</sup> party products not listed on SAP price list or endorsed by SAP, SAP strongly recommends only to use SAP certified&#160;3<sup>rd</sup> party products. To check if a&#160;3<sup>rd</sup> party product has a valid certification please refer to <a target=\"_blank\" href=\"http://global.sap.com/community/ebook/2013_09_adpd/enEN/search.html#categories=SAP%20Certified%20Solutions&amp;tab=certifications\">Application Development Partner Directory</a>, enter (parts of) the product or company name in the search field on top left, then click on the found companies to get further details. if&#160;the partner&#160;is not listed or&#160;you&#160;have further questions, contact <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a> or contact the&#160;3<sup>rd</sup> party supplier directly. For ABAP add-ons the 3<sup>rd</sup> party certification should especially include&#160;SAP NetWeaver&#160;7.40 on SAP HANA.</li>\r\n<li>If you are using financials-specific scenarios please contact your 3<sup>rd</sup> party supplier to check if compatibility to SAP Simple Finance is granted by the supplier. For details refer to <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-7772\">http://scn.sap.com/docs/DOC-7772</a>&#160;.</li>\r\n<li>For all 3<sup>rd</sup> party product not validated by SAP, SAP cannot give any statement on compatibility except for those statements given by certification.</li>\r\n<li>This note will be continuously updated.</li>\r\n</ul>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002100133/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2176077", "RefComponent": "XX-SER-REL", "RefTitle": "Check report for SAP S/4HANA Finance", "RefUrl": "/notes/2176077"}, {"RefNumber": "2119188", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance, on-premise edition 1503", "RefUrl": "/notes/2119188"}, {"RefNumber": "2103558", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition 1503: Compatible add-ons", "RefUrl": "/notes/2103558"}, {"RefNumber": "2012817", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA: Compatible Partner Products", "RefUrl": "/notes/2012817"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2339256", "RefComponent": "XX-PART-OPT-INV", "RefTitle": "SAP Invoice Management by OpenText support for SAP products", "RefUrl": "/notes/2339256 "}, {"RefNumber": "2119188", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance, on-premise edition 1503", "RefUrl": "/notes/2119188 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}