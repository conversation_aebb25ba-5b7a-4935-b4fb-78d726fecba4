{"Request": {"Number": "2476734", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 300, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018962802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002476734?language=E&token=57B04C6DA230C51CD063AB763C87354F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002476734", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002476734/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2476734"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.03.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-TOO-RTA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Runtime Analysis"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Tools: Editors, Painter, Modeler", "value": "BC-DWB-TOO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-TOO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Runtime Analysis", "value": "BC-DWB-TOO-RTA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-TOO-RTA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2476734 - Runtime error SYSTEM_ABAP_ACCESS_DENIED"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The execution of an ABAP application terminates with runtime error SYSTEM_ABAP_ACCESS_DENIED or with an error message that references this note.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Blocklist Monitor, SBLM, Code Deprecation</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>With the&#160;Blocklist Monitor tool&#160;execution of selected functions, methods, forms, transactions or usage of programs may be prohibited dynamically without modifying ABAP coding. The collection of these prohibited artifacts is called blocklist. It may consist of deprecated artifacts that are designated for later deletion. Two main usages of the Blocklist Monitor can be distinguished: deprecation of SAP coding and deprecation of customer coding.</p>\r\n<p>When control reaches an artifact on the blocklist,&#160;program execution termiates with a&#160;runtime error SYSTEM_ABAP_ACCESS_DENIED. In section 'Error analysis' of the runtime error, the blocklist entry that caused the runtime error is listed. Consult note&#160;2249880 when the mentioned artifact is SAP coding in SAP S/4HANA on premise. Otherwise, when&#160;you did not use the artifact by mistake, consult your system administrator to find out why the artifact is on the blocklist.</p>\r\n<p>When an artifact on the blocklist is used by user input and not by program code (e.g. when the user tries to start a transaction and has SAP kernel release &gt;= 771), an error message referencing this note may be displayed instead of the runtime error SYSTEM_ABAP_ACCESS_DENIED.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For artifacts on the blocklist in&#160;SAP coding in SAP S/4HANA on premise, proceed by note 2249880. Otherwise,&#160;avoid usage of the artifact or consult your system administrator.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-ABA-LA (Syntax, Compiler,  Runtime)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D045729)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D059054)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002476734/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002476734/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002476734/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3442091", "RefComponent": "LO-RIS", "RefTitle": "Unable to execute RIS t-codes in S/4HAN system and receive error 00977", "RefUrl": "/notes/3442091 "}, {"RefNumber": "3199079", "RefComponent": "RE-FX-CP", "RefTitle": "Transaction TXBA is not available in S/4HANA", "RefUrl": "/notes/3199079 "}, {"RefNumber": "2585772", "RefComponent": "IS-A-LMN", "RefTitle": "S/4HANA - Function module CONVERSION_EXIT_MATNV_OUTPUT usage", "RefUrl": "/notes/2585772 "}, {"RefNumber": "2755714", "RefComponent": "CA-CL", "RefTitle": "Disable Delete Class with Values menu in Manage Classes", "RefUrl": "/notes/2755714 "}, {"RefNumber": "2736702", "RefComponent": "LO-RFM-MD-ART", "RefTitle": "Runtime error (short dump) in the discontinuation when calling detail functions for bills of material articles", "RefUrl": "/notes/2736702 "}, {"RefNumber": "2661099", "RefComponent": "SCM-APO-MD-RE", "RefTitle": "Runtime error SYSTEM_ABAP_ACCESS_DENIED in resource where-used list", "RefUrl": "/notes/2661099 "}, {"RefNumber": "2249880", "RefComponent": "CA-EPT-TAS", "RefTitle": "Dump SYSTEM_ABAP_ACCESS_DENIED caused through Blocklist Monitor in SAP S/4HANA on premise", "RefUrl": "/notes/2249880 "}, {"RefNumber": "2513497", "RefComponent": "LO-ECH", "RefTitle": "Program termination when displaying the document flow", "RefUrl": "/notes/2513497 "}, {"RefNumber": "2498973", "RefComponent": "LO-PDM-GF-OBR", "RefTitle": "CC04: Displaying a characteristic", "RefUrl": "/notes/2498973 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2688803", "RefTitle": "CC05 &#x2013; program termination SYSTEM_ABAP_ACCESS_DENIED in SAP S/4HANA", "RefUrl": "/notes/0002688803"}]}}}}}