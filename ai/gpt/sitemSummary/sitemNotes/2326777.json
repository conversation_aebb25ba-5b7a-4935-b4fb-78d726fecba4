{"Request": {"Number": "2326777", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 414, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018343702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002326777?language=E&token=2E63AE7476BBD5E38757910D1A0F5594"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002326777", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002326777/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2326777"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.01.2021"}, "SAPComponentKey": {"_label": "Component", "value": "IS-ADEC-ETM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Construction Equipment Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction", "value": "IS-ADEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Construction Equipment Management", "value": "IS-ADEC-ETM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC-ETM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2326777 - S4TWL - Equipment and Tools Management (ETM)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4HANA, Simplification List, DIMP, Engineering Constructions &amp; Operations, Recipient, Project site, customer</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Reason: Conversion from SAP ERP for Discrete Industries &amp; Mill Products (ECC-DIMP) to SAP S/4HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong></p>\r\n<p>ETM functionality is available from SAP S/4HANA 1610 release with the possibility of creating ETM master data recipient as a business partner. Customers who used ETM functionality in their earlier ERP releases will have to plan ETM recipient data migration to business partner.</p>\r\n<p>A conversion to SAP S/4HANA 1511 is not supported. In&#160;SAP S/4HANA&#160;1511, ETM is not available.</p>\r\n<p>ETM is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it&#8217;s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, ETM can be found under the ID 119.&#8221;</p>\r\n<p><strong>Business Process Related Information</strong></p>\r\n<p>There is no change in ETM functionality except the changes to ETM recipient. Nevertheless the related functionality is classified in the simplification list as Non-Strategic-function</p>\r\n<p><strong><strong>Required and Recommended Action(s</strong>)</strong></p>\r\n<p>Please consider to convert the ETM recipient related customer master records to business partner records. Refer to the SAP Note&#160;2265093. Ensure that you perform&#160;the following pre-requisite steps&#160;during conversion of ECC DIMP ETM recipient to Business partner :</p>\r\n<p>1.&#160;Perform additional Customizing for ETM recipient:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160; a) &#160;Create an ETM-specific grouping and assign it to a business partner number range</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160; b)&#160;Assign the ETM-specific grouping to the J3G account group</p>\r\n<p>2. Ensure that the Customizing settings from the Customizing client are&#160;copied to the productive client</p>\r\n<p>3. Ensure that&#160;the business partner assignment is set to required or optional entry in the customizing transaction OBD2- ( As mentioned in steps below)</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; a) Choose customer account group J3G</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;b) Double-click <em>Sales data</em> in the&#160;&#160;<em>Field status</em> group box</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;c) Double-click <em>Partner usage</em> in the <em>Select Group</em> group box</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;d) Set the Business partner assignment as required or an optional entry</p>\r\n<p><strong>&#160;Restrictions:</strong></p>\r\n<p><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-bidi-font-family: Arial;\">&#160;&#160;&#160;&#160; Graphical Planning Board Transaction for ETM: </span><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-bidi-font-family: Arial; mso-fareast-font-family: Calibri; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">As part of the ETM enablement the Graphical Planning Board transaction is as well available via the classic SAP GUI Client. Nevertheless due to technical limitation&#160;&#160;&#160;&#160;&#160; the Graphical Planning Board transaction&#160;is&#160;</span><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-bidi-font-family: Arial; mso-fareast-font-family: Calibri; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">not&#160;availabe as an&#160;SAP Fiori Visual Theme for classic applications and&#160; SAP Fiori Launchpad.</span></p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I041975)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024808)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002326777/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326777/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2350332", "RefComponent": "IS-ADEC-ETM", "RefTitle": "S4TWL - ZDEP ETM CATS Package", "RefUrl": "/notes/2350332"}, {"RefNumber": "2301533", "RefComponent": "IS-ADEC-ETM", "RefTitle": "S4TWL - ETM (Equipment and Tools Management) Tables Obsolete", "RefUrl": "/notes/2301533"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}