Available Migration Objects | SAP Help PortalHomeSAP S/4HANAMigration Objects for SAP S/4HANAMigration Objects for SAP S/4HANA2023 LatestAvailable Versions: 2023 FPS02 (Oct 2024)  2023 Latest  2023 Latest  2022 Latest  2022 FPS02 (May 2023)  2022 FPS01 (Feb 2023)  2022 (Oct 2022)  2021 Latest  2021 FPS02 (May 2022)  2021 FPS01 (Feb 2022)  2021 (Oct 2021)  2020 Latest  2020 FPS02 (May 2021)  2020 FPS01 (Feb 2021)  2020 (Oct 2020)  1909 Latest  1909 FPS02 (May 2020)  1909 FPS01 (Feb 2020)  1909 (Sep 2019) EnglishAvailable Languages: English  German (Deutsch) ProductionStates:TestDraftProductionThis documentSearch Scopes:All SAP productsThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Available Migration Objects

You can use the following table to gain an overview of all migration objects
available for SAP S/4HANA. It’s sorted in alphabetical order by Migration
Object Name. Choose a migration object name to navigate to the
documentation for the migration object.
Tip
If you want to see less or more information, choose Hide/Show Columns, and select the respective checkboxes for the columns you
would like to hide or show.
Tip
Filter the Migration Approach column to only see migration
objects relevant for you. Therefore, choose Filter in the
respective column, and select the required approach, Direct
Transfer or Staging Table.


Hide/Show ColumnsMigration Object NameBusiness Object TypeMigration ApproachCustom Field SupportComponentDownload DataSelect a file type to downloadXLSXCSVSelect the CSV delimiter you want to use for your fileSemi-Colon (;)Comma (,)Pipe Line (|)Caret (^)Which data do you want to download?Download filtered data on this pageDownload all data on all pagesDownload Download Migration Object NameBusiness Object TypeMigration ApproachCustom Field SupportComponent Filter: [No Selection]No values match your filter.Master dataTransactional data Filter: [No Selection]Select AllNo values match your filter.Direct Transfer - AFSDirect Transfer - ERPDirect Transfer - eSPPDirect Transfer - EWMStaging Table Filter: [No Selection]No values match your filter.Not applicableYes Filter: [No Selection]Select AllNo values match your filter.AP-MD-BPCACA-CLCA-DMSCA-FL-SGCA-FL-SRVCA-GTF-CONCA-IAM-MOCCA-JVACM-GFCOCO-OMCO-OM-ABCCRM-S4-SRV-SVOEHS-SUS-CIEHS-SUS-DGEHS-SUS-EMEHS-SUS-FNDEHS-SUS-IMEHS-SUS-PMAEHS-SUS-SDSFIFI-AAFI-APFI-ARFI-LOC-FIFI-RAFIN-FSCM-CFIN-FSCM-CLMFIN-FSCM-TRMFIN-FSCM-TRM-TMFT-ITR-CLSIS-AFS-BDIS-AFS-MMIS-AFS-PP-MANIS-AFS-SDIS-OIL-PRALE-JITLE-JIT-S2CLE-JIT-S2PLOLO-ABLO-BMLO-FSHLO-HULO-HU-BFLO-MD-MMLO-RFM-MD-LSTLO-RFM-MD-MCLO-VCLO-VCHLO-WTYMMMM-IMMM-PURMM-PUR-MPSMM-SRVPA-PA-XX-MIGPLM-ESD-ESCPLM-PLCPLM-WUI-OBJ-ECNPMPM-EQM-SF-MPCPPPP-BDPP-BD-WKCPP-KABPP-MRPPP-PIPP-PI-MD-MRCPP-PI-PORPP-SFCPSQMRE-FXRE-FX-BDSCM-BAS-MDSCM-EWMSDSD-MD-MM-LISSD-SLSSLL-ITR-CLSMigration Object NameBusiness Object TypeMigration ApproachCustom Field SupportComponentNo content displayedAFS - ArticleMaster dataDirect Transfer - AFSNot applicable
IS-AFS-MM
LO-FSH
AFS - Configuration class for material gridMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Dimensional characteristicMaster dataDirect Transfer - AFSNot applicableCA-CLAFS - Distribution curveMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Distribution curve conditionMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Non-dimensional characteristicMaster dataDirect Transfer - AFSNot applicableCA-CLAFS - Planned independent requirement at material levelTransactional dataDirect Transfer - AFSNot applicablePP-MRPAFS - Planned independent requirement at SKU levelTransactional dataDirect Transfer - AFSNot applicablePP-MRPAFS - Purchase grid condition recordMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Quantity distribution profileMaster dataDirect Transfer - AFSNot applicableIS-AFS-PP-MANAFS - Sales grid condition recordMaster dataDirect Transfer - AFSNot applicableIS-AFS-SDAFS - SeasonMaster dataDirect Transfer - AFSNot applicableIS-AFS-SDAFS - Segmentation default valueMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Segmentation strategyMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Segmentation structureMaster dataDirect Transfer - AFSNot applicableIS-AFS-BDAFS - Value-added serviceMaster dataDirect Transfer - AFSNot applicableIS-AFS-SDAVC - Characteristics groupMaster dataStaging TableNot applicableLO-VCHAVC - Configuration profile (restricted)Master dataStaging TableNot applicableLO-VCHAVC - Material variantMaster dataStaging TableNot applicableLO-VCHBankMaster dataStaging TableNot applicableFIBankMaster dataDirect Transfer - ERPNot applicableFIBankMaster dataDirect Transfer - AFSNot applicableFIBatch unique at material and client levelMaster dataStaging TableNot applicableLO-BMBatch unique at material and client levelMaster dataDirect Transfer - ERPNot applicableLO-BMBatch unique at plant levelTransactional dataStaging TableNot applicableLO-BMBatch unique at plant levelMaster dataDirect Transfer - ERPNot applicableLO-BMBatch unique at plant levelMaster dataDirect Transfer - AFSNot applicableLO-BMBusiness partnerMaster dataDirect Transfer - ERPNot applicableAP-MD-BPCarrier profileMaster dataDirect Transfer - EWMNot applicableSCM-EWMCatalog code group and codeMaster dataDirect Transfer - ERPNot applicableQMChange request and activity for SAP Management of ChangeTransactional dataStaging TableNot applicableCA-IAM-MOCCharacteristicMaster dataStaging TableNot applicableCA-CLCharacteristicMaster dataDirect Transfer - ERPNot applicableCA-CLClassMaster dataStaging TableNot applicableCA-CLClassMaster dataDirect Transfer - ERPNot applicableCA-CLClassMaster dataDirect Transfer - AFSNot applicableCA-CLClass hierarchyTransactional dataStaging TableNot applicableCA-CLClass hierarchyMaster dataDirect Transfer - ERPNot applicableLO-MD-MMClass hierarchyMaster dataDirect Transfer - AFSNot applicableLO-MD-MMCO - Activity typeMaster dataStaging TableNot applicableCOCO - Activity typeMaster dataDirect Transfer - ERPNot applicableCOCO - Activity typeMaster dataDirect Transfer - AFSNot applicableCOCO - Activity type groupMaster dataDirect Transfer - ERPNot applicableCOCO - Activity type groupMaster dataDirect Transfer - AFSNot applicableCOCO - Business processMaster dataStaging TableNot applicableCO-OMCO - Business processMaster dataDirect Transfer - ERPNot applicableCO-OM-ABCCO - Business processMaster dataDirect Transfer - AFSNot applicableCO-OM-ABCCO - Business process groupMaster dataDirect Transfer - ERPNot applicableCO-OM-ABCCO - Business process groupMaster dataDirect Transfer - AFSNot applicableCO-OM-ABCCO - Cost centerMaster dataStaging TableNot applicableCOCO - Cost centerMaster dataDirect Transfer - ERPNot applicableCOCO - Cost centerMaster dataDirect Transfer - AFSNot applicableCOCO - Cost center groupMaster dataDirect Transfer - ERPNot applicableCOCO - Cost center groupMaster dataDirect Transfer - AFSNot applicableCOCO - Cost element groupMaster dataDirect Transfer - ERPNot applicableCOCO - Cost element groupMaster dataDirect Transfer - AFSNot applicableCOCO - Cost rateMaster dataStaging TableNot applicableCOCO - Cost rateMaster dataDirect Transfer - ERPNot applicableCO-OMCO - Cost rateMaster dataDirect Transfer - AFSNot applicableCO-OMCO - Internal orderTransactional dataDirect Transfer - ERPNot applicableCOCO - Internal orderTransactional dataDirect Transfer - AFSNot applicableCOCO - Internal order (restricted)Transactional dataStaging TableNot applicableCOCO - Profit centerMaster dataStaging TableNot applicableCOCO - Profit centerMaster dataDirect Transfer - ERPNot applicableCOCO - Profit centerMaster dataDirect Transfer - AFSNot applicableCOCO - Profit center groupMaster dataDirect Transfer - ERPNot applicableCOCO - Profit center groupMaster dataDirect Transfer - AFSNot applicableCOCO - Secondary cost elementMaster dataDirect Transfer - ERPNot applicableCOCO - Secondary cost elementMaster dataDirect Transfer - AFSNot applicableCOCO - Statistical key figureMaster dataStaging TableNot applicableCOCO - Statistical key figureMaster dataDirect Transfer - ERPNot applicableCOCO - Statistical key figureMaster dataDirect Transfer - AFSNot applicableCOCondition contractTransactional dataStaging TableNot applicableLO-ABCondition contractMaster dataDirect Transfer - ERPNot applicableLO-ABCondition record for batch determination (for all areas)Master dataStaging TableNot applicableLO-BMCondition record for pricing (general template)Master dataStaging TableNot applicableSDCondition record for pricing in purchasing (restricted)Master dataStaging TableNot applicableMM-PURCondition record for pricing in sales (restricted)Master dataStaging TableNot applicableSDCondition records (warehouse dependent)Master dataDirect Transfer - EWMNot applicableSCM-EWMCondition records (warehouse independent)Master dataDirect Transfer - EWMNot applicableSCM-EWMConsentTransactional dataStaging TableNot applicableCA-GTF-CONCustomerMaster dataStaging TableYesSDCustomerMaster dataDirect Transfer - ERPNot applicableSDCustomerMaster dataDirect Transfer - AFSNot applicableSDCustomer - extend existing record by credit management dataMaster dataStaging TableNot applicableSDCustomer - extend existing record by multiple addressesMaster dataStaging TableNot applicableSDCustomer - extend existing record by new org levelsMaster dataStaging TableYesSDCustomer - extend existing record by Thailand branch codeMaster dataStaging TableNot applicableSDDG - Assessment for packaged productMaster dataStaging TableNot applicableEHS-SUS-DGDG - Assessment for unpackaged product (content-based)Master dataStaging TableNot applicableEHS-SUS-DGDG - Assessment for unpackaged product (text-based)Master dataStaging TableNot applicableEHS-SUS-DGDG - Technical names for substanceMaster dataStaging TableNot applicableEHS-SUS-DGDocument info recordMaster dataStaging TableNot applicableCA-DMSDocument info record (DMS)Master dataDirect Transfer - ERPNot applicableCA-DMSDocument info record (DMS)Master dataDirect Transfer - AFSNot applicableCA-DMSECM - Change masterMaster dataStaging TableNot applicablePLM-WUI-OBJ-ECNECM - Change masterMaster dataDirect Transfer - ERPNot applicablePLM-PLCECM - Change masterMaster dataDirect Transfer - AFSNot applicablePLM-PLCEFD - Reinf report (Brazil)Transactional dataStaging TableNot applicableFI-LOC-FIEHS - Calculation definitionMaster dataStaging TableNot applicableEHS-SUS-EMEHS - Chemical/Physical propertyMaster dataStaging TableNot applicableEHS-SUS-FNDEHS - Compliance requirementMaster dataStaging TableNot applicableEHS-SUS-CIEHS - Compliance scenarioMaster dataStaging TableNot applicableEHS-SUS-EMEHS - Data classifierMaster dataStaging TableNot applicableEHS-SUS-EMEHS - Data collectionMaster dataStaging TableNot applicableEHS-SUS-EMEHS - Deviation incidentMaster dataStaging TableNot applicableEHS-SUS-IMEHS - IncidentTransactional dataStaging TableNot applicableEHS-SUS-IMEHS - LocationMaster dataStaging TableNot applicableEHS-SUS-FNDEHS - LocationMaster dataDirect Transfer - ERPNot applicableEHS-SUS-FNDEHS - Location aggregationMaster dataStaging TableNot applicableEHS-SUS-EMEHS - Location classifierMaster dataStaging TableNot applicableEHS-SUS-CIEHS - Location hierarchyMaster dataStaging TableNot applicableEHS-SUS-FNDEHS - Location hierarchyMaster dataDirect Transfer - ERPNot applicableEHS-SUS-FNDEHS - TaskTransactional dataStaging TableNot applicableEHS-SUS-EMeSPP - Bill of Distribution (BoD)Master dataDirect Transfer - eSPPNot applicableSCM-BAS-MDeSPP - LocationMaster dataDirect Transfer - eSPPNot applicableSCM-BAS-MDeSPP - Location ProductMaster dataDirect Transfer - eSPPNot applicableSCM-BAS-MDeSPP - MRP AreaMaster dataDirect Transfer - eSPPNot applicableSCM-BAS-MDeSPP - ProductMaster dataDirect Transfer - eSPPNot applicableSCM-BAS-MDeSPP - Product BoD AssignmentMaster dataDirect Transfer - eSPPNot applicableSCM-BAS-MDeSPP - Transportation LaneMaster dataDirect Transfer - eSPPNot applicableSCM-BAS-MDExchange rateMaster dataStaging TableNot applicableFIExchange rateMaster dataDirect Transfer - ERPNot applicableFIExchange rateMaster dataDirect Transfer - AFSNot applicableFIFI - Accounts payable open itemTransactional dataStaging TableYesFI-APFI - Accounts payable open item (classic G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - Accounts payable open item (classic G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - Accounts payable open item (new G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - Accounts payable open item (new G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - Accounts receivable open itemTransactional dataStaging TableYesFI-ARFI - Accounts receivable open item (classic GL)Transactional dataDirect Transfer - ERPNot applicableFIFI - Accounts receivable open item (classic GL)Transactional dataDirect Transfer - AFSNot applicableFIFI - Accounts receivable open item (new G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - Accounts receivable open item (new G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - Bank accountMaster dataDirect Transfer - ERPNot applicableFIFI - Bank accountMaster dataDirect Transfer - AFSNot applicableFIFI - Bank account balanceTransactional dataStaging TableNot applicableFIN-FSCM-CLMFI - Cash memo recordTransactional dataStaging TableNot applicableFIN-FSCM-CLMFI - G/L accountMaster dataStaging TableNot applicableFIFI - G/L accountMaster dataDirect Transfer - ERPNot applicableFIFI - G/L accountMaster dataDirect Transfer - AFSNot applicableFIFI - G/L account - extend existing record by new org levelsMaster dataStaging TableNot applicableFIFI - G/L account balance (classic G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - G/L account balance (classic G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - G/L account balance (new G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - G/L account balance (new G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - G/L account balance and open/line itemTransactional dataStaging TableYesFIFI - G/L open item (classic G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - G/L open item (classic G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - G/L open item (new G/L)Transactional dataDirect Transfer - ERPNot applicableFIFI - G/L open item (new G/L)Transactional dataDirect Transfer - AFSNot applicableFIFI - Historical balanceTransactional dataStaging TableNot applicableFIFI - House bankMaster dataDirect Transfer - ERPNot applicableFIFI - House bankMaster dataDirect Transfer - AFSNot applicableFIFI - Ledger group-specific open itemTransactional dataDirect Transfer - ERPNot applicableFIFI - Ledger group-specific open itemTransactional dataDirect Transfer - AFSNot applicableFIFI - SEPA mandateMaster dataStaging TableNot applicableFIFI - SEPA mandateMaster dataDirect Transfer - ERPNot applicableFI-ARFI - SEPA mandateMaster dataDirect Transfer - AFSNot applicableFI-ARFI-CA - Direct debit mandateMaster dataStaging TableNot applicableFIField Logistics - Container (restricted)Master dataStaging TableYesCA-FL-SGField logistics - Supplier itemTransactional dataStaging TableYesCA-FL-SRVFixed asset (incl. balances and transactions)Master dataStaging TableNot applicableFI-AAFixed asset (including balance)Transactional dataDirect Transfer - ERPNot applicableFI-AAFixed asset (including balance)Transactional dataDirect Transfer - AFSNot applicableFI-AAFixed asset - Master dataMaster dataStaging TableYesFI-AAFixed asset - PostingsTransactional dataStaging TableNot applicableFI-AAFixed asset - Usage objectMaster dataStaging TableNot applicableFI-AAFSCM - Credit management roleMaster dataDirect Transfer - ERPNot applicableFIN-FSCM-CHCM - EmployeeMaster dataStaging TableNot applicablePA-PA-XX-MIGHCM - EmployeeMaster dataDirect Transfer - ERPNot applicablePA-PA-XX-MIGHCM - ObjectMaster dataStaging TableNot applicablePA-PA-XX-MIGHCM - ObjectMaster dataDirect Transfer - ERPNot applicablePA-PA-XX-MIGHCM - RelationshipMaster dataStaging TableNot applicablePA-PA-XX-MIGHCM - RelationshipMaster dataDirect Transfer - ERPNot applicablePA-PA-XX-MIGJIT - Communication groupMaster dataStaging TableNot applicableLE-JIT-S2PJIT - Control cycleMaster dataStaging TableYesLE-JIT-S2PJIT - CustomerMaster dataStaging TableNot applicableLE-JIT-S2CJIT - CustomerMaster dataDirect Transfer - ERPNot applicableLE-JITJIT - Packing group specificationMaster dataStaging TableNot applicableLE-JIT-S2CJIT - Supply controlMaster dataStaging TableNot applicableLE-JIT-S2CJVA - Cost center suspenseMaster dataStaging TableNot applicableCA-JVAJVA - Cost center suspenseMaster dataDirect Transfer - ERPNot applicableCA-JVAJVA - Joint operating agreementMaster dataStaging TableNot applicableCA-JVAJVA - Joint operating agreement (step 1)Master dataDirect Transfer - ERPNot applicableCA-JVAJVA - Joint operating agreement (step 2)Master dataDirect Transfer - ERPNot applicableCA-JVAJVA - Joint venture master dataMaster dataStaging TableNot applicableCA-JVAJVA - Joint venture master data (step 1)Master dataDirect Transfer - ERPNot applicableCA-JVAJVA - Joint venture master data (step 2)Master dataDirect Transfer - ERPNot applicableCA-JVAJVA - Joint venture partnerMaster dataStaging TableNot applicableCA-JVAJVA - Joint venture partnerMaster dataDirect Transfer - ERPNot applicableCA-JVAJVA - Order suspenseMaster dataDirect Transfer - ERPNot applicableCA-JVAJVA - Project suspenseMaster dataStaging TableNot applicableCA-JVAJVA - Project suspenseMaster dataDirect Transfer - ERPNot applicableCA-JVAJVA - Venture suspenseMaster dataStaging TableNot applicableCA-JVAJVA - Venture suspenseMaster dataDirect Transfer - ERPNot applicableCA-JVALegal documentTransactional dataStaging TableNot applicableCM-GFLegal transactionTransactional dataStaging TableNot applicableCM-GFLO - Handling unitTransactional dataStaging TableNot applicableLO-HU-BFLO - Handling unitMaster dataDirect Transfer - ERPNot applicableLO-HUMaster recipeMaster dataStaging TableNot applicablePP-PIMaterial - Forecast planningMaster dataStaging TableNot applicableMMMaterial BOMMaster dataStaging TableNot applicablePP-BDMaterial flow systemMaster dataDirect Transfer - EWMNot applicableSCM-EWMMaterial inventory balanceTransactional dataStaging TableNot applicableMM-IMMaterial listing and exclusionMaster dataStaging TableNot applicableSDMiscellaneous EWM master data settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMMM - Material commodityMaster dataDirect Transfer - ERPNot applicableLO-MD-MMMM - Material consumptionMaster dataDirect Transfer - ERPNot applicableMMMM - Material inventory balanceTransactional dataDirect Transfer - ERPNot applicableMM-IMMM - Material inventory balanceTransactional dataDirect Transfer - AFSNot applicableMM-IMMM - Model product specificationMaster dataStaging TableNot applicableMM-PUR-MPSMM - Purchase contractTransactional dataStaging TableNot applicableMM-PURMM - Purchase contractTransactional dataDirect Transfer - ERPNot applicableMM-PURMM - Purchase contractTransactional dataDirect Transfer - AFSNot applicableMM-PURMM - Purchase orderTransactional dataDirect Transfer - ERPNot applicableMM-PURMM - Purchase orderTransactional dataDirect Transfer - AFSNot applicableMM-PURMM - Purchase order (only open PO)Transactional dataStaging TableYesMM-PURMM - Purchase requisition (only open PR)Transactional dataDirect Transfer - ERPNot applicableMM-PURMM - Purchase scheduling agreementTransactional dataStaging TableNot applicableMM-PURMM - Purchase scheduling agreementTransactional dataDirect Transfer - ERPNot applicableMM-PURMM - Purchase scheduling agreementTransactional dataDirect Transfer - AFSNot applicableMM-PURMM - Purchasing info recordMaster dataDirect Transfer - ERPNot applicableMM-PURMM - Purchasing info recordMaster dataDirect Transfer - AFSNot applicableMM-PURMM - Purchasing info record with conditionsMaster dataStaging TableNot applicableMM-PURMM - Purchasing info record- extend existing recordMaster dataStaging TableNot applicableMM-PURMM - Quota arrangementTransactional dataDirect Transfer - ERPNot applicableMM-PURMM - Service entry sheetTransactional dataDirect Transfer - ERPNot applicableMMMM - Service masterMaster dataDirect Transfer - ERPNot applicableMM-SRVMM - Source listTransactional dataStaging TableNot applicableMM-PURMM - Source listMaster dataDirect Transfer - ERPNot applicableMM-PURMM - Source listMaster dataDirect Transfer - AFSNot applicableMM-PURObject classification (general template)Master dataDirect Transfer - ERPNot applicableCAObject classification (general template)Master dataDirect Transfer - AFSNot applicableCAObject classification - Code group (QPGR)Master dataStaging TableNot applicableCA-CLObject classification - Document (DRAW)Master dataStaging TableNot applicableCA-CLObject classification - General templateMaster dataStaging TableNot applicableCA-CLObject classification - Inspection characteristic (QPMK)Master dataStaging TableNot applicableCA-CLObject classification - Inspection method (QMTB)Master dataStaging TableNot applicableCA-CLObject classification - Material (MARA)Master dataStaging TableNot applicableCA-CLObject classification - Purchasing info record (EINA)Master dataStaging TableNot applicableCA-CLObject classification - Selected set (QPAM)Master dataStaging TableNot applicableCA-CLPC - Analytical compositionMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Confidential business informationMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Material-based compositionMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Physical-chemical propertyMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Polymer compositionMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Product compliance infoMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Raw material compliance infoMaster dataStaging TableNot applicableEHS-SUS-FNDPC - Safety-related propertyMaster dataStaging TableNot applicableEHS-SUS-FNDPC - SubstanceMaster dataStaging TableNot applicableEHS-SUS-FNDPhysical inventory settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMPM - EquipmentMaster dataStaging TableYesPMPM - EquipmentMaster dataDirect Transfer - ERPNot applicablePMPM - EquipmentMaster dataDirect Transfer - AFSNot applicablePMPM - Equipment BOMMaster dataStaging TableNot applicablePMPM - Equipment BOMMaster dataDirect Transfer - ERPNot applicablePMPM - Equipment task listMaster dataStaging TableNot applicablePMPM - Equipment task listMaster dataDirect Transfer - ERPNot applicablePMPM - Functional locationMaster dataStaging TableYesPMPM - Functional locationMaster dataDirect Transfer - ERPNot applicablePMPM - Functional location BOMMaster dataStaging TableNot applicablePMPM - Functional location BOMMaster dataDirect Transfer - ERPNot applicablePMPM - Functional location task listMaster dataStaging TableNot applicablePMPM - Functional location task listMaster dataDirect Transfer - ERPNot applicablePMPM - General maintenance task listMaster dataStaging TableNot applicablePMPM - General maintenance task listMaster dataDirect Transfer - ERPNot applicablePMPM - Maintenance itemMaster dataStaging TableYesPMPM - Maintenance itemMaster dataDirect Transfer - ERPNot applicablePMPM - Maintenance notificationMaster dataStaging TableYesPMPM - Maintenance notificationTransactional dataDirect Transfer - ERPNot applicablePMPM - Maintenance orderTransactional dataStaging TableYesPMPM - Maintenance orderTransactional dataDirect Transfer - ERPNot applicablePMPM - Maintenance planMaster dataStaging TableYesPMPM - Maintenance planMaster dataDirect Transfer - ERPNot applicablePMPM - Measurement documentMaster dataStaging TableNot applicablePMPM - Measurement documentMaster dataDirect Transfer - ERPNot applicablePM-EQM-SF-MPCPM - Measuring pointMaster dataStaging TableNot applicablePMPM - Measuring pointMaster dataDirect Transfer - ERPNot applicablePMPMA - Customer compliance assessmentMaster dataStaging TableNot applicableEHS-SUS-PMAPMA - Simple compliance assessmentMaster dataStaging TableNot applicableEHS-SUS-PMAPMA - Supplier information assessmentMaster dataStaging TableNot applicableEHS-SUS-PMAPP - Master RecipeMaster dataDirect Transfer - ERPNot applicablePP-PI-MD-MRCPP - Material BOMMaster dataDirect Transfer - ERPNot applicablePP-BDPP - Material BOMMaster dataDirect Transfer - AFSNot applicablePP-BDPP - Material MRP assignmentMaster dataDirect Transfer - ERPNot applicablePP-MRPPP - Planned independent requirementTransactional dataDirect Transfer - ERPNot applicablePP-MRPPP - Planned independent requirementTransactional dataDirect Transfer - AFSNot applicablePP-MRPPP - Process order (only open PO)Transactional dataStaging TableNot applicablePP-PI-PORPP - Production orderTransactional dataDirect Transfer - ERPNot applicablePPPP - Production orderTransactional dataDirect Transfer - AFSNot applicablePPPP - Production order (only open PO)Transactional dataStaging TableNot applicablePP-SFCPP - Production resource/toolMaster dataDirect Transfer - ERPNot applicablePPPP - Production versionMaster dataDirect Transfer - ERPNot applicablePPPP - Production versionMaster dataDirect Transfer - AFSNot applicablePPPP - RoutingMaster dataDirect Transfer - ERPNot applicablePPPP - RoutingMaster dataDirect Transfer - AFSNot applicablePPPP - Sales order BOMMaster dataDirect Transfer - ERPNot applicablePP-BDPP - Sales order BOMMaster dataDirect Transfer - AFSNot applicablePP-BDPP - Work breakdown structure BOMMaster dataDirect Transfer - ERPNot applicablePPPP - Work centerMaster dataDirect Transfer - ERPNot applicablePMPP - Work centerMaster dataDirect Transfer - AFSNot applicablePMPP - Work center hierarchyMaster dataDirect Transfer - ERPNot applicablePP-BD-WKCPP-KAB - Kanban control cycleMaster dataStaging TableNot applicablePP-KABPP-KAB - Production supply areaMaster dataStaging TableYesPP-KABPRA - Account entry controlMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Allocation cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Check input process ruleMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Check input translationMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Chemical analysis dataMaster dataStaging TableNot applicableIS-OIL-PRAPRA - ContractMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Contract volumeTransactional dataStaging TableNot applicableIS-OIL-PRAPRA - Delivery networkMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Delivery network allocation profileMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Delivery network downstream nodeMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Division order interestMaster dataStaging TableNot applicableIS-OIL-PRAPRA - DOI accountingMaster dataStaging TableNot applicableIS-OIL-PRAPRA - DOI MP WC cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - extend existing customer by PRA dataMaster dataStaging TableNot applicableIS-OIL-PRAPRA - extend existing supplier by PRA dataMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Joint ventureMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Journal entry open balanceTransactional dataStaging TableNot applicableIS-OIL-PRAPRA - Marketing group assignmentMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Measurement pointMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Measurement point allocation profileMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Measurement point volumeTransactional dataStaging TableNot applicableIS-OIL-PRAPRA - MP-WC to contract cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - MP/WC transporter cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Ownership leaseMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Remitter to DOI cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Settlement diversityMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Settlement statement DOI cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - State tax rateMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Tax calculation dataMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Unit venture tractMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Valuation cross referenceMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Valuation formulaMaster dataStaging TableNot applicableIS-OIL-PRAPRA - WC/DN theoretical calculation methodMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Well and well completionMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Well completion tax classificationMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Well completion testMaster dataStaging TableNot applicableIS-OIL-PRAPRA - Well completion volumeTransactional dataStaging TableNot applicableIS-OIL-PRAPricing condition (purchasing and sales)Master dataDirect Transfer - ERPNot applicable
SD
MM-PUR
Pricing condition (purchasing and sales)Master dataDirect Transfer - AFSNot applicable
SD
MM-PUR
ProductMaster dataStaging TableYesLOProductMaster dataDirect Transfer - ERPNot applicableLOProduct - extend existing record by new org levelsMaster dataStaging TableYesLOProduct - extend existing record with long textMaster dataStaging TableNot applicableLOProduct classification (legal control)Master dataDirect Transfer - ERPNot applicableSLL-ITR-CLSProduct classification - Commodity codeMaster dataStaging TableNot applicableFT-ITR-CLSProduct classification - Legal controlMaster dataStaging TableNot applicableFT-ITR-CLSProduct consumptionMaster dataStaging TableNot applicableMMProduction supply areaMaster dataDirect Transfer - EWMNot applicableSCM-EWMProduction versionMaster dataStaging TableNot applicablePPPS - NetworkTransactional dataStaging TableNot applicablePSPS - NetworkTransactional dataDirect Transfer - ERPNot applicablePSPS - ProjectTransactional dataStaging TableNot applicablePSPS - ProjectTransactional dataDirect Transfer - ERPNot applicablePSPS - WBS elementTransactional dataStaging TableNot applicablePSQM - Inspection methodMaster dataStaging TableNot applicableQMQM - Inspection methodMaster dataDirect Transfer - ERPNot applicableQMQM - Inspection planMaster dataStaging TableNot applicableQMQM - Inspection planMaster dataDirect Transfer - ERPNot applicableQMQM - Master inspection characteristicMaster dataStaging TableNot applicableQMQM - Master inspection characteristicMaster dataDirect Transfer - ERPNot applicableQMQM - Quality info recordMaster dataStaging TableNot applicableQMQM - Quality info recordMaster dataDirect Transfer - ERPNot applicableQMQM - Selected setMaster dataStaging TableNot applicableQMQM - Selected setMaster dataDirect Transfer - ERPNot applicableQMQM - Selected set codeMaster dataStaging TableNot applicableQMQM - Selected set codeMaster dataDirect Transfer - ERPNot applicableQMQM/PM - Catalog code group/codeMaster dataStaging TableNot applicableQMQuality inspection rulesMaster dataDirect Transfer - EWMNot applicableSCM-EWMQuality inspection rules (cross-warehouse)Master dataDirect Transfer - EWMNot applicableSCM-EWMRE-FX - Architectural objectMaster dataDirect Transfer - ERPNot applicableRE-FX-BDRE-FX - BuildingMaster dataDirect Transfer - ERPNot applicableRE-FX-BDRE-FX - Business entityMaster dataDirect Transfer - ERPNot applicableRE-FXRE-FX - Business partner roleMaster dataDirect Transfer - ERPNot applicableAP-MD-BPRE-FX - LandMaster dataDirect Transfer - ERPNot applicableRE-FX-BDRE-FX - Real estate contractTransactional dataDirect Transfer - ERPNot applicableRE-FXRE-FX - Rental objectMaster dataDirect Transfer - ERPNot applicableRE-FXReal estate - Occupancy groupMaster dataStaging TableYesRE-FXReal estate - Usage enablement groupMaster dataStaging TableYesRE-FXReal estate contractTransactional dataStaging TableYesRE-FXResource and user maintenanceMaster dataDirect Transfer - EWMNot applicableSCM-EWMResource execution settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMResource group and queue settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMRevenue accounting contractTransactional dataStaging TableNot applicableFI-RARFID settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMRFM - Assortment moduleMaster dataStaging TableNot applicableLO-RFM-MD-LSTRFM - MCHN Characteristic value restrictionMaster dataStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise categoryMaster dataStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category characteristic value restrictionMaster dataStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category hierarchy nodeMaster dataStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category hierarchy node assignmentMaster dataStaging TableNot applicableLO-RFM-MD-MCRFM - Merchandise category reference articleMaster dataStaging TableNot applicableLO-RFM-MD-MCRFM - Product assignment to distribution centerMaster dataStaging TableNot applicableLO-RFM-MD-LSTRouteMaster dataDirect Transfer - EWMNot applicableSCM-EWMRoutingMaster dataStaging TableNot applicablePPSample-drawing procedureMaster dataDirect Transfer - EWMNot applicableSCM-EWMSD - Condition record for free goodsMaster dataStaging TableNot applicableSDSD - Condition Record for Material determinationMaster dataStaging TableNot applicableSDSD - Customer MaterialMaster dataStaging TableYesSDSD - Customer materialMaster dataDirect Transfer - ERPNot applicableSDSD - Customer quotationTransactional dataDirect Transfer - ERPNot applicableSDSD - Material listing and exclusionMaster dataDirect Transfer - ERPNot applicableSD-MD-MM-LISSD - Sales contractTransactional dataStaging TableNot applicableSDSD - Sales contractTransactional dataDirect Transfer - ERPNot applicableSDSD - Sales contractTransactional dataDirect Transfer - AFSNot applicableSDSD - Sales inquiryTransactional dataDirect Transfer - ERPNot applicableSDSD - Sales orderTransactional dataDirect Transfer - AFSNot applicableSDSD - Sales order (only open SO)Transactional dataStaging TableYesSDSD - Sales order (only open SO)Transactional dataDirect Transfer - ERPNot applicableSDSD - Sales scheduling agreementTransactional dataStaging TableNot applicableSD-SLSSDS - Assessment for unpackaged productMaster dataStaging TableNot applicableEHS-SUS-SDSSDS - Shipment historyTransactional dataStaging TableNot applicableEHS-SUS-SDSService order (only open SRVO)Transactional dataStaging TableYesCRM-S4-SRV-SVOService productMaster dataStaging TableYesLOShipping and receiving settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMSoftware/Hardware constraintTransactional dataStaging TableNot applicablePLM-ESD-ESCSupplierMaster dataStaging TableYesMMSupplierMaster dataDirect Transfer - ERPNot applicableMMSupplierMaster dataDirect Transfer - AFSNot applicableMMSupplier - extend existing record by multiple addressesMaster dataStaging TableNot applicableMMSupplier - extend existing record by new org levelsMaster dataStaging TableYesMMSupplier - extend existing record by Thailand branch codeMaster dataStaging TableNot applicableMMTRM - Additional flowTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Bank guaranteeTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Business partner roleMaster dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Cap/FloorTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Commercial paperTransactional dataStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Commercial paperTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Condition detailTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Deposit at noticeTransactional dataStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Deposit at noticeTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - FacilityTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Fixed term depositTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange collar - contractTransactional dataStaging TableNot applicableFIN-FSCM-TRMTRM - Foreign exchange collar contractTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange spot/forward contractTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange spot/forward transaction - contractTransactional dataStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange swap - contractTransactional dataStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Foreign exchange swap contractTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - FX optionTransactional dataStaging TableNot applicableFIN-FSCM-TRM-TMTRM - FX optionTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Interest rate instrumentTransactional dataStaging TableNot applicableFIN-FSCM-TRMTRM - Interest rate instrumentTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Interest rate swapTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Investment certificateTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Letter of creditTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Main flow detailTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Payment detailTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Position valueTransactional dataStaging TableNot applicableFIN-FSCM-TRM-TMTRM - Security bondTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - Security classTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMTRM - StockTransactional dataDirect Transfer - ERPNot applicableFIN-FSCM-TRM-TMVC - Assign global dependency to characteristicMaster dataDirect Transfer - ERPNot applicableCA-CLVC - Assign global dependency to characteristicMaster dataDirect Transfer - AFSNot applicableCA-CLVC - Assign global dependency to classMaster dataDirect Transfer - ERPNot applicableCA-CLVC - Assign global dependency to classMaster dataDirect Transfer - AFSNot applicableCA-CLVC - Configuration profileMaster dataDirect Transfer - ERPNot applicableLO-VCVC - Interface design (characteristic group)Master dataDirect Transfer - ERPNot applicableLO-VCVC - Material variantMaster dataDirect Transfer - ERPNot applicableLO-VCVC - Object dependency and dependency netMaster dataDirect Transfer - ERPNot applicableLO-VCVC - Object dependency and dependency netMaster dataDirect Transfer - AFSNot applicableLO-VCVC - Variant table entryTransactional dataDirect Transfer - ERPNot applicableLO-VCVC - Variant table structureMaster dataDirect Transfer - ERPNot applicableLO-VCWarehouse application log settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse fixed bin assignmentMaster dataStaging TableNot applicableSCM-EWMWarehouse fixed bin assignmentsMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse handling unitMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse productMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse stockMaster dataStaging TableNot applicableSCM-EWMWarehouse stockMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse storage binMaster dataStaging TableNot applicableSCM-EWMWarehouse storage binMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse storage bin sortingMaster dataStaging TableNot applicableSCM-EWMWarehouse storage bin sortingMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarehouse storage bin user statusMaster dataDirect Transfer - EWMNot applicableSCM-EWMWarranty claimTransactional dataStaging TableYesLO-WTYWave templatesMaster dataDirect Transfer - EWMNot applicableSCM-EWMWork center dependent settingsMaster dataDirect Transfer - EWMNot applicableSCM-EWMWork center determinationsMaster dataDirect Transfer - EWMNot applicableSCM-EWMWork center/ResourceMaster dataStaging TableNot applicablePPZoneMaster dataDirect Transfer - EWMNot applicableSCM-EWMZones hierarchyMaster dataDirect Transfer - EWMNot applicableSCM-EWMPage Size: 25102550100AllShowing 25 of 486Previous123...20Next
Caution
Data protection legislation may
require that personal data is deleted once the data has served its originally
defined purpose and is also no longer subject to additional legal data retention
requirements. If data protection legislation is applicable in your case, then
migrating personal data, which should have been deleted could be interpreted as
the processing of personal data without any legally justified
purpose.Note
For some migration objects, there are additions to the
migration object name. These additions include "restricted" and "deprecated".
Restricted means that not all fields and structures of the relevant business
processes are covered by this migration object, deprecated means that there’s a
newer version of this migration object available. Deprecated objects will be deleted
after a couple of releases. Make sure you always read the migration object
documentation for these objects carefully. Also see SAP Note
2698032  for more details on deprecated migration
objects.Note
Migration objects are built for initial migration of your data to your SAP S/4HANA system. This
means that you can create data with a migration object, but you can't change or
update existing data with it. Note
The predelivered data migration objects are built
for SAP S/4HANA Cloud and the respective SAP Best Practices Content. You can use
these objects also for SAP S/4HANA and the respective SAP Best Practices Content.
Using SAP S/4HANA, the delivered standard migration objects are templates that can
be enhanced according to your needs. The enhancement is only limited by the
functionality of the tool and the capabilities of the underlying API to load the
data. Starting with SAP S/4HANA 1610 FPS2, you can enhance customer projects based
on these delivered standard migration objects or you can create your own objects
using the SAP S/4HANA migration object modeler (transaction
LTMOM). For further information, please
see SAP Note 2481235 .
Further Information
Use these SAP Help Portal aliases to access the following sections of our
product assistance: 

Type this in your browser...
To jump to...



http://help.sap.com/S4_OP_MO
this very topic: Available Migration Objects


http://help.sap.com/S4_OP_DM
the landing page for data migration


http://help.sap.com/S4_OP_DM_STATUS
 the entry topic: Data Migration Status



If you want to view information about the differences between the current
release and the previous release, see SAP S/4HANA – Release Comparison of Migration Object
Templates (for the Staging Table
migration approach only, and for customers and partners only).

If you’re using the Fixed asset (incl. balances and transactions)
migration object, we provide background information on asset classes and available fields per asset class
here (for customers and partners only).
For information regarding mapping of unit of measures, see SAP Knowledge Base
Article 2907822 .

CommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

